<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الرئيسية - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/">
            <i class="fas fa-robot"></i> CS Bot - لوحة التحكم
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="/dashboard"><i class="fas fa-home"></i> الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/users"><i class="fas fa-users"></i> المستخدمين</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/servers"><i class="fas fa-server"></i> السيرفرات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard/premium"><i class="fas fa-crown"></i> البريميوم</a>
                </li>
            </ul>
            <div class="d-flex">
                <span class="navbar-text me-3">
                    <i class="fas fa-user-shield"></i> مرحباً، المطور
                </span>
                <a href="/auth/logout" class="btn btn-outline-light">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </div>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="sidebar bg-dark rounded p-3">
                <h6 class="text-light mb-3"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h6>
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="fas fa-chart-line"></i> الإحصائيات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/users">
                            <i class="fas fa-users"></i> إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/servers">
                            <i class="fas fa-server"></i> إدارة السيرفرات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/premium">
                            <i class="fas fa-crown"></i> إدارة البريميوم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/settings">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="col-md-9 col-lg-10">
            <!-- Welcome Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 bg-gradient text-white">
                        <div class="card-body">
                            <h2 class="card-title">
                                <i class="fas fa-crown"></i> مرحباً بك في لوحة التحكم الأسطورية
                            </h2>
                            <p class="card-text">إدارة شاملة لبوت CS مع جميع المميزات المتقدمة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-server fa-2x text-primary mb-2"></i>
                            <h4 class="card-title" id="serverCount">
                                <i class="fas fa-spinner fa-spin"></i>
                            </h4>
                            <p class="card-text text-muted">السيرفرات</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-success mb-2"></i>
                            <h4 class="card-title" id="userCount">
                                <i class="fas fa-spinner fa-spin"></i>
                            </h4>
                            <p class="card-text text-muted">المستخدمين</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                            <h4 class="card-title" id="premiumCount">
                                <i class="fas fa-spinner fa-spin"></i>
                            </h4>
                            <p class="card-text text-muted">أعضاء البريميوم</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-terminal fa-2x text-info mb-2"></i>
                            <h4 class="card-title" id="commandCount">
                                <i class="fas fa-spinner fa-spin"></i>
                            </h4>
                            <p class="card-text text-muted">الأوامر المنفذة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="/dashboard/users" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-users"></i> إدارة المستخدمين
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="/dashboard/premium" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-crown"></i> إدارة البريميوم
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="/dashboard/servers" class="btn btn-outline-success w-100">
                                        <i class="fas fa-server"></i> إدارة السيرفرات
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <button class="btn btn-outline-danger w-100" onclick="restartBot()">
                                        <i class="fas fa-redo"></i> إعادة تشغيل البوت
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> النشاط الأخير</h6>
                        </div>
                        <div class="card-body">
                            <div id="recentActivity">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> تنبيهات النظام</h6>
                        </div>
                        <div class="card-body">
                            <div id="systemAlerts">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> البوت يعمل بشكل طبيعي
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> آخر تحديث: الآن
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تحديث الإحصائيات
function updateStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('serverCount').textContent = data.servers || '0';
            document.getElementById('userCount').textContent = data.users || '0';
            document.getElementById('premiumCount').textContent = data.premiumUsers || '0';
            document.getElementById('commandCount').textContent = data.commands || '0';
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
        });
}

// إعادة تشغيل البوت
function restartBot() {
    if (confirm('هل أنت متأكد من إعادة تشغيل البوت؟')) {
        fetch('/api/restart', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                alert('تم إرسال أمر إعادة التشغيل');
            })
            .catch(error => {
                alert('حدث خطأ في إعادة التشغيل');
            });
    }
}

// تحديث فوري
updateStats();

// تحديث كل 30 ثانية
setInterval(updateStats, 30000);
</script>

</body>
</html>
