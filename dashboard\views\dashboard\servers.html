<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيرفرات - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-robot"></i> CS Bot - إدارة السيرفرات
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-server"></i> إدارة السيرفرات
                    </h2>
                    <p class="card-text">إدارة شاملة لجميع السيرفرات التي يتواجد فيها البوت</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="totalServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي السيرفرات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="totalMembers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي الأعضاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-hashtag fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="totalChannels">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي القنوات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="ownedServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفرات مملوكة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Servers List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة السيرفرات
                        <span class="badge bg-light text-dark ms-2" id="serverCountBadge">0</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الأيقونة</th>
                                    <th>اسم السيرفر</th>
                                    <th>المعرف</th>
                                    <th>الأعضاء</th>
                                    <th>القنوات</th>
                                    <th>المالك</th>
                                    <th>تاريخ الانضمام</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="serversTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p class="mt-2">جاري تحميل السيرفرات...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Server Details Modal -->
<div class="modal fade" id="serverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-server"></i> تفاصيل السيرفر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverModalBody">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-danger" onclick="leaveServer()">
                    <i class="fas fa-sign-out-alt"></i> مغادرة السيرفر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let allServers = [];
let currentServerId = null;

// تحميل السيرفرات
async function loadServers() {
    try {
        const response = await fetch('/api/servers');
        const data = await response.json();
        
        allServers = data.servers || [];
        displayServers(allServers);
        updateStats(allServers);
        
        document.getElementById('serverCountBadge').textContent = allServers.length;
    } catch (error) {
        console.error('خطأ في تحميل السيرفرات:', error);
        document.getElementById('serversTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                    <p class="mt-2">حدث خطأ في تحميل السيرفرات</p>
                </td>
            </tr>
        `;
    }
}

// عرض السيرفرات
function displayServers(servers) {
    const tbody = document.getElementById('serversTableBody');
    
    if (servers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="fas fa-server fa-2x text-muted"></i>
                    <p class="mt-2">لا توجد سيرفرات</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = servers.map(server => `
        <tr>
            <td>
                <img src="${server.icon || '/img/default-server.png'}" 
                     alt="${server.name}" 
                     class="rounded-circle" 
                     width="40" height="40"
                     onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            </td>
            <td>
                <strong>${server.name}</strong>
                ${server.owner ? '<span class="badge bg-warning ms-1"><i class="fas fa-crown"></i></span>' : ''}
            </td>
            <td><code>${server.id}</code></td>
            <td>
                <span class="badge bg-success">
                    <i class="fas fa-users"></i> ${server.memberCount || 0}
                </span>
            </td>
            <td>
                <span class="badge bg-info">
                    <i class="fas fa-hashtag"></i> ${server.channelCount || 0}
                </span>
            </td>
            <td>
                <span class="badge ${server.owner ? 'bg-warning' : 'bg-secondary'}">
                    ${server.owner ? 'نعم' : 'لا'}
                </span>
            </td>
            <td>
                <small class="text-muted">
                    ${server.joinedAt ? new Date(server.joinedAt).toLocaleDateString('ar-SA') : 'غير معروف'}
                </small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-primary" onclick="viewServerDetails('${server.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-success" onclick="manageServer('${server.id}')">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn btn-danger" onclick="confirmLeaveServer('${server.id}', '${server.name}')">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تحديث الإحصائيات
function updateStats(servers) {
    const totalMembers = servers.reduce((sum, server) => sum + (server.memberCount || 0), 0);
    const totalChannels = servers.reduce((sum, server) => sum + (server.channelCount || 0), 0);
    const ownedServers = servers.filter(server => server.owner).length;
    
    document.getElementById('totalServers').textContent = servers.length;
    document.getElementById('totalMembers').textContent = totalMembers.toLocaleString();
    document.getElementById('totalChannels').textContent = totalChannels.toLocaleString();
    document.getElementById('ownedServers').textContent = ownedServers;
}

// عرض تفاصيل السيرفر
async function viewServerDetails(serverId) {
    try {
        const response = await fetch(`/api/server/${serverId}`);
        const server = await response.json();
        
        if (response.ok) {
            document.getElementById('serverModalBody').innerHTML = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        <img src="${server.icon || '/img/default-server.png'}" 
                             alt="${server.name}" 
                             class="rounded-circle mb-3" 
                             width="100" height="100">
                        <h5>${server.name}</h5>
                        <p class="text-muted">${server.id}</p>
                    </div>
                    <div class="col-md-8">
                        <h6>معلومات السيرفر:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>عدد الأعضاء:</span>
                                <span class="badge bg-success">${server.memberCount}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>عدد القنوات:</span>
                                <span class="badge bg-info">${server.channelCount}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>عدد الرتب:</span>
                                <span class="badge bg-warning">${server.roleCount}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>مالك السيرفر:</span>
                                <span class="badge ${server.owner ? 'bg-success' : 'bg-secondary'}">${server.owner ? 'نعم' : 'لا'}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            `;
            
            currentServerId = serverId;
            new bootstrap.Modal(document.getElementById('serverModal')).show();
        } else {
            alert('خطأ في تحميل تفاصيل السيرفر');
        }
    } catch (error) {
        alert('حدث خطأ في تحميل التفاصيل');
    }
}

// إدارة السيرفر
function manageServer(serverId) {
    window.open(`/dashboard/server/${serverId}`, '_blank');
}

// تأكيد مغادرة السيرفر
function confirmLeaveServer(serverId, serverName) {
    if (confirm(`هل أنت متأكد من مغادرة السيرفر "${serverName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        currentServerId = serverId;
        leaveServer();
    }
}

// مغادرة السيرفر
async function leaveServer() {
    if (!currentServerId) return;
    
    try {
        const response = await fetch(`/api/server/${currentServerId}/leave`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم مغادرة السيرفر بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('serverModal'))?.hide();
            loadServers(); // إعادة تحميل القائمة
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في مغادرة السيرفر');
    }
}

// تحميل السيرفرات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadServers);
</script>

</body>
</html>
