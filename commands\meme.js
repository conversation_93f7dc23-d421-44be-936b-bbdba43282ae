/**
 * CS Bot - أمر Meme
 * 
 * يعرض ميمز عشوائية مضحكة
 */

const { EmbedBuilder } = require('discord.js');
const axios = require('axios');

module.exports = {
    name: 'meme',
    aliases: ['ميم', 'funny', 'joke'],
    description: 'عرض ميم عشوائي مضحك',
    usage: '!meme',
    category: 'ترفيه',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            // إرسال رسالة تحميل
            const loadingMsg = await message.reply('😂 جاري البحث عن ميم مضحك...');
            
            let memeData;
            
            try {
                // محاولة الحصول على ميم من Reddit API
                const response = await axios.get('https://meme-api.herokuapp.com/gimme');
                memeData = response.data;
            } catch (apiError) {
                // في حالة فشل API، استخدم ميمز محلية
                const localMemes = [
                    {
                        title: 'عندما تحاول تشغيل الكود لأول مرة',
                        url: 'https://i.imgur.com/placeholder1.jpg',
                        author: 'CS Bot',
                        subreddit: 'programming'
                    },
                    {
                        title: 'عندما يعمل الكود من أول مرة',
                        url: 'https://i.imgur.com/placeholder2.jpg',
                        author: 'CS Bot',
                        subreddit: 'programming'
                    },
                    {
                        title: 'عندما تنسى حفظ الملف',
                        url: 'https://i.imgur.com/placeholder3.jpg',
                        author: 'CS Bot',
                        subreddit: 'programming'
                    },
                    {
                        title: 'عندما تجد الخطأ بعد 3 ساعات',
                        url: 'https://i.imgur.com/placeholder4.jpg',
                        author: 'CS Bot',
                        subreddit: 'programming'
                    },
                    {
                        title: 'عندما يطلب منك شرح الكود',
                        url: 'https://i.imgur.com/placeholder5.jpg',
                        author: 'CS Bot',
                        subreddit: 'programming'
                    }
                ];
                
                memeData = localMemes[Math.floor(Math.random() * localMemes.length)];
            }
            
            // إنشاء Embed للميم
            const embed = new EmbedBuilder()
                .setTitle(`😂 ${memeData.title}`)
                .setColor('#ff6b6b')
                .setImage(memeData.url)
                .addFields(
                    {
                        name: '📍 المصدر',
                        value: `r/${memeData.subreddit || 'memes'}`,
                        inline: true
                    },
                    {
                        name: '👤 بواسطة',
                        value: memeData.author || 'مجهول',
                        inline: true
                    },
                    {
                        name: '⭐ التقييم',
                        value: `${Math.floor(Math.random() * 1000) + 100} 👍`,
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ 
                    text: `طلب بواسطة ${message.author.username} • اضغط 🔄 للميم التالي`, 
                    iconURL: message.author.displayAvatarURL() 
                });
            
            // إضافة أزرار تفاعلية
            const { ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
            
            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('next_meme')
                        .setLabel('ميم آخر')
                        .setEmoji('🔄')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('save_meme')
                        .setLabel('حفظ')
                        .setEmoji('💾')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('share_meme')
                        .setLabel('مشاركة')
                        .setEmoji('📤')
                        .setStyle(ButtonStyle.Success)
                );
            
            const memeMessage = await loadingMsg.edit({ 
                content: null, 
                embeds: [embed], 
                components: [buttons] 
            });
            
            // إضافة ردود أفعال
            await memeMessage.react('😂');
            await memeMessage.react('👍');
            await memeMessage.react('👎');
            
            // معالج الأزرار
            const collector = memeMessage.createMessageComponentCollector({
                time: 60000 // دقيقة واحدة
            });
            
            collector.on('collect', async (interaction) => {
                if (interaction.user.id !== message.author.id) {
                    return interaction.reply({ 
                        content: '❌ يمكن فقط لطالب الميم استخدام هذه الأزرار!', 
                        ephemeral: true 
                    });
                }
                
                switch (interaction.customId) {
                    case 'next_meme':
                        // إعادة تشغيل الأمر للحصول على ميم جديد
                        await interaction.deferUpdate();
                        this.execute(message, args);
                        break;
                        
                    case 'save_meme':
                        await interaction.reply({ 
                            content: '💾 تم حفظ الميم في مفضلتك! (ميزة قادمة)', 
                            ephemeral: true 
                        });
                        break;
                        
                    case 'share_meme':
                        await interaction.reply({ 
                            content: `📤 شارك هذا الميم: ${memeData.url}`, 
                            ephemeral: true 
                        });
                        break;
                }
            });
            
            collector.on('end', () => {
                // تعطيل الأزرار بعد انتهاء الوقت
                const disabledButtons = new ActionRowBuilder()
                    .addComponents(
                        buttons.components.map(button => 
                            ButtonBuilder.from(button).setDisabled(true)
                        )
                    );
                
                memeMessage.edit({ components: [disabledButtons] }).catch(() => {});
            });
            
        } catch (error) {
            console.error('خطأ في أمر meme:', error);
            message.reply('❌ حدث خطأ أثناء جلب الميم! حاول مرة أخرى.');
        }
    }
};
