/**
 * أمر إزالة البريميوم - !remove @user
 */

const { getUserData } = require('../../models/user');

module.exports = {
    name: 'remove',
    description: 'إزالة البريميوم من مستخدم',
    usage: '!remove @user',
    category: 'admin',
    ownerOnly: true,
    
    async execute(message, args, client) {
        try {
            // التحقق من الصلاحيات
            if (message.author.id !== client.config.owner.id) {
                return message.reply('❌ هذا الأمر مخصص للمطور فقط!');
            }

            // التحقق من وجود المعاملات
            if (args.length < 1) {
                return message.reply('❌ الاستخدام الصحيح: `!remove @user`');
            }

            // الحصول على المستخدم
            const user = message.mentions.users.first();
            if (!user) {
                return message.reply('❌ يرجى منشن المستخدم المراد إزالة البريميوم منه!');
            }

            // الحصول على بيانات المستخدم
            const userData = await getUserData(user.id, user.username, user.discriminator, user.avatar);
            
            // التحقق من وجود البريميوم
            if (!userData.premium || !userData.premium.enabled) {
                return message.reply('❌ هذا المستخدم لا يملك بريميوم!');
            }

            // إزالة البريميوم
            userData.premium = {
                enabled: false,
                expiresAt: null,
                tier: null,
                removedBy: message.author.id,
                removedAt: new Date()
            };

            await userData.save();

            // إرسال رسالة تأكيد
            const embed = {
                color: 0xFF0000,
                title: '🚫 تم إزالة البريميوم',
                description: `تم إزالة البريميوم من المستخدم ${user} بنجاح!`,
                fields: [
                    {
                        name: '👤 المستخدم',
                        value: `${user.tag} (${user.id})`,
                        inline: true
                    },
                    {
                        name: '📅 تاريخ الإزالة',
                        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                ],
                footer: {
                    text: `تم الإزالة بواسطة ${message.author.tag}`,
                    icon_url: message.author.displayAvatarURL()
                },
                timestamp: new Date()
            };

            await message.reply({ embeds: [embed] });

            // تسجيل في اللوج
            console.log(`✅ تم إزالة البريميوم من المستخدم ${user.tag} بواسطة ${message.author.tag}`);

        } catch (error) {
            console.error('خطأ في أمر remove:', error);
            message.reply('❌ حدث خطأ أثناء إزالة البريميوم!');
        }
    }
};
