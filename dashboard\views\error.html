<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حدث خطأ - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="bg-gradient">

<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-8 text-center">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        <i class="fas fa-bug fa-4x text-danger mb-3"></i>
                        <h1 class="display-4 fw-bold text-danger">خطأ</h1>
                    </div>

                    <!-- Error Message -->
                    <h2 class="mb-3">حدث خطأ غير متوقع</h2>
                    <p class="text-muted mb-4">
                        عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.
                    </p>

                    <!-- Error Details (if available) -->
                    <div class="alert alert-danger text-start" style="display: none;" id="errorDetails">
                        <h6><i class="fas fa-info-circle me-2"></i>تفاصيل الخطأ:</h6>
                        <code id="errorMessage">خطأ غير محدد</code>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <button onclick="history.back()" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للخلف
                        </button>
                        <a href="/" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                        <button onclick="location.reload()" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-redo me-2"></i>
                            إعادة المحاولة
                        </button>
                    </div>

                    <!-- Help Text -->
                    <div class="mt-4">
                        <p class="small text-muted">
                            إذا استمر هذا الخطأ، يرجى التواصل مع المطور مع تفاصيل ما كنت تحاول فعله.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// عرض تفاصيل الخطأ إذا كانت متوفرة
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    const message = urlParams.get('message');
    
    if (error || message) {
        const errorDetails = document.getElementById('errorDetails');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message || error || 'خطأ غير محدد';
        errorDetails.style.display = 'block';
    }
});
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    min-height: 100vh;
}

.card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
}

.fa-bug {
    animation: shake 1s infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.btn {
    border-radius: 15px;
    font-weight: 600;
}

.alert {
    border-radius: 15px;
}
</style>

</body>
</html>
