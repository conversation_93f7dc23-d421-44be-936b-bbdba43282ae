<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات المتقدمة - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64" 
                 alt="CS Bot" 
                 class="rounded-circle me-2" 
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-chart-bar"></i> CS Bot - التحليلات المتقدمة</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-chart-bar"></i> التحليلات المتقدمة
                    </h2>
                    <p class="card-text">إحصائيات وتحليلات شاملة لأداء البوت</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="totalViews">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي المشاهدات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-terminal fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="commandsExecuted">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">الأوامر المنفذة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-user-plus fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="newUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">مستخدمين جدد</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="uptime">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">وقت التشغيل</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Commands Chart -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> استخدام الأوامر (آخر 7 أيام)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="commandsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Users Chart -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i> توزيع المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="usersChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Servers Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area"></i> نمو السيرفرات (آخر 30 يوم)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="serversChart" width="400" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Commands Table -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy"></i> أكثر الأوامر استخداماً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الأمر</th>
                                    <th>عدد الاستخدامات</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody id="topCommandsTable">
                                <tr>
                                    <td colspan="3" class="text-center py-3">
                                        <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Servers Table -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> أكثر السيرفرات نشاطاً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>السيرفر</th>
                                    <th>الأعضاء</th>
                                    <th>النشاط</th>
                                </tr>
                            </thead>
                            <tbody id="topServersTable">
                                <tr>
                                    <td colspan="3" class="text-center py-3">
                                        <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Stats -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-broadcast-tower"></i> الإحصائيات المباشرة
                        <span class="badge bg-success ms-2" id="liveIndicator">
                            <i class="fas fa-circle"></i> مباشر
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>الأوامر في الدقيقة الأخيرة</h6>
                                <h3 class="text-primary" id="commandsPerMinute">0</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>المستخدمين النشطين</h6>
                                <h3 class="text-success" id="activeUsers">0</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>استخدام الذاكرة</h6>
                                <h3 class="text-info" id="memoryUsage">0 MB</h3>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>استخدام المعالج</h6>
                                <h3 class="text-warning" id="cpuUsage">0%</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تحميل البيانات التحليلية
async function loadAnalytics() {
    try {
        const response = await fetch('/api/analytics');
        const data = await response.json();
        
        if (response.ok) {
            updateStats(data.stats);
            updateCharts(data.charts);
            updateTables(data.tables);
        } else {
            console.error('خطأ في تحميل التحليلات:', data.error);
            loadDummyData();
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        loadDummyData();
    }
}

// تحديث الإحصائيات
function updateStats(stats) {
    document.getElementById('totalViews').textContent = stats.totalViews || '1,234';
    document.getElementById('commandsExecuted').textContent = stats.commandsExecuted || '5,678';
    document.getElementById('newUsers').textContent = stats.newUsers || '89';
    document.getElementById('uptime').textContent = stats.uptime || '99.9%';
}

// تحديث الرسوم البيانية
function updateCharts(chartsData) {
    // رسم بياني لاستخدام الأوامر
    const commandsCtx = document.getElementById('commandsChart').getContext('2d');
    new Chart(commandsCtx, {
        type: 'line',
        data: {
            labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            datasets: [{
                label: 'الأوامر المنفذة',
                data: chartsData?.commands || [12, 19, 3, 5, 2, 3, 9],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // رسم بياني دائري للمستخدمين
    const usersCtx = document.getElementById('usersChart').getContext('2d');
    new Chart(usersCtx, {
        type: 'doughnut',
        data: {
            labels: ['مستخدمين نشطين', 'مستخدمين غير نشطين', 'مستخدمين بريميوم'],
            datasets: [{
                data: chartsData?.users || [65, 25, 10],
                backgroundColor: [
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(255, 99, 132)'
                ]
            }]
        },
        options: {
            responsive: true
        }
    });

    // رسم بياني لنمو السيرفرات
    const serversCtx = document.getElementById('serversChart').getContext('2d');
    new Chart(serversCtx, {
        type: 'bar',
        data: {
            labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
            datasets: [{
                label: 'سيرفرات جديدة',
                data: chartsData?.servers || [4, 7, 2, 5],
                backgroundColor: 'rgba(153, 102, 255, 0.6)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// تحديث الجداول
function updateTables(tablesData) {
    // جدول أكثر الأوامر استخداماً
    const topCommands = tablesData?.topCommands || [
        { name: 'help', count: 1234, percentage: 25 },
        { name: 'ping', count: 987, percentage: 20 },
        { name: 'music', count: 654, percentage: 13 },
        { name: 'fun', count: 432, percentage: 9 },
        { name: 'mod', count: 321, percentage: 7 }
    ];
    
    document.getElementById('topCommandsTable').innerHTML = topCommands.map(cmd => `
        <tr>
            <td><code>/${cmd.name}</code></td>
            <td><span class="badge bg-primary">${cmd.count}</span></td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" role="progressbar" style="width: ${cmd.percentage}%">
                        ${cmd.percentage}%
                    </div>
                </div>
            </td>
        </tr>
    `).join('');

    // جدول أكثر السيرفرات نشاطاً
    const topServers = tablesData?.topServers || [
        { name: 'سيرفر الاختبار الأول', members: 150, activity: 'عالي' },
        { name: 'مجتمع المطورين', members: 500, activity: 'متوسط' },
        { name: 'سيرفر الألعاب', members: 75, activity: 'منخفض' }
    ];
    
    document.getElementById('topServersTable').innerHTML = topServers.map(server => `
        <tr>
            <td>${server.name}</td>
            <td><span class="badge bg-info">${server.members}</span></td>
            <td>
                <span class="badge ${server.activity === 'عالي' ? 'bg-success' : server.activity === 'متوسط' ? 'bg-warning' : 'bg-secondary'}">
                    ${server.activity}
                </span>
            </td>
        </tr>
    `).join('');
}

// تحميل بيانات وهمية للاختبار
function loadDummyData() {
    updateStats({
        totalViews: '1,234',
        commandsExecuted: '5,678',
        newUsers: '89',
        uptime: '99.9%'
    });
    
    updateCharts({
        commands: [12, 19, 3, 5, 2, 3, 9],
        users: [65, 25, 10],
        servers: [4, 7, 2, 5]
    });
    
    updateTables({
        topCommands: [
            { name: 'help', count: 1234, percentage: 25 },
            { name: 'ping', count: 987, percentage: 20 },
            { name: 'music', count: 654, percentage: 13 },
            { name: 'fun', count: 432, percentage: 9 },
            { name: 'mod', count: 321, percentage: 7 }
        ],
        topServers: [
            { name: 'سيرفر الاختبار الأول', members: 150, activity: 'عالي' },
            { name: 'مجتمع المطورين', members: 500, activity: 'متوسط' },
            { name: 'سيرفر الألعاب', members: 75, activity: 'منخفض' }
        ]
    });
}

// تحديث الإحصائيات المباشرة
function updateLiveStats() {
    document.getElementById('commandsPerMinute').textContent = Math.floor(Math.random() * 10);
    document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 50) + 10;
    document.getElementById('memoryUsage').textContent = (Math.random() * 100 + 50).toFixed(1) + ' MB';
    document.getElementById('cpuUsage').textContent = (Math.random() * 30 + 5).toFixed(1) + '%';
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    loadAnalytics();
    
    // تحديث الإحصائيات المباشرة كل 5 ثوان
    setInterval(updateLiveStats, 5000);
    
    // تحديث البيانات كل دقيقة
    setInterval(loadAnalytics, 60000);
});
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

#liveIndicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>

</body>
</html>
