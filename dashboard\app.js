/**
 * CS Bot - Dashboard App
 *
 * هذا الملف مسؤول عن تشغيل لوحة التحكم الخاصة بالبوت
 */

// استيراد المكتبات اللازمة
const express = require('express');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const { Strategy } = require('passport-discord');
const path = require('path');
const bodyParser = require('body-parser');
const mongoose = require('mongoose');

// استيراد الإعدادات
const config = require('../config');

// إنشاء تطبيق Express
const app = express();

// إعداد المحرك للقوالب - استخدام HTML بدلاً من EJS
app.set('view engine', 'html');
app.set('views', path.join(__dirname, 'views'));

// إعداد محرك HTML مخصص
app.engine('html', (filePath, options, callback) => {
    const fs = require('fs');

    fs.readFile(filePath, 'utf8', (err, content) => {
        if (err) return callback(err);

        // استبدال المتغيرات البسيطة
        let rendered = content
            .replace(/{{user\.username}}/g, options.user ? options.user.username : 'زائر')
            .replace(/{{user\.id}}/g, options.user ? options.user.id : '')
            .replace(/{{user\.avatar}}/g, options.user ? options.user.avatar : '')
            .replace(/{{config\.clientId}}/g, options.config ? options.config.clientId : '')
            .replace(/{{config\.prefix}}/g, options.config ? options.config.prefix : '!')
            .replace(/{{bot\.name}}/g, options.bot ? options.bot.name : 'CS Bot');

        // معالجة الشروط البسيطة
        if (options.user) {
            rendered = rendered.replace(/{{#if user}}([\s\S]*?){{\/if}}/g, '$1');
            rendered = rendered.replace(/{{#unless user}}([\s\S]*?){{\/unless}}/g, '');
        } else {
            rendered = rendered.replace(/{{#if user}}([\s\S]*?){{\/if}}/g, '');
            rendered = rendered.replace(/{{#unless user}}([\s\S]*?){{\/unless}}/g, '$1');
        }

        callback(null, rendered);
    });
});

// إضافة middleware لتمرير المتغيرات العامة لجميع القوالب
app.use((req, res, next) => {
    res.locals.user = req.user || null;
    res.locals.config = req.app.locals.config || {};
    res.locals.bot = req.app.locals.bot || {};
    next();
});

// إعداد الـ Middleware
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// إعداد الجلسات
app.use(session({
    secret: config.dashboard.sessionSecret,
    cookie: {
        maxAge: 60000 * 60 * 24 // 24 ساعة
    },
    resave: false,
    saveUninitialized: false,
    store: MongoStore.create({
        mongoUrl: config.mongoURI
    })
}));

// إعداد Passport للمصادقة
passport.serializeUser((user, done) => done(null, user));
passport.deserializeUser((obj, done) => done(null, obj));

passport.use(new Strategy({
    clientID: config.clientId,
    clientSecret: config.clientSecret,
    callbackURL: `${config.dashboard.domain}${config.dashboard.callbackURL}`,
    scope: ['identify', 'guilds']
}, (accessToken, refreshToken, profile, done) => {
    process.nextTick(() => done(null, profile));
}));

app.use(passport.initialize());
app.use(passport.session());

// متغيرات عامة للقوالب
app.locals.bot = {
    name: "CS Bot",
    avatar: "/img/logo.png"
};
app.locals.config = config;

// دالة للتحقق من تسجيل الدخول
const checkAuth = (req, res, next) => {
    if (req.isAuthenticated()) return next();
    req.session.backURL = req.url;
    res.redirect('/auth/login');
};

// استيراد المسارات
const authRoutes = require('./routes/auth');
const indexRoutes = require('./routes/index');
const dashboardRoutes = require('./routes/dashboard');
const apiRoutes = require('./routes/api');

// استخدام المسارات
app.use('/auth', authRoutes);
app.use('/', indexRoutes);
app.use('/dashboard', checkAuth, dashboardRoutes);
app.use('/api', apiRoutes);

// التعامل مع الصفحات غير الموجودة
app.use((req, res) => {
    res.status(404).render('404', {
        user: req.user,
        path: req.path,
        config: config,
        bot: app.locals.bot
    });
});

// تصدير التطبيق
module.exports = app;
