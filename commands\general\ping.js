/**
 * أمر البينغ الأسطوري
 */

module.exports = {
    name: 'ping',
    description: 'فحص سرعة استجابة البوت',
    usage: '!ping',
    category: 'general',
    
    async execute(message, args, client) {
        try {
            const sent = await message.reply('🏓 جاري فحص البينغ...');
            
            const timeTaken = sent.createdTimestamp - message.createdTimestamp;
            const apiPing = Math.round(client.ws.ping);
            
            // تحديد لون البينغ حسب السرعة
            let color = 0x00FF00; // أخضر
            let status = 'ممتاز';
            
            if (apiPing > 100) {
                color = 0xFFFF00; // أصفر
                status = 'جيد';
            }
            if (apiPing > 200) {
                color = 0xFF8000; // برتقالي
                status = 'متوسط';
            }
            if (apiPing > 300) {
                color = 0xFF0000; // أحمر
                status = 'بطيء';
            }

            const embed = {
                color: color,
                title: '🏓 نتائج فحص البينغ',
                fields: [
                    {
                        name: '📡 بينغ API',
                        value: `\`${apiPing}ms\``,
                        inline: true
                    },
                    {
                        name: '💬 بينغ الرسالة',
                        value: `\`${timeTaken}ms\``,
                        inline: true
                    },
                    {
                        name: '📊 الحالة',
                        value: `\`${status}\``,
                        inline: true
                    }
                ],
                footer: {
                    text: 'CS Bot الأسطوري',
                    icon_url: client.user.displayAvatarURL()
                },
                timestamp: new Date()
            };

            await sent.edit({ content: null, embeds: [embed] });

        } catch (error) {
            console.error('خطأ في أمر ping:', error);
            message.reply('❌ حدث خطأ أثناء فحص البينغ!');
        }
    }
};
