/**
 * أوامر الاقتصاد - CS Bot
 * نظام اقتصادي متكامل مع العملات والمتجر
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const User = require('../models/User');
const language = require('../utils/language');

module.exports = {
    name: 'economy',
    description: 'نظام الاقتصاد والعملات',
    aliases: ['eco', 'اقتصاد', 'فلوس'],
    category: 'اقتصاد',
    usage: 'economy <balance|daily|work|shop|transfer>',
    cooldown: 3,
    
    async execute(message, args, client) {
        const lang = await language.getLanguage(message.guild?.id);
        const subcommand = args[0]?.toLowerCase();

        if (!subcommand) {
            return this.showEconomyMenu(message, lang);
        }

        switch (subcommand) {
            case 'balance':
            case 'bal':
            case 'رصيد':
                return this.showBalance(message, args, lang);
            
            case 'daily':
            case 'يومي':
                return this.dailyReward(message, lang);
            
            case 'work':
            case 'عمل':
                return this.work(message, lang);
            
            case 'shop':
            case 'متجر':
                return this.showShop(message, lang);
            
            case 'transfer':
            case 'تحويل':
                return this.transferMoney(message, args, lang);
            
            case 'gamble':
            case 'مقامرة':
                return this.gamble(message, args, lang);
            
            case 'leaderboard':
            case 'lb':
            case 'ترتيب':
                return this.showLeaderboard(message, lang);
            
            default:
                return this.showEconomyMenu(message, lang);
        }
    },

    async showEconomyMenu(message, lang) {
        const embed = new EmbedBuilder()
            .setTitle('🏦 نظام الاقتصاد')
            .setDescription('اختر من الخيارات التالية:')
            .addFields([
                {
                    name: '💰 الرصيد',
                    value: '`economy balance` - عرض رصيدك',
                    inline: true
                },
                {
                    name: '🎁 المكافأة اليومية',
                    value: '`economy daily` - احصل على مكافأة يومية',
                    inline: true
                },
                {
                    name: '💼 العمل',
                    value: '`economy work` - اعمل لكسب المال',
                    inline: true
                },
                {
                    name: '🛒 المتجر',
                    value: '`economy shop` - تسوق من المتجر',
                    inline: true
                },
                {
                    name: '💸 التحويل',
                    value: '`economy transfer @user amount` - حول المال',
                    inline: true
                },
                {
                    name: '🎲 المقامرة',
                    value: '`economy gamble amount` - قامر بأموالك',
                    inline: true
                },
                {
                    name: '🏆 الترتيب',
                    value: '`economy leaderboard` - أغنى المستخدمين',
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setFooter({ text: 'CS Bot Economy System' });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('economy_balance')
                    .setLabel('💰 الرصيد')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('economy_daily')
                    .setLabel('🎁 يومي')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('economy_work')
                    .setLabel('💼 عمل')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('economy_shop')
                    .setLabel('🛒 متجر')
                    .setStyle(ButtonStyle.Danger)
            );

        return message.reply({ embeds: [embed], components: [row] });
    },

    async showBalance(message, args, lang) {
        const target = message.mentions.users.first() || message.author;
        const userData = await User.findOne({ userId: target.id });

        if (!userData) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('المستخدم غير مسجل في النظام')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle(`💰 رصيد ${target.username}`)
            .addFields([
                {
                    name: '💵 الرصيد الحالي',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '📊 المستوى',
                    value: `**${userData.economy.level}**`,
                    inline: true
                },
                {
                    name: '⭐ النقاط',
                    value: `**${userData.economy.xp.toLocaleString()}** XP`,
                    inline: true
                },
                {
                    name: '🏦 في البنك',
                    value: `**${(userData.economy.bank || 0).toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '💎 الأحجار الكريمة',
                    value: `**${(userData.economy.gems || 0).toLocaleString()}** جوهرة`,
                    inline: true
                },
                {
                    name: '🎯 إجمالي الكسب',
                    value: `**${(userData.economy.totalEarned || 0).toLocaleString()}** عملة`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(target.displayAvatarURL())
            .setFooter({ text: `المستوى التالي: ${((userData.economy.level + 1) * 1000) - userData.economy.xp} XP` });

        return message.reply({ embeds: [embed] });
    },

    async dailyReward(message, lang) {
        const userData = await User.findOneAndUpdate(
            { userId: message.author.id },
            {},
            { upsert: true, new: true }
        );

        const now = new Date();
        const lastDaily = userData.economy.lastDaily;
        const cooldown = 24 * 60 * 60 * 1000; // 24 ساعة

        if (lastDaily && (now - lastDaily) < cooldown) {
            const timeLeft = cooldown - (now - lastDaily);
            const hours = Math.floor(timeLeft / (60 * 60 * 1000));
            const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));

            const embed = new EmbedBuilder()
                .setTitle('⏰ انتظر قليلاً')
                .setDescription(`يمكنك الحصول على المكافأة اليومية بعد **${hours}س ${minutes}د**`)
                .setColor('#ff9900');
            return message.reply({ embeds: [embed] });
        }

        // حساب المكافأة
        const baseReward = 1000;
        const levelBonus = userData.economy.level * 100;
        const streakBonus = (userData.economy.dailyStreak || 0) * 50;
        const totalReward = baseReward + levelBonus + streakBonus;

        // تحديث البيانات
        userData.economy.balance += totalReward;
        userData.economy.lastDaily = now;
        userData.economy.dailyStreak = (userData.economy.dailyStreak || 0) + 1;
        userData.economy.totalEarned = (userData.economy.totalEarned || 0) + totalReward;
        
        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle('🎁 مكافأة يومية!')
            .setDescription(`تهانينا! حصلت على مكافأتك اليومية`)
            .addFields([
                {
                    name: '💰 المكافأة الأساسية',
                    value: `${baseReward.toLocaleString()} عملة`,
                    inline: true
                },
                {
                    name: '📊 مكافأة المستوى',
                    value: `${levelBonus.toLocaleString()} عملة`,
                    inline: true
                },
                {
                    name: '🔥 مكافأة التتالي',
                    value: `${streakBonus.toLocaleString()} عملة`,
                    inline: true
                },
                {
                    name: '💵 إجمالي المكافأة',
                    value: `**${totalReward.toLocaleString()}** عملة`,
                    inline: false
                },
                {
                    name: '💰 رصيدك الجديد',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '🔥 سلسلة التتالي',
                    value: `**${userData.economy.dailyStreak}** يوم`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(message.author.displayAvatarURL());

        return message.reply({ embeds: [embed] });
    },

    async work(message, lang) {
        const userData = await User.findOneAndUpdate(
            { userId: message.author.id },
            {},
            { upsert: true, new: true }
        );

        const now = new Date();
        const lastWork = userData.economy.lastWork;
        const cooldown = 60 * 60 * 1000; // ساعة واحدة

        if (lastWork && (now - lastWork) < cooldown) {
            const timeLeft = cooldown - (now - lastWork);
            const minutes = Math.floor(timeLeft / (60 * 1000));

            const embed = new EmbedBuilder()
                .setTitle('😴 أنت متعب')
                .setDescription(`يمكنك العمل مرة أخرى بعد **${minutes}** دقيقة`)
                .setColor('#ff9900');
            return message.reply({ embeds: [embed] });
        }

        // أنواع الأعمال
        const jobs = [
            { name: 'مطور برمجيات', min: 500, max: 1500, emoji: '💻' },
            { name: 'طبيب', min: 800, max: 2000, emoji: '👨‍⚕️' },
            { name: 'مهندس', min: 600, max: 1800, emoji: '👷' },
            { name: 'معلم', min: 400, max: 1200, emoji: '👨‍🏫' },
            { name: 'طباخ', min: 300, max: 1000, emoji: '👨‍🍳' },
            { name: 'سائق', min: 200, max: 800, emoji: '🚗' },
            { name: 'عامل نظافة', min: 150, max: 600, emoji: '🧹' }
        ];

        const randomJob = jobs[Math.floor(Math.random() * jobs.length)];
        const earnings = Math.floor(Math.random() * (randomJob.max - randomJob.min + 1)) + randomJob.min;
        const xpGain = Math.floor(earnings / 10);

        // تحديث البيانات
        userData.economy.balance += earnings;
        userData.economy.xp += xpGain;
        userData.economy.lastWork = now;
        userData.economy.totalEarned = (userData.economy.totalEarned || 0) + earnings;

        // فحص ترقية المستوى
        const newLevel = Math.floor(userData.economy.xp / 1000) + 1;
        let levelUp = false;
        if (newLevel > userData.economy.level) {
            userData.economy.level = newLevel;
            levelUp = true;
        }

        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle(`${randomJob.emoji} عمل رائع!`)
            .setDescription(`عملت كـ **${randomJob.name}** وكسبت:`)
            .addFields([
                {
                    name: '💰 الأرباح',
                    value: `**${earnings.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '⭐ النقاط',
                    value: `**${xpGain}** XP`,
                    inline: true
                },
                {
                    name: '💵 رصيدك الجديد',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(message.author.displayAvatarURL());

        if (levelUp) {
            embed.addFields([
                {
                    name: '🎉 ترقية مستوى!',
                    value: `تهانينا! وصلت للمستوى **${userData.economy.level}**`,
                    inline: false
                }
            ]);
        }

        return message.reply({ embeds: [embed] });
    },

    async showShop(message, lang) {
        const shopItems = [
            {
                id: 'premium_1day',
                name: 'بريميوم يوم واحد',
                price: 10000,
                emoji: '👑',
                description: 'احصل على البريميوم لمدة يوم واحد'
            },
            {
                id: 'premium_1week',
                name: 'بريميوم أسبوع',
                price: 50000,
                emoji: '💎',
                description: 'احصل على البريميوم لمدة أسبوع'
            },
            {
                id: 'xp_boost',
                name: 'مضاعف النقاط',
                price: 5000,
                emoji: '⚡',
                description: 'مضاعف النقاط لمدة ساعة'
            },
            {
                id: 'money_boost',
                name: 'مضاعف المال',
                price: 7500,
                emoji: '💰',
                description: 'مضاعف الأرباح لمدة ساعة'
            },
            {
                id: 'custom_role',
                name: 'رتبة مخصصة',
                price: 25000,
                emoji: '🎨',
                description: 'احصل على رتبة مخصصة'
            }
        ];

        const embed = new EmbedBuilder()
            .setTitle('🛒 متجر CS Bot')
            .setDescription('اشتري العناصر باستخدام عملاتك!')
            .setColor('#0099ff');

        shopItems.forEach(item => {
            embed.addFields([
                {
                    name: `${item.emoji} ${item.name}`,
                    value: `${item.description}\n💰 السعر: **${item.price.toLocaleString()}** عملة`,
                    inline: true
                }
            ]);
        });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('shop_premium')
                    .setLabel('👑 بريميوم')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('shop_boosts')
                    .setLabel('⚡ مضاعفات')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('shop_roles')
                    .setLabel('🎨 رتب')
                    .setStyle(ButtonStyle.Secondary)
            );

        return message.reply({ embeds: [embed], components: [row] });
    },

    async transferMoney(message, args, lang) {
        const target = message.mentions.users.first();
        const amount = parseInt(args[2]);

        if (!target) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('يرجى منشن المستخدم الذي تريد التحويل إليه')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        if (!amount || amount <= 0) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('يرجى إدخال مبلغ صحيح للتحويل')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        if (target.id === message.author.id) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('لا يمكنك تحويل المال لنفسك!')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const senderData = await User.findOne({ userId: message.author.id });
        if (!senderData || senderData.economy.balance < amount) {
            const embed = new EmbedBuilder()
                .setTitle('❌ رصيد غير كافي')
                .setDescription('ليس لديك رصيد كافي لهذا التحويل')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        // تحديث رصيد المرسل
        senderData.economy.balance -= amount;
        await senderData.save();

        // تحديث رصيد المستقبل
        await User.findOneAndUpdate(
            { userId: target.id },
            { 
                $inc: { 'economy.balance': amount },
                $setOnInsert: { 
                    userId: target.id,
                    username: target.username,
                    'economy.level': 1,
                    'economy.xp': 0
                }
            },
            { upsert: true }
        );

        const embed = new EmbedBuilder()
            .setTitle('💸 تحويل ناجح')
            .setDescription(`تم تحويل **${amount.toLocaleString()}** عملة إلى ${target}`)
            .addFields([
                {
                    name: '💰 رصيدك الجديد',
                    value: `**${senderData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(target.displayAvatarURL());

        return message.reply({ embeds: [embed] });
    },

    async gamble(message, args, lang) {
        const amount = parseInt(args[1]);
        
        if (!amount || amount <= 0) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('يرجى إدخال مبلغ صحيح للمقامرة')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const userData = await User.findOne({ userId: message.author.id });
        if (!userData || userData.economy.balance < amount) {
            const embed = new EmbedBuilder()
                .setTitle('❌ رصيد غير كافي')
                .setDescription('ليس لديك رصيد كافي للمقامرة')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        // نسبة الفوز 45%
        const win = Math.random() < 0.45;
        const multiplier = win ? (Math.random() * 1.5 + 1.5) : 0; // 1.5x - 3x
        const winnings = Math.floor(amount * multiplier);

        if (win) {
            userData.economy.balance += winnings - amount;
            await userData.save();

            const embed = new EmbedBuilder()
                .setTitle('🎉 فزت!')
                .setDescription(`تهانينا! فزت في المقامرة`)
                .addFields([
                    {
                        name: '💰 المبلغ المراهن',
                        value: `${amount.toLocaleString()} عملة`,
                        inline: true
                    },
                    {
                        name: '🎯 المضاعف',
                        value: `${multiplier.toFixed(2)}x`,
                        inline: true
                    },
                    {
                        name: '💵 الأرباح',
                        value: `**${winnings.toLocaleString()}** عملة`,
                        inline: true
                    },
                    {
                        name: '💰 رصيدك الجديد',
                        value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                        inline: false
                    }
                ])
                .setColor('#00ff00');

            return message.reply({ embeds: [embed] });
        } else {
            userData.economy.balance -= amount;
            await userData.save();

            const embed = new EmbedBuilder()
                .setTitle('😢 خسرت!')
                .setDescription(`للأسف، خسرت في المقامرة`)
                .addFields([
                    {
                        name: '💸 المبلغ المفقود',
                        value: `${amount.toLocaleString()} عملة`,
                        inline: true
                    },
                    {
                        name: '💰 رصيدك الجديد',
                        value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                        inline: true
                    }
                ])
                .setColor('#ff0000');

            return message.reply({ embeds: [embed] });
        }
    },

    async showLeaderboard(message, lang) {
        const topUsers = await User.find({})
            .sort({ 'economy.balance': -1 })
            .limit(10);

        if (topUsers.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('📊 ترتيب الأثرياء')
                .setDescription('لا يوجد مستخدمين في النظام بعد')
                .setColor('#ff9900');
            return message.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle('🏆 ترتيب أغنى المستخدمين')
            .setDescription('أغنى 10 مستخدمين في البوت')
            .setColor('#ffd700');

        let description = '';
        for (let i = 0; i < topUsers.length; i++) {
            const user = topUsers[i];
            const medal = i === 0 ? '🥇' : i === 1 ? '🥈' : i === 2 ? '🥉' : `${i + 1}.`;
            description += `${medal} **${user.username}** - ${user.economy.balance.toLocaleString()} عملة\n`;
        }

        embed.setDescription(description);

        // إضافة ترتيب المستخدم الحالي
        const userRank = await User.countDocuments({
            'economy.balance': { $gt: (await User.findOne({ userId: message.author.id }))?.economy?.balance || 0 }
        }) + 1;

        embed.addFields([
            {
                name: '📍 ترتيبك',
                value: `أنت في المرتبة **#${userRank}**`,
                inline: false
            }
        ]);

        return message.reply({ embeds: [embed] });
    }
};
