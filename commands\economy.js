/**
 * CS Bot - نظام الاقتصاد الأسطوري
 * 
 * نظام اقتصادي متكامل مع عملات وجوائز ومتجر
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'economy',
    aliases: ['eco', 'اقتصاد', 'فلوس', 'money'],
    description: 'نظام الاقتصاد الأسطوري - اربح واشتري وتاجر!',
    usage: '!economy [balance|daily|work|shop|buy|transfer]',
    category: 'اقتصاد',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            const subCommand = args[0]?.toLowerCase() || 'balance';
            const userId = message.author.id;
            
            // قاعدة بيانات مؤقتة للاقتصاد
            const memoryDB = require('../utils/memoryDB');
            
            switch (subCommand) {
                case 'balance':
                case 'bal':
                case 'رصيد':
                    await showBalance(message, userId);
                    break;
                    
                case 'daily':
                case 'يومي':
                    await claimDaily(message, userId);
                    break;
                    
                case 'work':
                case 'عمل':
                    await workForMoney(message, userId);
                    break;
                    
                case 'shop':
                case 'متجر':
                    await showShop(message);
                    break;
                    
                case 'buy':
                case 'شراء':
                    await buyItem(message, args.slice(1), userId);
                    break;
                    
                case 'transfer':
                case 'تحويل':
                    await transferMoney(message, args.slice(1), userId);
                    break;
                    
                case 'leaderboard':
                case 'lb':
                case 'ترتيب':
                    await showLeaderboard(message);
                    break;
                    
                default:
                    await showEconomyHelp(message);
            }
            
        } catch (error) {
            console.error('خطأ في نظام الاقتصاد:', error);
            message.reply('❌ حدث خطأ في نظام الاقتصاد!');
        }
    }
};

// عرض الرصيد
async function showBalance(message, userId) {
    const user = await getUserEconomy(userId);
    const targetUser = message.mentions.users.first() || message.author;
    
    if (message.mentions.users.first()) {
        const targetUserData = await getUserEconomy(message.mentions.users.first().id);
        user.coins = targetUserData.coins;
        user.bank = targetUserData.bank;
    }
    
    const embed = new EmbedBuilder()
        .setTitle(`💰 رصيد ${targetUser.username}`)
        .setThumbnail(targetUser.displayAvatarURL({ dynamic: true }))
        .setColor('#ffd700')
        .addFields(
            {
                name: '💵 النقود',
                value: `${user.coins.toLocaleString()} عملة`,
                inline: true
            },
            {
                name: '🏦 البنك',
                value: `${user.bank.toLocaleString()} عملة`,
                inline: true
            },
            {
                name: '💎 المجموع',
                value: `${(user.coins + user.bank).toLocaleString()} عملة`,
                inline: true
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// المكافأة اليومية
async function claimDaily(message, userId) {
    const user = await getUserEconomy(userId);
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    
    if (user.lastDaily && (now - user.lastDaily) < oneDay) {
        const timeLeft = oneDay - (now - user.lastDaily);
        const hours = Math.floor(timeLeft / (60 * 60 * 1000));
        const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));
        
        return message.reply(`⏰ يمكنك الحصول على المكافأة اليومية بعد ${hours}س ${minutes}د`);
    }
    
    const dailyAmount = Math.floor(Math.random() * 500) + 100; // 100-600 عملة
    const bonus = user.streak >= 7 ? Math.floor(dailyAmount * 0.5) : 0;
    const total = dailyAmount + bonus;
    
    user.coins += total;
    user.lastDaily = now;
    user.streak = user.lastDaily && (now - user.lastDaily) <= oneDay * 2 ? user.streak + 1 : 1;
    
    await saveUserEconomy(userId, user);
    
    const embed = new EmbedBuilder()
        .setTitle('🎁 المكافأة اليومية!')
        .setColor('#00ff00')
        .addFields(
            {
                name: '💰 المكافأة',
                value: `${dailyAmount} عملة`,
                inline: true
            },
            {
                name: '🔥 المتتالية',
                value: `${user.streak} يوم`,
                inline: true
            },
            {
                name: '🎉 مكافأة إضافية',
                value: bonus > 0 ? `${bonus} عملة` : 'لا توجد',
                inline: true
            }
        )
        .setDescription(`تم إضافة **${total}** عملة إلى رصيدك!`)
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// العمل لكسب المال
async function workForMoney(message, userId) {
    const user = await getUserEconomy(userId);
    const now = Date.now();
    const cooldown = 60 * 60 * 1000; // ساعة واحدة
    
    if (user.lastWork && (now - user.lastWork) < cooldown) {
        const timeLeft = cooldown - (now - user.lastWork);
        const minutes = Math.floor(timeLeft / (60 * 1000));
        
        return message.reply(`⏰ يمكنك العمل مرة أخرى بعد ${minutes} دقيقة`);
    }
    
    const jobs = [
        { name: 'مطور ويب', min: 200, max: 500, emoji: '💻' },
        { name: 'مصمم جرافيك', min: 150, max: 400, emoji: '🎨' },
        { name: 'كاتب محتوى', min: 100, max: 300, emoji: '✍️' },
        { name: 'مترجم', min: 120, max: 350, emoji: '🌐' },
        { name: 'محاسب', min: 180, max: 450, emoji: '📊' },
        { name: 'مدرس', min: 140, max: 380, emoji: '👨‍🏫' }
    ];
    
    const job = jobs[Math.floor(Math.random() * jobs.length)];
    const earned = Math.floor(Math.random() * (job.max - job.min + 1)) + job.min;
    
    user.coins += earned;
    user.lastWork = now;
    user.totalEarned = (user.totalEarned || 0) + earned;
    
    await saveUserEconomy(userId, user);
    
    const embed = new EmbedBuilder()
        .setTitle(`${job.emoji} عملت كـ ${job.name}`)
        .setColor('#4CAF50')
        .setDescription(`لقد ربحت **${earned}** عملة من عملك!`)
        .addFields(
            {
                name: '💰 الرصيد الحالي',
                value: `${user.coins.toLocaleString()} عملة`,
                inline: true
            },
            {
                name: '📈 إجمالي الأرباح',
                value: `${user.totalEarned.toLocaleString()} عملة`,
                inline: true
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// عرض المتجر
async function showShop(message) {
    const items = [
        { id: 'premium_1d', name: 'بريميوم يوم واحد', price: 1000, emoji: '👑', description: 'بريميوم لمدة يوم واحد' },
        { id: 'premium_7d', name: 'بريميوم أسبوع', price: 6000, emoji: '💎', description: 'بريميوم لمدة أسبوع' },
        { id: 'custom_role', name: 'رتبة مخصصة', price: 5000, emoji: '🎭', description: 'رتبة بلون واسم مخصص' },
        { id: 'nickname', name: 'تغيير الاسم', price: 2000, emoji: '📝', description: 'تغيير اسمك في السيرفر' },
        { id: 'lottery_ticket', name: 'تذكرة يانصيب', price: 500, emoji: '🎫', description: 'فرصة للفوز بجوائز كبيرة' }
    ];
    
    const embed = new EmbedBuilder()
        .setTitle('🛒 متجر CS Bot الأسطوري')
        .setColor('#e91e63')
        .setDescription('اشتري العناصر المذهلة بعملاتك!')
        .setTimestamp();
    
    items.forEach(item => {
        embed.addFields({
            name: `${item.emoji} ${item.name}`,
            value: `💰 **${item.price.toLocaleString()}** عملة\n${item.description}`,
            inline: true
        });
    });
    
    const buttons = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('buy_premium_1d')
                .setLabel('شراء بريميوم يوم')
                .setEmoji('👑')
                .setStyle(ButtonStyle.Primary),
            new ButtonBuilder()
                .setCustomId('buy_custom_role')
                .setLabel('رتبة مخصصة')
                .setEmoji('🎭')
                .setStyle(ButtonStyle.Secondary)
        );
    
    await message.reply({ embeds: [embed], components: [buttons] });
}

// مساعدة الاقتصاد
async function showEconomyHelp(message) {
    const embed = new EmbedBuilder()
        .setTitle('💰 نظام الاقتصاد الأسطوري')
        .setColor('#ffd700')
        .setDescription('اربح العملات واشتري العناصر المذهلة!')
        .addFields(
            {
                name: '💵 الأوامر الأساسية',
                value: [
                    '`!economy balance` - عرض رصيدك',
                    '`!economy daily` - المكافأة اليومية',
                    '`!economy work` - العمل لكسب المال',
                    '`!economy shop` - عرض المتجر'
                ].join('\n'),
                inline: false
            },
            {
                name: '🛒 أوامر الشراء',
                value: [
                    '`!economy buy <item>` - شراء عنصر',
                    '`!economy transfer @user <amount>` - تحويل أموال',
                    '`!economy leaderboard` - ترتيب الأغنياء'
                ].join('\n'),
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// دوال مساعدة لقاعدة البيانات
async function getUserEconomy(userId) {
    // استخدام قاعدة بيانات مؤقتة
    const memoryDB = require('../utils/memoryDB');
    const user = await memoryDB.getUser(userId);
    
    return {
        coins: user.economy?.coins || 0,
        bank: user.economy?.bank || 0,
        lastDaily: user.economy?.lastDaily || null,
        lastWork: user.economy?.lastWork || null,
        streak: user.economy?.streak || 0,
        totalEarned: user.economy?.totalEarned || 0
    };
}

async function saveUserEconomy(userId, data) {
    const memoryDB = require('../utils/memoryDB');
    const user = await memoryDB.getUser(userId);
    user.economy = data;
    // في التطبيق الحقيقي، سيتم حفظ البيانات في قاعدة البيانات
}
