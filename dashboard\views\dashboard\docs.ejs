<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التوثيق | CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .docs-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .docs-sidebar {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 100px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .docs-content {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            line-height: 1.8;
        }

        .docs-nav-item {
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: right;
        }

        .docs-nav-item:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }

        .docs-nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .command-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }

        .command-syntax {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }

        .parameter {
            color: #68d391;
        }

        .optional {
            color: #fbb6ce;
        }

        .section-content {
            display: none;
        }

        .section-content.active {
            display: block;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس الصفحة -->
            <div class="docs-header">
                <h1 class="text-white mb-3">
                    <i class="fas fa-book fa-3x mb-3 d-block"></i>
                    دليل CS Bot
                </h1>
                <p class="text-white-50 lead mb-0">
                    دليل شامل لاستخدام جميع ميزات البوت
                </p>
            </div>

            <div class="row">
                <!-- الشريط الجانبي للتنقل -->
                <div class="col-lg-3">
                    <div class="docs-sidebar">
                        <h5 class="mb-3">المحتويات</h5>
                        <button class="docs-nav-item active" onclick="showSection('getting-started')">
                            <i class="fas fa-play me-2"></i>البداية السريعة
                        </button>
                        <button class="docs-nav-item" onclick="showSection('commands')">
                            <i class="fas fa-terminal me-2"></i>الأوامر
                        </button>
                        <button class="docs-nav-item" onclick="showSection('features')">
                            <i class="fas fa-star me-2"></i>الميزات
                        </button>
                        <button class="docs-nav-item" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </button>
                        <button class="docs-nav-item" onclick="showSection('premium')">
                            <i class="fas fa-crown me-2"></i>البريميوم
                        </button>
                        <button class="docs-nav-item" onclick="showSection('troubleshooting')">
                            <i class="fas fa-wrench me-2"></i>حل المشاكل
                        </button>
                        <button class="docs-nav-item" onclick="showSection('api')">
                            <i class="fas fa-code me-2"></i>API
                        </button>
                    </div>
                </div>

                <!-- المحتوى الرئيسي -->
                <div class="col-lg-9">
                    <div class="docs-content">
                        <!-- البداية السريعة -->
                        <div id="getting-started" class="section-content active">
                            <h2><i class="fas fa-play text-primary me-2"></i>البداية السريعة</h2>
                            <p class="lead">مرحباً بك في CS Bot! هذا الدليل سيساعدك على البدء بسرعة.</p>

                            <h3>الخطوة 1: إضافة البوت</h3>
                            <ol>
                                <li>سجل دخول في لوحة التحكم</li>
                                <li>اذهب إلى صفحة "سيرفراتي"</li>
                                <li>اضغط "إضافة البوت" بجانب الخادم المطلوب</li>
                                <li>وافق على الصلاحيات المطلوبة</li>
                            </ol>

                            <h3>الخطوة 2: الإعداد الأساسي</h3>
                            <ul>
                                <li>اذهب لصفحة إدارة السيرفر</li>
                                <li>فعل الميزات التي تريدها</li>
                                <li>اضبط القنوات والرسائل</li>
                            </ul>

                            <h3>الخطوة 3: اختبار البوت</h3>
                            <div class="command-syntax">
                                /help
                            </div>
                            <p>استخدم هذا الأمر لعرض قائمة بجميع الأوامر المتاحة.</p>
                        </div>

                        <!-- الأوامر -->
                        <div id="commands" class="section-content">
                            <h2><i class="fas fa-terminal text-primary me-2"></i>الأوامر</h2>
                            <p>قائمة شاملة بجميع أوامر البوت المتاحة.</p>

                            <h3>أوامر عامة</h3>
                            <div class="command-card">
                                <h5>/help</h5>
                                <p>عرض قائمة المساعدة</p>
                                <div class="command-syntax">
                                    /help [<span class="optional">command</span>]
                                </div>
                            </div>

                            <div class="command-card">
                                <h5>/ping</h5>
                                <p>فحص استجابة البوت</p>
                                <div class="command-syntax">
                                    /ping
                                </div>
                            </div>

                            <h3>أوامر الإدارة</h3>
                            <div class="command-card">
                                <h5>/ban</h5>
                                <p>حظر عضو من الخادم</p>
                                <div class="command-syntax">
                                    /ban <span class="parameter">@user</span> [<span class="optional">reason</span>]
                                </div>
                            </div>

                            <div class="command-card">
                                <h5>/kick</h5>
                                <p>طرد عضو من الخادم</p>
                                <div class="command-syntax">
                                    /kick <span class="parameter">@user</span> [<span class="optional">reason</span>]
                                </div>
                            </div>

                            <h3>أوامر الترفيه</h3>
                            <div class="command-card">
                                <h5>/meme</h5>
                                <p>عرض صورة مضحكة عشوائية</p>
                                <div class="command-syntax">
                                    /meme
                                </div>
                            </div>
                        </div>

                        <!-- الميزات -->
                        <div id="features" class="section-content">
                            <h2><i class="fas fa-star text-primary me-2"></i>الميزات</h2>
                            <p>اكتشف جميع الميزات المتاحة في CS Bot.</p>

                            <div class="feature-grid">
                                <div class="feature-card">
                                    <i class="fas fa-hand-wave fa-2x text-primary mb-3"></i>
                                    <h5>رسائل الترحيب</h5>
                                    <p>رحب بالأعضاء الجدد برسائل مخصصة</p>
                                </div>

                                <div class="feature-card">
                                    <i class="fas fa-shield-alt fa-2x text-success mb-3"></i>
                                    <h5>أدوات الإدارة</h5>
                                    <p>أوامر قوية لإدارة الخادم</p>
                                </div>

                                <div class="feature-card">
                                    <i class="fas fa-music fa-2x text-info mb-3"></i>
                                    <h5>الموسيقى</h5>
                                    <p>تشغيل الموسيقى من مصادر متعددة</p>
                                </div>

                                <div class="feature-card">
                                    <i class="fas fa-chart-line fa-2x text-warning mb-3"></i>
                                    <h5>الإحصائيات</h5>
                                    <p>تتبع نشاط الخادم والأعضاء</p>
                                </div>
                            </div>
                        </div>

                        <!-- لوحة التحكم -->
                        <div id="dashboard" class="section-content">
                            <h2><i class="fas fa-tachometer-alt text-primary me-2"></i>لوحة التحكم</h2>
                            <p>دليل استخدام لوحة التحكم الويب.</p>

                            <h3>الوصول للوحة التحكم</h3>
                            <ol>
                                <li>اذهب إلى موقع البوت</li>
                                <li>اضغط "تسجيل الدخول"</li>
                                <li>سجل دخول بحساب Discord</li>
                            </ol>

                            <h3>إدارة السيرفرات</h3>
                            <ul>
                                <li>عرض جميع السيرفرات التي تديرها</li>
                                <li>إضافة البوت للسيرفرات الجديدة</li>
                                <li>تخصيص إعدادات كل سيرفر</li>
                            </ul>

                            <h3>الإعدادات المتاحة</h3>
                            <ul>
                                <li>رسائل الترحيب والوداع</li>
                                <li>نظام اللوجات</li>
                                <li>الرتب التلقائية</li>
                                <li>الردود التلقائية</li>
                            </ul>
                        </div>

                        <!-- البريميوم -->
                        <div id="premium" class="section-content">
                            <h2><i class="fas fa-crown text-warning me-2"></i>البريميوم</h2>
                            <p>اكتشف الميزات الحصرية للمشتركين في البريميوم.</p>

                            <h3>ميزات البريميوم</h3>
                            <ul>
                                <li>أوامر متقدمة إضافية</li>
                                <li>تخصيص أكبر للرسائل</li>
                                <li>إحصائيات مفصلة</li>
                                <li>دعم أولوية</li>
                                <li>ميزات حصرية</li>
                            </ul>

                            <h3>كيفية الاشتراك</h3>
                            <ol>
                                <li>اذهب لصفحة البريميوم في لوحة التحكم</li>
                                <li>اختر الخطة المناسبة</li>
                                <li>أكمل عملية الدفع</li>
                                <li>استمتع بالميزات الجديدة!</li>
                            </ol>
                        </div>

                        <!-- حل المشاكل -->
                        <div id="troubleshooting" class="section-content">
                            <h2><i class="fas fa-wrench text-danger me-2"></i>حل المشاكل</h2>
                            <p>حلول للمشاكل الشائعة.</p>

                            <h3>البوت لا يستجيب</h3>
                            <ul>
                                <li>تأكد أن البوت متصل</li>
                                <li>تحقق من الصلاحيات</li>
                                <li>تأكد من كتابة الأمر بشكل صحيح</li>
                            </ul>

                            <h3>لا يمكن الوصول للوحة التحكم</h3>
                            <ul>
                                <li>تأكد من تسجيل الدخول بـ Discord</li>
                                <li>امسح ملفات تعريف الارتباط</li>
                                <li>جرب متصفح آخر</li>
                            </ul>
                        </div>

                        <!-- API -->
                        <div id="api" class="section-content">
                            <h2><i class="fas fa-code text-info me-2"></i>API</h2>
                            <p>معلومات للمطورين حول API البوت.</p>

                            <h3>نقاط النهاية المتاحة</h3>
                            <div class="command-syntax">
                                GET /api/stats
                                GET /api/servers
                                POST /api/settings
                            </div>

                            <h3>المصادقة</h3>
                            <p>يتطلب API مفتاح مصادقة صالح في رأس الطلب.</p>

                            <div class="command-syntax">
                                Authorization: Bearer YOUR_API_KEY
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showSection(sectionId) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.remove('active');
            });

            // إزالة التحديد من جميع العناصر
            document.querySelectorAll('.docs-nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // إظهار القسم المحدد
            document.getElementById(sectionId).classList.add('active');

            // تحديد العنصر النشط
            event.target.classList.add('active');

            // التمرير لأعلى المحتوى
            document.querySelector('.docs-content').scrollTop = 0;
        }
    </script>
</body>
</html>
