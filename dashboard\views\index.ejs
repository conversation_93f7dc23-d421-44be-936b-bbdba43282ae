<%- include('partials/header') %>

<!-- Hero Section -->
<section class="hero-section text-center py-5 bg-gradient">
    <div class="container">
        <h1 class="display-4 fw-bold mb-4">
            بوت CS – نظام شامل للسيرفرات الاحترافية
            <span class="badge bg-warning text-dark">🚀 الأسطوري</span>
        </h1>
        <p class="lead mb-4">بوت ديسكورد عام مصمم لخدمة جميع أنواع السيرفرات باحترافية، مع لوحة تحكم متقدمة وسهلة الاستخدام. مناسب لسيرفرات الألعاب، البرمجة، المجتمعات، المتاجر، وأكثر!</p>

        <!-- إحصائيات مباشرة -->
        <div class="row text-center mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card p-3 bg-white rounded shadow-sm">
                    <h3 class="text-primary mb-1" id="serverCount">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <small class="text-muted">السيرفرات</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card p-3 bg-white rounded shadow-sm">
                    <h3 class="text-success mb-1" id="userCount">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <small class="text-muted">المستخدمين</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card p-3 bg-white rounded shadow-sm">
                    <h3 class="text-warning mb-1" id="commandCount">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <small class="text-muted">الأوامر المنفذة</small>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-card p-3 bg-white rounded shadow-sm">
                    <h3 class="text-info mb-1" id="uptime">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <small class="text-muted">وقت التشغيل</small>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="https://discord.com/api/oauth2/authorize?client_id=<%= config.clientId %>&permissions=8&scope=bot%20applications.commands" class="btn btn-primary btn-lg" target="_blank">
                <i class="fas fa-plus-circle"></i> أضف البوت
            </a>
            <% if (user) { %>
                <a href="/dashboard" class="btn btn-success btn-lg">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                </a>
                <% if (user.premium && user.premium.enabled) { %>
                    <span class="btn btn-warning btn-lg">
                        <i class="fas fa-crown"></i> عضو مميز
                    </span>
                <% } %>
            <% } else { %>
                <a href="/auth/login" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </a>
            <% } %>
        </div>
    </div>
</section>

<!-- تحديث الإحصائيات بـ JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الإحصائيات
    function updateStats() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                document.getElementById('serverCount').textContent = data.servers || '0';
                document.getElementById('userCount').textContent = data.users || '0';
                document.getElementById('commandCount').textContent = data.commands || '0';
                document.getElementById('uptime').textContent = data.uptime || '0m';
            })
            .catch(error => {
                console.error('خطأ في تحميل الإحصائيات:', error);
                document.getElementById('serverCount').innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                document.getElementById('userCount').innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                document.getElementById('commandCount').innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                document.getElementById('uptime').innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            });
    }

    // تحديث فوري
    updateStats();

    // تحديث كل 30 ثانية
    setInterval(updateStats, 30000);
});
</script>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">🧩 الأنظمة المدعومة داخل البوت</h2>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-door-open fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">نظام الترحيب</h3>
                        <p class="card-text">رسائل ترحيب قابلة للتخصيص + صور ترحيبية تلقائية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-ticket-alt fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">نظام التذاكر</h3>
                        <p class="card-text">نظام تذاكر احترافي لدعم الأعضاء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-history fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">نظام اللوجات</h3>
                        <p class="card-text">لوج لكل شيء: بان، كيك، رول، رسائل، ردود، بوتات… إلخ</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-tag fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">الأدوار التلقائية</h3>
                        <p class="card-text">تعيين رتب تلقائيًا للأعضاء الجدد</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-reply-all fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">الردود التلقائية</h3>
                        <p class="card-text">ردود ذكية على كلمات محددة يحددها الأدمين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-coins fa-3x mb-3 text-primary"></i>
                        <h3 class="card-title">نظام العملات</h3>
                        <p class="card-text">عملة مخصصة، تحويل، شغل، سرقة، متجر، يومي، وأكثر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Features -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">🛠️ مميزات لوحة التحكم</h2>
        <div class="row align-items-center">
            <div class="col-md-6">
                <img src="/img/dashboard-preview.png" alt="لوحة التحكم" class="img-fluid rounded shadow">
            </div>
            <div class="col-md-6">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> تسجيل دخول آمن بـ OAuth2 من ديسكورد</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> تحكم كامل في كل إعدادات السيرفر (بضغطة زر)</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> إمكانية تشغيل/إيقاف الأنظمة حسب رغبة صاحب السيرفر</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> تعديل الرسائل بسهولة (ترحيب، مغادرة، ردود)</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> التحكم في الرولات، اللوجات، التذاكر، العملات، الردود التلقائية</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> نظام حماية من الضغط/السبام (rate limit لكل مستخدم كل 20 ثانية)</li>
                    <li class="list-group-item bg-transparent"><i class="fas fa-check-circle text-success"></i> واجهة موبايل متوافقة + تصميم احترافي + سرعة استجابة عالية</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Premium Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">💎 نظام البريميوم</h2>
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <p class="lead mb-4">نظام البريميوم في بوت CS يسمح لك بإعطاء صلاحيات مميزة لأي مستخدم، بإدخال الأمر:</p>
                <div class="bg-dark text-light p-3 rounded mb-4">
                    <code>!add @User 1s 1m 1d 1y</code>
                </div>
                <h4 class="mb-3">🧠 المميزات:</h4>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check-circle text-success"></i> يتم حفظ مدة البريميوم</li>
                    <li><i class="fas fa-check-circle text-success"></i> يظهر في الداشبورد كـ Premium Member</li>
                    <li><i class="fas fa-check-circle text-success"></i> يقدر يضيف توكن بوت خاص بيه واستخدام بعض المميزات الحصرية</li>
                </ul>
                <a href="/premium" class="btn btn-warning mt-3">
                    <i class="fas fa-crown"></i> احصل على البريميوم
                </a>
            </div>
        </div>
    </div>
</section>

<%- include('partials/footer') %>
