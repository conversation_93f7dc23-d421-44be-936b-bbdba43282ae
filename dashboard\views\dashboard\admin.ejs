<%- include('../partials/header') %>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><i class="fas fa-shield-alt"></i> لوحة تحكم الأدمن</h1>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> 
                <strong>تحذير:</strong> هذه الصفحة مخصصة لمطور البوت فقط. استخدم هذه الأدوات بحذر.
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-line"></i> إحصائيات البوت</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <h3 class="text-primary" id="serverCount">0</h3>
                                    <small>السيرفرات</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <h3 class="text-success" id="userCount">0</h3>
                                    <small>المستخدمين</small>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-info" id="commandCount">0</h3>
                                    <small>الأوامر المنفذة</small>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-warning" id="uptime">0</h3>
                                    <small>وقت التشغيل</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-crown"></i> إدارة البريميوم</h5>
                        </div>
                        <div class="card-body">
                            <form id="premiumForm">
                                <div class="mb-3">
                                    <label for="userId" class="form-label">معرف المستخدم</label>
                                    <input type="text" class="form-control" id="userId" placeholder="123456789012345678">
                                </div>
                                <div class="mb-3">
                                    <label for="duration" class="form-label">المدة</label>
                                    <select class="form-select" id="duration">
                                        <option value="1d">يوم واحد</option>
                                        <option value="7d">أسبوع</option>
                                        <option value="30d">شهر</option>
                                        <option value="365d">سنة</option>
                                    </select>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" id="addPremiumBtn">
                                        <i class="fas fa-plus"></i> إضافة بريميوم
                                    </button>
                                    <button type="button" class="btn btn-danger" id="removePremiumBtn">
                                        <i class="fas fa-minus"></i> إزالة بريميوم
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-broadcast-tower"></i> إرسال إعلان</h5>
                        </div>
                        <div class="card-body">
                            <form id="announcementForm">
                                <div class="mb-3">
                                    <label for="announcementTitle" class="form-label">العنوان</label>
                                    <input type="text" class="form-control" id="announcementTitle" placeholder="عنوان الإعلان">
                                </div>
                                <div class="mb-3">
                                    <label for="announcementMessage" class="form-label">الرسالة</label>
                                    <textarea class="form-control" id="announcementMessage" rows="3" placeholder="محتوى الإعلان"></textarea>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sendToAll">
                                        <label class="form-check-label" for="sendToAll">
                                            إرسال لجميع السيرفرات
                                        </label>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-warning" id="sendAnnouncementBtn">
                                    <i class="fas fa-paper-plane"></i> إرسال الإعلان
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-tools"></i> أدوات النظام</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-primary" id="reloadCommandsBtn">
                                    <i class="fas fa-sync"></i> إعادة تحميل الأوامر
                                </button>
                                <button type="button" class="btn btn-outline-success" id="backupDbBtn">
                                    <i class="fas fa-download"></i> نسخ احتياطي لقاعدة البيانات
                                </button>
                                <button type="button" class="btn btn-outline-warning" id="clearCacheBtn">
                                    <i class="fas fa-trash"></i> مسح الذاكرة المؤقتة
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="restartBotBtn">
                                    <i class="fas fa-power-off"></i> إعادة تشغيل البوت
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> آخر الأنشطة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody id="activityLog">
                                <tr>
                                    <td colspan="4" class="text-center text-muted">لا توجد أنشطة حديثة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الإحصائيات
    loadStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(loadStats, 30000);
    
    // إضافة بريميوم
    document.getElementById('addPremiumBtn').addEventListener('click', function() {
        const userId = document.getElementById('userId').value;
        const duration = document.getElementById('duration').value;
        
        if (!userId) {
            alert('يرجى إدخال معرف المستخدم');
            return;
        }
        
        // إرسال طلب إضافة بريميوم
        fetch('/api/admin/premium/add', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId, duration })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إضافة البريميوم بنجاح!');
                document.getElementById('userId').value = '';
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        });
    });
    
    // إزالة بريميوم
    document.getElementById('removePremiumBtn').addEventListener('click', function() {
        const userId = document.getElementById('userId').value;
        
        if (!userId) {
            alert('يرجى إدخال معرف المستخدم');
            return;
        }
        
        // إرسال طلب إزالة بريميوم
        fetch('/api/admin/premium/remove', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إزالة البريميوم بنجاح!');
                document.getElementById('userId').value = '';
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        });
    });
    
    // إرسال إعلان
    document.getElementById('sendAnnouncementBtn').addEventListener('click', function() {
        const title = document.getElementById('announcementTitle').value;
        const message = document.getElementById('announcementMessage').value;
        const sendToAll = document.getElementById('sendToAll').checked;
        
        if (!title || !message) {
            alert('يرجى ملء جميع الحقول');
            return;
        }
        
        // إرسال الإعلان
        fetch('/api/admin/announcement', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ title, message, sendToAll })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال الإعلان بنجاح!');
                document.getElementById('announcementTitle').value = '';
                document.getElementById('announcementMessage').value = '';
            } else {
                alert('حدث خطأ: ' + data.error);
            }
        });
    });
    
    function loadStats() {
        fetch('/api/admin/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('serverCount').textContent = data.servers || 0;
            document.getElementById('userCount').textContent = data.users || 0;
            document.getElementById('commandCount').textContent = data.commands || 0;
            document.getElementById('uptime').textContent = data.uptime || '0m';
        })
        .catch(error => console.error('Error loading stats:', error));
    }
});
</script>

<%- include('../partials/footer') %>
