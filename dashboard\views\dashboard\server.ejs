<%- include(__dirname + '/../partials/header') %>

<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-server"></i> <%= guild.name %></h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/dashboard/server/<%= guild.id %>" class="list-group-item list-group-item-action active">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/welcome" class="list-group-item list-group-item-action">
                        <i class="fas fa-door-open"></i> الترحيب
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/logs" class="list-group-item list-group-item-action">
                        <i class="fas fa-history"></i> اللوجات
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/tickets" class="list-group-item list-group-item-action">
                        <i class="fas fa-ticket-alt"></i> التذاكر
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/autorole" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-tag"></i> الرتب التلقائية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/autoresponse" class="list-group-item list-group-item-action">
                        <i class="fas fa-reply-all"></i> الردود التلقائية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/economy" class="list-group-item list-group-item-action">
                        <i class="fas fa-coins"></i> الاقتصاد
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/antispam" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> مانع السبام
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات السيرفر</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <% if (guild.icon) { %>
                                <img src="https://cdn.discordapp.com/icons/<%= guild.id %>/<%= guild.icon %>.png" alt="<%= guild.name %>" class="img-fluid rounded-circle mb-3" style="max-width: 120px;">
                            <% } else { %>
                                <div class="server-icon-placeholder mb-3" style="width: 120px; height: 120px; font-size: 48px;">
                                    <%= guild.name.charAt(0) %>
                                </div>
                            <% } %>
                        </div>
                        <div class="col-md-9">
                            <h4><%= guild.name %></h4>
                            <p class="text-muted">ID: <%= guild.id %></p>
                            <p>البادئة: <code><%= settings.prefix || '!' %></code></p>
                            <p>اللغة: <%= settings.language === 'ar' ? 'العربية' : 'English' %></p>

                            <div class="mt-3">
                                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#settingsModal">
                                    <i class="fas fa-cog"></i> تعديل الإعدادات الأساسية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card bg-primary text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5 class="card-title">الأعضاء</h5>
                            <p class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-success text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                            <h5 class="card-title">التذاكر</h5>
                            <p class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card bg-info text-white h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-terminal fa-3x mb-3"></i>
                            <h5 class="card-title">الأوامر</h5>
                            <p class="card-text display-6">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Systems Status -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-toggle-on"></i> حالة الأنظمة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-door-open"></i> نظام الترحيب</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="welcomeToggle" <%= settings.welcome && settings.welcome.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-history"></i> نظام اللوجات</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="logsToggle" <%= settings.logs && settings.logs.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-ticket-alt"></i> نظام التذاكر</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="ticketsToggle" <%= settings.tickets && settings.tickets.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-user-tag"></i> الرتب التلقائية</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoRoleToggle" <%= settings.autoRole && settings.autoRole.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-coins"></i> نظام الاقتصاد</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="economyToggle" <%= settings.economy && settings.economy.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-shield-alt"></i> مانع السبام</span>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="antiSpamToggle" <%= settings.antiSpam && settings.antiSpam.enabled ? 'checked' : '' %>>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button id="saveSystemsBtn" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="settingsModalLabel">الإعدادات الأساسية</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="basicSettingsForm">
                    <div class="mb-3">
                        <label for="prefix" class="form-label">البادئة</label>
                        <input type="text" class="form-control" id="prefix" value="<%= settings.prefix || '!' %>" maxlength="3">
                        <div class="form-text">البادئة المستخدمة لأوامر البوت (مثال: !، $، #)</div>
                    </div>
                    <div class="mb-3">
                        <label for="language" class="form-label">اللغة</label>
                        <select class="form-select" id="language">
                            <option value="ar" <%= settings.language === 'ar' ? 'selected' : '' %>>العربية</option>
                            <option value="en" <%= settings.language === 'en' ? 'selected' : '' %>>English</option>
                        </select>
                        <div class="form-text">لغة البوت في هذا السيرفر</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveSettingsBtn">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom JS for this page -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حفظ الإعدادات الأساسية
        document.getElementById('saveSettingsBtn').addEventListener('click', function() {
            const prefix = document.getElementById('prefix').value;
            const language = document.getElementById('language').value;

            // إرسال البيانات إلى الخادم
            fetch('/api/guilds/<%= guild.id %>/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prefix,
                    language
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ الإعدادات بنجاح!');
                    $('#settingsModal').modal('hide');
                    // تحديث الصفحة
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء حفظ الإعدادات: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ الإعدادات');
            });
        });

        // حفظ حالة الأنظمة
        document.getElementById('saveSystemsBtn').addEventListener('click', function() {
            const welcomeEnabled = document.getElementById('welcomeToggle').checked;
            const logsEnabled = document.getElementById('logsToggle').checked;
            const ticketsEnabled = document.getElementById('ticketsToggle').checked;
            const autoRoleEnabled = document.getElementById('autoRoleToggle').checked;
            const economyEnabled = document.getElementById('economyToggle').checked;
            const antiSpamEnabled = document.getElementById('antiSpamToggle').checked;

            // إرسال البيانات إلى الخادم
            fetch('/api/guilds/<%= guild.id %>/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    welcome: {
                        enabled: welcomeEnabled
                    },
                    logs: {
                        enabled: logsEnabled
                    },
                    tickets: {
                        enabled: ticketsEnabled
                    },
                    autoRole: {
                        enabled: autoRoleEnabled
                    },
                    economy: {
                        enabled: economyEnabled
                    },
                    antiSpam: {
                        enabled: antiSpamEnabled
                    }
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ حالة الأنظمة بنجاح!');
                } else {
                    alert('حدث خطأ أثناء حفظ حالة الأنظمة: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ حالة الأنظمة');
            });
        });
    });
</script>

<%- include(__dirname + '/../partials/footer') %>
