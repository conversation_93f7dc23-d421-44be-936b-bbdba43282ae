<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيرفر | CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .server-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .server-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(255,255,255,0.3);
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .quick-settings {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس السيرفر -->
            <div class="server-header">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <% if (guild && guild.icon) { %>
                            <img src="https://cdn.discordapp.com/icons/<%= guild.id %>/<%= guild.icon %>.png" 
                                 alt="<%= guild.name %>" class="server-icon">
                        <% } else { %>
                            <div class="server-icon bg-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-server fa-2x text-white"></i>
                            </div>
                        <% } %>
                    </div>
                    <div class="col">
                        <h1 class="text-white mb-2"><%= guild ? guild.name : 'السيرفر' %></h1>
                        <p class="text-white-50 mb-0">
                            <i class="fas fa-users me-2"></i><%= guild ? (guild.memberCount || 0) : 0 %> عضو
                            <i class="fas fa-hashtag me-2 ms-3"></i><%= guild ? (guild.channelCount || 0) : 0 %> قناة
                            <i class="fas fa-shield-alt me-2 ms-3"></i><%= guild ? (guild.roleCount || 0) : 0 %> رتبة
                        </p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button class="btn btn-light" onclick="saveAllSettings()">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                            <button class="btn btn-outline-light" onclick="resetSettings()">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات السيرفر -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div>الأوامر المستخدمة اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div>الرسائل هذا الأسبوع</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div>الأعضاء الجدد</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div>وقت التشغيل</div>
                </div>
            </div>

            <div class="row">
                <!-- الإعدادات السريعة -->
                <div class="col-lg-4">
                    <div class="quick-settings">
                        <h3 class="text-white mb-4">
                            <i class="fas fa-bolt me-2"></i>الإعدادات السريعة
                        </h3>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-white">رسائل الترحيب</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.welcome && settings.welcome.enabled ? 'checked' : '' %> 
                                       onchange="toggleFeature('welcome', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-white">رسائل المغادرة</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.leave && settings.leave.enabled ? 'checked' : '' %>
                                       onchange="toggleFeature('leave', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-white">مانع السبام</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.antispam && settings.antispam.enabled ? 'checked' : '' %>
                                       onchange="toggleFeature('antispam', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-white">الرتب التلقائية</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.autorole && settings.autorole.enabled ? 'checked' : '' %>
                                       onchange="toggleFeature('autorole', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-white">سجل الأحداث</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.logs && settings.logs.enabled ? 'checked' : '' %>
                                       onchange="toggleFeature('logs', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-white">الاقتصاد</span>
                            <label class="toggle-switch">
                                <input type="checkbox" <%= settings && settings.economy && settings.economy.enabled ? 'checked' : '' %>
                                       onchange="toggleFeature('economy', this.checked)">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- الميزات المتقدمة -->
                <div class="col-lg-8">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-success">
                                    <i class="fas fa-hand-wave"></i>
                                </div>
                                <h5>رسائل الترحيب</h5>
                                <p class="text-muted mb-3">رحب بالأعضاء الجدد برسائل مخصصة</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/welcome" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-danger">
                                    <i class="fas fa-door-open"></i>
                                </div>
                                <h5>رسائل المغادرة</h5>
                                <p class="text-muted mb-3">ودع الأعضاء المغادرين برسائل مخصصة</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/leave" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-warning">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h5>مانع السبام</h5>
                                <p class="text-muted mb-3">احم سيرفرك من الرسائل المزعجة</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/antispam" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-info">
                                    <i class="fas fa-user-tag"></i>
                                </div>
                                <h5>الرتب التلقائية</h5>
                                <p class="text-muted mb-3">أعط رتب تلقائية للأعضاء الجدد</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/autorole" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-secondary">
                                    <i class="fas fa-list-alt"></i>
                                </div>
                                <h5>سجل الأحداث</h5>
                                <p class="text-muted mb-3">تتبع جميع أحداث السيرفر</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/logs" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="feature-card">
                                <div class="feature-icon bg-success">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <h5>نظام الاقتصاد</h5>
                                <p class="text-muted mb-3">أضف نظام نقاط وعملة للسيرفر</p>
                                <a href="/dashboard/server/<%= guild ? guild.id : '' %>/economy" class="btn btn-primary">
                                    <i class="fas fa-cog me-2"></i>إعداد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل الميزات
        async function toggleFeature(feature, enabled) {
            try {
                const guildId = '<%= guild ? guild.id : "" %>';
                if (!guildId) return;

                const response = await fetch(`/dashboard/server/${guildId}/save`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        path: `${feature}.enabled`,
                        value: enabled
                    })
                });

                const result = await response.json();
                if (result.success) {
                    showToast(`تم ${enabled ? 'تفعيل' : 'إلغاء'} ${getFeatureName(feature)}`, 'success');
                } else {
                    showToast('حدث خطأ في حفظ الإعدادات', 'error');
                }
            } catch (error) {
                console.error('خطأ في تبديل الميزة:', error);
                showToast('حدث خطأ في الاتصال', 'error');
            }
        }

        function getFeatureName(feature) {
            const names = {
                'welcome': 'رسائل الترحيب',
                'leave': 'رسائل المغادرة',
                'antispam': 'مانع السبام',
                'autorole': 'الرتب التلقائية',
                'logs': 'سجل الأحداث',
                'economy': 'نظام الاقتصاد'
            };
            return names[feature] || feature;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function saveAllSettings() {
            showToast('تم حفظ جميع الإعدادات', 'success');
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                showToast('تم إعادة تعيين الإعدادات', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }
    </script>
</body>
</html>
