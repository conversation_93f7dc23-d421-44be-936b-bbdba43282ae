<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيرفر - <%= guild.name %> | CS Bot</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .server-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .server-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .settings-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس السيرفر -->
            <div class="server-header">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <% if (guild.icon) { %>
                            <img src="https://cdn.discordapp.com/icons/<%= guild.id %>/<%= guild.icon %>.png" 
                                 alt="<%= guild.name %>" class="server-icon">
                        <% } else { %>
                            <div class="server-icon bg-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-server fa-2x text-white"></i>
                            </div>
                        <% } %>
                    </div>
                    <div class="col">
                        <h1 class="text-white mb-2">
                            <i class="fas fa-cogs me-2"></i>
                            إدارة السيرفر: <%= guild.name %>
                        </h1>
                        <p class="text-white-50 mb-0">
                            <i class="fas fa-id-card me-2"></i>
                            معرف السيرفر: <code><%= guild.id %></code>
                        </p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button class="btn btn-light" onclick="refreshServerData()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                            <button class="btn btn-warning" onclick="resetSettings()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات السيرفر -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><%= guild.memberCount || 0 %></div>
                    <div>الأعضاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= guild.channels?.cache?.size || 0 %></div>
                    <div>القنوات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= guild.roles?.cache?.size || 0 %></div>
                    <div>الرتب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= guild.premiumTier || 0 %></div>
                    <div>مستوى البوست</div>
                </div>
            </div>

            <!-- الإعدادات السريعة -->
            <div class="settings-section">
                <h3 class="mb-4">
                    <i class="fas fa-sliders-h text-primary me-2"></i>
                    الإعدادات السريعة
                </h3>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">رسائل الترحيب</h6>
                                <small class="text-muted">ترحيب بالأعضاء الجدد</small>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="welcomeEnabled" <%= settings?.welcome?.enabled ? 'checked' : '' %>>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">رسائل المغادرة</h6>
                                <small class="text-muted">وداع للأعضاء المغادرين</small>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="leaveEnabled" <%= settings?.leave?.enabled ? 'checked' : '' %>>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">نظام اللوجات</h6>
                                <small class="text-muted">تسجيل أحداث السيرفر</small>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="logsEnabled" <%= settings?.logs?.enabled ? 'checked' : '' %>>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">مانع السبام</h6>
                                <small class="text-muted">حماية من الرسائل المزعجة</small>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="antispamEnabled" <%= settings?.antispam?.enabled ? 'checked' : '' %>>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الميزات المتقدمة -->
            <div class="row">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <i class="fas fa-hand-wave"></i>
                        </div>
                        <h5>رسائل الترحيب</h5>
                        <p class="text-muted">إعداد رسائل ترحيب مخصصة للأعضاء الجدد</p>
                        <a href="/dashboard/server/<%= guild.id %>/welcome" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <h5>رسائل المغادرة</h5>
                        <p class="text-muted">رسائل وداع للأعضاء المغادرين</p>
                        <a href="/dashboard/server/<%= guild.id %>/leave" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h5>نظام اللوجات</h5>
                        <p class="text-muted">تسجيل وتتبع أحداث السيرفر</p>
                        <a href="/dashboard/server/<%= guild.id %>/logs" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h5>نظام التذاكر</h5>
                        <p class="text-muted">إنشاء وإدارة تذاكر الدعم</p>
                        <a href="/dashboard/server/<%= guild.id %>/tickets" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <h5>الرتب التلقائية</h5>
                        <p class="text-muted">إعطاء رتب تلقائية للأعضاء الجدد</p>
                        <a href="/dashboard/server/<%= guild.id %>/autorole" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h5>الردود التلقائية</h5>
                        <p class="text-muted">ردود آلية على كلمات معينة</p>
                        <a href="/dashboard/server/<%= guild.id %>/autoresponse" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-1"></i> إعداد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // حفظ الإعدادات السريعة
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                const setting = this.id.replace('Enabled', '');
                const enabled = this.checked;
                
                saveQuickSetting(setting, enabled);
            });
        });

        function saveQuickSetting(setting, enabled) {
            fetch(`/dashboard/server/<%= guild.id %>/save`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    [setting]: { enabled }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم حفظ الإعدادات بنجاح', 'success');
                } else {
                    showNotification('حدث خطأ في حفظ الإعدادات', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('حدث خطأ في حفظ الإعدادات', 'error');
            });
        }

        function refreshServerData() {
            location.reload();
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
                showNotification('تم إعادة تعيين الإعدادات', 'info');
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
