/**
 * CS Bot - المسارات الرئيسية
 *
 * هذا الملف يحتوي على مسارات الصفحة الرئيسية والصفحات العامة
 */

const express = require('express');
const router = express.Router();

// الصفحة الرئيسية
router.get('/', (req, res) => {
    res.render('index');
});

// صفحة السيرفرات تم نقلها إلى الداشبورد المحمي

// لوحة تحكم المستخدمين العاديين
router.get('/user-dashboard', (req, res) => {
    // التحقق من تسجيل الدخول
    if (!req.session || !req.session.user) {
        return res.redirect('/auth/login');
    }

    // التحقق من أن المستخدم ليس مطور (المطورين يذهبون للداشبورد الكامل)
    if (req.session.user.isOwner) {
        return res.redirect('/dashboard');
    }

    res.render('user-dashboard', {
        user: req.session.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة اختبار إعدادات Discord (للتطوير فقط)
router.get('/test-discord', (req, res) => {
    const config = req.app.locals.config;

    res.send(`
        <div style="font-family: Arial; padding: 20px;">
            <h2>اختبار إعدادات Discord OAuth</h2>
            <p><strong>Client ID:</strong> ${config.clientId}</p>
            <p><strong>Callback URL:</strong> ${config.dashboard.callbackURL}</p>
            <p><strong>Owner ID:</strong> ${config.owner.id}</p>

            <h3>رابط OAuth:</h3>
            <a href="/auth/discord" style="background: #5865F2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                اختبار تسجيل الدخول
            </a>

            <h3>ملاحظات:</h3>
            <ul>
                <li>تأكد من أن Client ID صحيح</li>
                <li>تأكد من أن Callback URL مضاف في Discord Developer Portal</li>
                <li>تأكد من أن Owner ID هو معرفك الصحيح</li>
            </ul>
        </div>
    `);
});

// صفحة الميزات
router.get('/features', (req, res) => {
    res.render('features', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الأوامر
router.get('/commands', (req, res) => {
    res.render('commands', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الدعم
router.get('/support', (req, res) => {
    res.render('support', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة البريميوم
router.get('/premium', (req, res) => {
    res.render('premium', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة اختبار بسيطة
router.get('/test-simple', (req, res) => {
    res.render('index-simple');
});

module.exports = router;
