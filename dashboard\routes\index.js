/**
 * CS Bot - المسارات الرئيسية
 *
 * هذا الملف يحتوي على مسارات الصفحة الرئيسية والصفحات العامة
 */

const express = require('express');
const router = express.Router();

// الصفحة الرئيسية
router.get('/', (req, res) => {
    res.render('index');
});

// صفحة الميزات
router.get('/features', (req, res) => {
    res.render('features', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الأوامر
router.get('/commands', (req, res) => {
    res.render('commands', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الدعم
router.get('/support', (req, res) => {
    res.render('support', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة البريميوم
router.get('/premium', (req, res) => {
    res.render('premium', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة اختبار بسيطة
router.get('/test-simple', (req, res) => {
    res.render('index-simple');
});

module.exports = router;
