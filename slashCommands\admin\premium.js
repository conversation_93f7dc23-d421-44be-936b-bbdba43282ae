/**
 * Slash Command لإدارة البريميوم
 */

const { SlashCommandBuilder } = require('discord.js');
const { getUserData } = require('../../models/user');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('premium')
        .setDescription('إدارة البريميوم للمستخدمين')
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('إضافة بريميوم لمستخدم')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('المستخدم المراد إضافة البريميوم له')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('duration')
                        .setDescription('مدة البريميوم')
                        .setRequired(true)
                        .addChoices(
                            { name: '1 دقيقة', value: '1m' },
                            { name: '1 ساعة', value: '1h' },
                            { name: '1 يوم', value: '1d' },
                            { name: '1 أسبوع', value: '7d' },
                            { name: '1 شهر', value: '30d' },
                            { name: '1 سنة', value: '365d' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('إزالة البريميوم من مستخدم')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('المستخدم المراد إزالة البريميوم منه')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('check')
                .setDescription('فحص حالة البريميوم لمستخدم')
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('المستخدم المراد فحص بريميومه')
                        .setRequired(false))),

    async execute(interaction) {
        try {
            // التحقق من الصلاحيات
            if (interaction.user.id !== interaction.client.config.owner.id) {
                return interaction.reply({
                    content: '❌ هذا الأمر مخصص للمطور فقط!',
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();

            if (subcommand === 'add') {
                await handleAddPremium(interaction);
            } else if (subcommand === 'remove') {
                await handleRemovePremium(interaction);
            } else if (subcommand === 'check') {
                await handleCheckPremium(interaction);
            }

        } catch (error) {
            console.error('خطأ في slash command premium:', error);
            await interaction.reply({
                content: '❌ حدث خطأ أثناء تنفيذ الأمر!',
                ephemeral: true
            });
        }
    }
};

async function handleAddPremium(interaction) {
    const user = interaction.options.getUser('user');
    const duration = interaction.options.getString('duration');

    // حساب تاريخ الانتهاء
    const now = new Date();
    const expiresAt = new Date(now);

    const durationMap = {
        '1m': () => expiresAt.setMinutes(expiresAt.getMinutes() + 1),
        '1h': () => expiresAt.setHours(expiresAt.getHours() + 1),
        '1d': () => expiresAt.setDate(expiresAt.getDate() + 1),
        '7d': () => expiresAt.setDate(expiresAt.getDate() + 7),
        '30d': () => expiresAt.setDate(expiresAt.getDate() + 30),
        '365d': () => expiresAt.setDate(expiresAt.getDate() + 365)
    };

    durationMap[duration]();

    // تحديث بيانات المستخدم
    const userData = await getUserData(user.id, user.username, user.discriminator, user.avatar);
    userData.premium = {
        enabled: true,
        expiresAt: expiresAt,
        tier: 'premium',
        addedBy: interaction.user.id,
        addedAt: new Date()
    };

    await userData.save();

    // إرسال رد
    const embed = {
        color: 0xFFD700,
        title: '👑 تم إضافة البريميوم بنجاح!',
        description: `تم إضافة البريميوم للمستخدم ${user} بنجاح!`,
        fields: [
            {
                name: '👤 المستخدم',
                value: `${user.tag}`,
                inline: true
            },
            {
                name: '⏰ المدة',
                value: duration,
                inline: true
            },
            {
                name: '📅 ينتهي في',
                value: `<t:${Math.floor(expiresAt.getTime() / 1000)}:R>`,
                inline: false
            }
        ],
        timestamp: new Date()
    };

    await interaction.reply({ embeds: [embed] });
}

async function handleRemovePremium(interaction) {
    const user = interaction.options.getUser('user');

    // تحديث بيانات المستخدم
    const userData = await getUserData(user.id, user.username, user.discriminator, user.avatar);
    
    if (!userData.premium || !userData.premium.enabled) {
        return interaction.reply({
            content: '❌ هذا المستخدم لا يملك بريميوم!',
            ephemeral: true
        });
    }

    userData.premium = {
        enabled: false,
        expiresAt: null,
        tier: null,
        removedBy: interaction.user.id,
        removedAt: new Date()
    };

    await userData.save();

    const embed = {
        color: 0xFF0000,
        title: '🚫 تم إزالة البريميوم',
        description: `تم إزالة البريميوم من المستخدم ${user} بنجاح!`,
        fields: [
            {
                name: '👤 المستخدم',
                value: `${user.tag}`,
                inline: true
            }
        ],
        timestamp: new Date()
    };

    await interaction.reply({ embeds: [embed] });
}

async function handleCheckPremium(interaction) {
    const user = interaction.options.getUser('user') || interaction.user;
    const userData = await getUserData(user.id, user.username, user.discriminator, user.avatar);

    const embed = {
        color: userData.premium?.enabled ? 0xFFD700 : 0x808080,
        title: `👑 حالة البريميوم - ${user.username}`,
        fields: [
            {
                name: '📊 الحالة',
                value: userData.premium?.enabled ? '✅ نشط' : '❌ غير نشط',
                inline: true
            }
        ],
        thumbnail: {
            url: user.displayAvatarURL()
        },
        timestamp: new Date()
    };

    if (userData.premium?.enabled) {
        embed.fields.push(
            {
                name: '📅 ينتهي في',
                value: `<t:${Math.floor(userData.premium.expiresAt.getTime() / 1000)}:R>`,
                inline: true
            },
            {
                name: '🏆 النوع',
                value: userData.premium.tier || 'premium',
                inline: true
            }
        );
    }

    await interaction.reply({ embeds: [embed] });
}
