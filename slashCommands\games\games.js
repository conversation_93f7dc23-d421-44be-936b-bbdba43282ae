/**
 * Slash Command للألعاب - CS Bot
 */

const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const User = global.User || require('../../models/User');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('games')
        .setDescription('Interactive games with rewards')
        .addSubcommand(subcommand =>
            subcommand
                .setName('rps')
                .setDescription('Play Rock Paper Scissors against the bot')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('trivia')
                .setDescription('Answer trivia questions')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('guess')
                .setDescription('Guess the secret number')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('slots')
                .setDescription('Try your luck with slot machine')
                .addIntegerOption(option =>
                    option
                        .setName('bet')
                        .setDescription('Amount to bet')
                        .setRequired(false)
                        .setMinValue(1)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('math')
                .setDescription('Solve math challenges')
        ),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'rps':
                return this.handleRPS(interaction);
            case 'trivia':
                return this.handleTrivia(interaction);
            case 'guess':
                return this.handleGuess(interaction);
            case 'slots':
                return this.handleSlots(interaction);
            case 'math':
                return this.handleMath(interaction);
        }
    },

    async handleRPS(interaction) {
        const choices = ['🗿', '📄', '✂️'];
        const choiceNames = ['Rock', 'Paper', 'Scissors'];
        
        const embed = new EmbedBuilder()
            .setTitle('✂️ Rock Paper Scissors')
            .setDescription('Choose your move:')
            .setColor('#4ecdc4');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('rps_rock')
                    .setLabel('🗿 Rock')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('rps_paper')
                    .setLabel('📄 Paper')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('rps_scissors')
                    .setLabel('✂️ Scissors')
                    .setStyle(ButtonStyle.Secondary)
            );

        const msg = await interaction.reply({ embeds: [embed], components: [row] });

        const collector = msg.createMessageComponentCollector({
            filter: i => i.user.id === interaction.user.id,
            time: 30000
        });

        collector.on('collect', async (buttonInteraction) => {
            const userChoice = buttonInteraction.customId.split('_')[1];
            const userIndex = userChoice === 'rock' ? 0 : userChoice === 'paper' ? 1 : 2;
            const botIndex = Math.floor(Math.random() * 3);

            let result;
            if (userIndex === botIndex) {
                result = 'It\'s a tie! 🤝';
            } else if (
                (userIndex === 0 && botIndex === 2) ||
                (userIndex === 1 && botIndex === 0) ||
                (userIndex === 2 && botIndex === 1)
            ) {
                result = 'You win! 🎉';
                // Add rewards
                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { 
                        $inc: { 
                            'economy.balance': 50,
                            'economy.xp': 10,
                            'stats.gamesWon': 1
                        }
                    },
                    { upsert: true }
                );
            } else {
                result = 'You lose! 😢';
                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { $inc: { 'stats.gamesLost': 1 } },
                    { upsert: true }
                );
            }

            const resultEmbed = new EmbedBuilder()
                .setTitle('✂️ Rock Paper Scissors Result')
                .addFields([
                    {
                        name: 'Your Choice',
                        value: `${choices[userIndex]} ${choiceNames[userIndex]}`,
                        inline: true
                    },
                    {
                        name: 'Bot\'s Choice',
                        value: `${choices[botIndex]} ${choiceNames[botIndex]}`,
                        inline: true
                    },
                    {
                        name: 'Result',
                        value: result,
                        inline: false
                    }
                ])
                .setColor(result.includes('win') ? '#00ff00' : result.includes('lose') ? '#ff0000' : '#ffff00');

            await buttonInteraction.update({ embeds: [resultEmbed], components: [] });
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ Time\'s up!')
                    .setDescription('You didn\'t choose in time')
                    .setColor('#ff9900');
                await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
            }
        });
    },

    async handleTrivia(interaction) {
        const questions = [
            {
                question: 'What is the capital of France?',
                options: ['London', 'Paris', 'Rome', 'Berlin'],
                correct: 1,
                category: 'Geography'
            },
            {
                question: 'How many days are in a leap year?',
                options: ['365', '366', '364', '367'],
                correct: 1,
                category: 'General'
            },
            {
                question: 'Who founded Microsoft?',
                options: ['Steve Jobs', 'Bill Gates', 'Mark Zuckerberg', 'Elon Musk'],
                correct: 1,
                category: 'Technology'
            },
            {
                question: 'What is the largest planet in our solar system?',
                options: ['Earth', 'Mars', 'Jupiter', 'Saturn'],
                correct: 2,
                category: 'Science'
            }
        ];

        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        const emojis = ['🇦', '🇧', '🇨', '🇩'];

        const embed = new EmbedBuilder()
            .setTitle('🧠 Trivia Question')
            .setDescription(`**Category:** ${randomQuestion.category}\n\n**${randomQuestion.question}**`)
            .setColor('#9b59b6');

        const row = new ActionRowBuilder();
        randomQuestion.options.forEach((option, index) => {
            embed.addFields([
                {
                    name: `${emojis[index]} Option ${index + 1}`,
                    value: option,
                    inline: true
                }
            ]);

            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`trivia_${index}`)
                    .setLabel(`${emojis[index]} ${option}`)
                    .setStyle(ButtonStyle.Secondary)
            );
        });

        const msg = await interaction.reply({ embeds: [embed], components: [row] });

        const collector = msg.createMessageComponentCollector({
            filter: i => i.user.id === interaction.user.id,
            time: 30000
        });

        collector.on('collect', async (buttonInteraction) => {
            const answerIndex = parseInt(buttonInteraction.customId.split('_')[1]);
            const isCorrect = answerIndex === randomQuestion.correct;

            let resultEmbed;
            if (isCorrect) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('🎉 Correct Answer!')
                    .setDescription(`Well done! The correct answer is: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#00ff00');

                // Add rewards
                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { 
                        $inc: { 
                            'economy.balance': 100,
                            'economy.xp': 25,
                            'stats.triviaCorrect': 1
                        }
                    },
                    { upsert: true }
                );
            } else {
                resultEmbed = new EmbedBuilder()
                    .setTitle('❌ Wrong Answer')
                    .setDescription(`Sorry, the correct answer is: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { $inc: { 'stats.triviaWrong': 1 } },
                    { upsert: true }
                );
            }

            await buttonInteraction.update({ embeds: [resultEmbed], components: [] });
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ Time\'s up!')
                    .setDescription(`The correct answer was: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#ff9900');
                await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
            }
        });
    },

    async handleGuess(interaction) {
        const secretNumber = Math.floor(Math.random() * 100) + 1;
        let attempts = 0;
        const maxAttempts = 7;

        const embed = new EmbedBuilder()
            .setTitle('🔢 Number Guessing Game')
            .setDescription(`Guess the number between 1 and 100!\nYou have **${maxAttempts}** attempts\n\nReply with your guess!`)
            .setColor('#3498db');

        await interaction.reply({ embeds: [embed] });

        const filter = m => m.author.id === interaction.user.id && !isNaN(m.content);
        const collector = interaction.channel.createMessageCollector({ filter, time: 120000 });

        collector.on('collect', async (msg) => {
            attempts++;
            const guess = parseInt(msg.content);

            if (guess < 1 || guess > 100) {
                return msg.reply('❌ Please enter a number between 1 and 100');
            }

            let resultEmbed;
            if (guess === secretNumber) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('🎉 Congratulations!')
                    .setDescription(`Excellent! The secret number was **${secretNumber}**\nYou got it in **${attempts}** attempts`)
                    .setColor('#00ff00');

                const reward = Math.max(200 - (attempts * 20), 50);
                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { 
                        $inc: { 
                            'economy.balance': reward,
                            'economy.xp': 30,
                            'stats.gamesWon': 1
                        }
                    },
                    { upsert: true }
                );

                resultEmbed.addFields([
                    {
                        name: '💰 Reward',
                        value: `${reward} coins`,
                        inline: true
                    }
                ]);

                collector.stop();
            } else if (attempts >= maxAttempts) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('😢 Out of attempts')
                    .setDescription(`Sorry, the secret number was **${secretNumber}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { $inc: { 'stats.gamesLost': 1 } },
                    { upsert: true }
                );

                collector.stop();
            } else {
                const hint = guess < secretNumber ? 'Higher ⬆️' : 'Lower ⬇️';
                const remaining = maxAttempts - attempts;
                
                resultEmbed = new EmbedBuilder()
                    .setTitle('🔍 Try again')
                    .setDescription(`The secret number is ${hint}\nYou have **${remaining}** attempts left`)
                    .setColor('#ffff00');
            }

            await msg.reply({ embeds: [resultEmbed] });
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time') {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ Time\'s up!')
                    .setDescription(`The secret number was **${secretNumber}**`)
                    .setColor('#ff9900');
                await interaction.followUp({ embeds: [timeoutEmbed] });
            }
        });
    },

    async handleSlots(interaction) {
        const bet = interaction.options.getInteger('bet') || 50;
        
        const userData = await User.findOne({ userId: interaction.user.id });
        if (!userData || userData.economy.balance < bet) {
            const embed = new EmbedBuilder()
                .setTitle('❌ Insufficient Balance')
                .setDescription('You don\'t have enough money to play')
                .setColor('#ff0000');
            return interaction.reply({ embeds: [embed] });
        }

        const symbols = ['🍎', '🍊', '🍋', '🍇', '🍓', '💎', '⭐', '🔔'];
        const reels = [
            symbols[Math.floor(Math.random() * symbols.length)],
            symbols[Math.floor(Math.random() * symbols.length)],
            symbols[Math.floor(Math.random() * symbols.length)]
        ];

        let multiplier = 0;
        if (reels[0] === reels[1] && reels[1] === reels[2]) {
            // Three matching
            if (reels[0] === '💎') multiplier = 10;
            else if (reels[0] === '⭐') multiplier = 8;
            else if (reels[0] === '🔔') multiplier = 6;
            else multiplier = 4;
        } else if (reels[0] === reels[1] || reels[1] === reels[2] || reels[0] === reels[2]) {
            // Two matching
            multiplier = 2;
        }

        const winnings = bet * multiplier;
        const profit = winnings - bet;

        // Update balance
        userData.economy.balance += profit;
        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle('🎰 Slot Machine')
            .setDescription(`**${reels.join(' | ')}**`)
            .addFields([
                {
                    name: '💰 Bet',
                    value: `${bet} coins`,
                    inline: true
                },
                {
                    name: '🎯 Multiplier',
                    value: `${multiplier}x`,
                    inline: true
                },
                {
                    name: profit >= 0 ? '💵 Winnings' : '💸 Loss',
                    value: `${Math.abs(profit)} coins`,
                    inline: true
                },
                {
                    name: '💰 New Balance',
                    value: `${userData.economy.balance} coins`,
                    inline: false
                }
            ])
            .setColor(profit > 0 ? '#00ff00' : profit < 0 ? '#ff0000' : '#ffff00');

        return interaction.reply({ embeds: [embed] });
    },

    async handleMath(interaction) {
        const operations = ['+', '-', '*'];
        const operation = operations[Math.floor(Math.random() * operations.length)];
        
        let num1, num2, answer;
        
        switch (operation) {
            case '+':
                num1 = Math.floor(Math.random() * 100) + 1;
                num2 = Math.floor(Math.random() * 100) + 1;
                answer = num1 + num2;
                break;
            case '-':
                num1 = Math.floor(Math.random() * 100) + 50;
                num2 = Math.floor(Math.random() * 50) + 1;
                answer = num1 - num2;
                break;
            case '*':
                num1 = Math.floor(Math.random() * 12) + 1;
                num2 = Math.floor(Math.random() * 12) + 1;
                answer = num1 * num2;
                break;
        }

        const embed = new EmbedBuilder()
            .setTitle('🔢 Math Challenge')
            .setDescription(`**${num1} ${operation} ${num2} = ?**\n\nYou have 30 seconds to answer!\nReply with your answer!`)
            .setColor('#9b59b6');

        await interaction.reply({ embeds: [embed] });

        const filter = m => m.author.id === interaction.user.id && !isNaN(m.content);
        const collector = interaction.channel.createMessageCollector({ filter, time: 30000, max: 1 });

        collector.on('collect', async (msg) => {
            const userAnswer = parseInt(msg.content);
            
            if (userAnswer === answer) {
                const successEmbed = new EmbedBuilder()
                    .setTitle('🎉 Correct Answer!')
                    .setDescription(`Excellent! **${num1} ${operation} ${num2} = ${answer}**`)
                    .setColor('#00ff00');

                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { 
                        $inc: { 
                            'economy.balance': 75,
                            'economy.xp': 15,
                            'stats.mathCorrect': 1
                        }
                    },
                    { upsert: true }
                );

                await msg.reply({ embeds: [successEmbed] });
            } else {
                const failEmbed = new EmbedBuilder()
                    .setTitle('❌ Wrong Answer')
                    .setDescription(`Sorry, the correct answer is: **${answer}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: interaction.user.id },
                    { $inc: { 'stats.mathWrong': 1 } },
                    { upsert: true }
                );

                await msg.reply({ embeds: [failEmbed] });
            }
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ Time\'s up!')
                    .setDescription(`The correct answer was: **${answer}**`)
                    .setColor('#ff9900');
                await interaction.followUp({ embeds: [timeoutEmbed] });
            }
        });
    }
};
