/**
 * CS Bot - فحص المتطلبات
 * 
 * هذا الملف يتحقق من توفر جميع المتطلبات اللازمة لتشغيل البوت
 */

console.log('🔍 فحص متطلبات تشغيل بوت CS...\n');

let allGood = true;

// فحص إصدار Node.js
console.log('📦 فحص إصدار Node.js...');
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion >= 16) {
    console.log(`✅ Node.js ${nodeVersion} - مدعوم`);
} else {
    console.log(`❌ Node.js ${nodeVersion} - غير مدعوم (يتطلب 16.9.0 أو أحدث)`);
    allGood = false;
}

// فحص ملف .env
console.log('\n📄 فحص ملف .env...');
const fs = require('fs');
const path = require('path');

if (fs.existsSync('.env')) {
    console.log('✅ ملف .env موجود');
    
    // فحص محتويات ملف .env
    const envContent = fs.readFileSync('.env', 'utf8');
    const requiredVars = ['BOT_TOKEN', 'CLIENT_ID', 'CLIENT_SECRET', 'MONGODB_URI'];
    
    for (const varName of requiredVars) {
        if (envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=YOUR_`)) {
            console.log(`✅ ${varName} - تم تعيينه`);
        } else {
            console.log(`❌ ${varName} - غير معين أو يحتوي على قيمة افتراضية`);
            allGood = false;
        }
    }
} else {
    console.log('❌ ملف .env غير موجود');
    console.log('💡 قم بنسخ .env.example إلى .env وتعبئة البيانات');
    allGood = false;
}

// فحص الحزم المطلوبة
console.log('\n📚 فحص الحزم المطلوبة...');
const packageJson = require('./package.json');
const dependencies = Object.keys(packageJson.dependencies);

for (const dep of dependencies) {
    try {
        require(dep);
        console.log(`✅ ${dep} - مثبت`);
    } catch (error) {
        console.log(`❌ ${dep} - غير مثبت`);
        allGood = false;
    }
}

// فحص الملفات المطلوبة
console.log('\n📁 فحص الملفات المطلوبة...');
const requiredFiles = [
    'config.js',
    'index.js',
    'start.js',
    'models/guild.js',
    'models/user.js',
    'dashboard/app.js',
    'dashboard/routes/index.js',
    'dashboard/routes/auth.js',
    'dashboard/routes/dashboard.js',
    'dashboard/routes/api.js',
    'dashboard/views/partials/header.ejs',
    'dashboard/views/partials/footer.ejs',
    'dashboard/views/index.ejs',
    'dashboard/views/login.ejs',
    'dashboard/views/404.ejs'
];

for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} - موجود`);
    } else {
        console.log(`❌ ${file} - مفقود`);
        allGood = false;
    }
}

// فحص المجلدات المطلوبة
console.log('\n📂 فحص المجلدات المطلوبة...');
const requiredDirs = [
    'models',
    'dashboard',
    'dashboard/routes',
    'dashboard/views',
    'dashboard/views/partials',
    'dashboard/views/dashboard',
    'dashboard/public',
    'dashboard/public/css',
    'dashboard/public/js',
    'dashboard/public/img'
];

for (const dir of requiredDirs) {
    if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
        console.log(`✅ ${dir}/ - موجود`);
    } else {
        console.log(`❌ ${dir}/ - مفقود`);
        allGood = false;
    }
}

// النتيجة النهائية
console.log('\n' + '='.repeat(50));
if (allGood) {
    console.log('🎉 جميع المتطلبات متوفرة! البوت جاهز للتشغيل.');
    console.log('\n🚀 لتشغيل البوت، استخدم: npm start');
    console.log('🌐 لتشغيل لوحة التحكم فقط: npm run dashboard');
} else {
    console.log('❌ بعض المتطلبات مفقودة. يرجى إصلاح المشاكل أعلاه قبل التشغيل.');
    console.log('\n📖 راجع ملف SETUP.md للحصول على تعليمات مفصلة.');
}
console.log('='.repeat(50));
