/**
 * CS Bot - نظام قاعدة البيانات المؤقتة
 * لحفظ البيانات في الذاكرة مؤقتاً
 */

class MemoryDB {
    constructor() {
        this.data = new Map();
        this.cache = new Map();
        this.stats = {
            reads: 0,
            writes: 0,
            deletes: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
    }

    // حفظ البيانات
    set(key, value, ttl = null) {
        const entry = {
            value,
            timestamp: Date.now(),
            ttl: ttl ? Date.now() + ttl : null
        };
        
        this.data.set(key, entry);
        this.cache.set(key, value);
        this.stats.writes++;
        
        // حذف البيانات المنتهية الصلاحية
        if (ttl) {
            setTimeout(() => {
                this.delete(key);
            }, ttl);
        }
        
        return true;
    }

    // جلب البيانات
    get(key) {
        this.stats.reads++;
        
        // التحقق من الكاش أولاً
        if (this.cache.has(key)) {
            this.stats.cacheHits++;
            const entry = this.data.get(key);
            
            // التحقق من انتهاء الصلاحية
            if (entry && entry.ttl && entry.ttl < Date.now()) {
                this.delete(key);
                return null;
            }
            
            return this.cache.get(key);
        }
        
        this.stats.cacheMisses++;
        
        const entry = this.data.get(key);
        if (!entry) return null;
        
        // التحقق من انتهاء الصلاحية
        if (entry.ttl && entry.ttl < Date.now()) {
            this.delete(key);
            return null;
        }
        
        // إضافة للكاش
        this.cache.set(key, entry.value);
        return entry.value;
    }

    // حذف البيانات
    delete(key) {
        this.stats.deletes++;
        this.data.delete(key);
        this.cache.delete(key);
        return true;
    }

    // التحقق من وجود المفتاح
    has(key) {
        const entry = this.data.get(key);
        if (!entry) return false;
        
        // التحقق من انتهاء الصلاحية
        if (entry.ttl && entry.ttl < Date.now()) {
            this.delete(key);
            return false;
        }
        
        return true;
    }

    // مسح جميع البيانات
    clear() {
        this.data.clear();
        this.cache.clear();
        this.stats = {
            reads: 0,
            writes: 0,
            deletes: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        return true;
    }

    // الحصول على حجم قاعدة البيانات
    size() {
        return this.data.size;
    }

    // الحصول على جميع المفاتيح
    keys() {
        return Array.from(this.data.keys());
    }

    // الحصول على جميع القيم
    values() {
        return Array.from(this.data.values()).map(entry => entry.value);
    }

    // الحصول على الإحصائيات
    getStats() {
        return {
            ...this.stats,
            size: this.size(),
            cacheSize: this.cache.size,
            hitRate: this.stats.reads > 0 ? (this.stats.cacheHits / this.stats.reads * 100).toFixed(2) + '%' : '0%'
        };
    }

    // تنظيف البيانات المنتهية الصلاحية
    cleanup() {
        const now = Date.now();
        let cleaned = 0;
        
        for (const [key, entry] of this.data.entries()) {
            if (entry.ttl && entry.ttl < now) {
                this.delete(key);
                cleaned++;
            }
        }
        
        return cleaned;
    }

    // حفظ البيانات مع namespace
    setNamespace(namespace, key, value, ttl = null) {
        return this.set(`${namespace}:${key}`, value, ttl);
    }

    // جلب البيانات من namespace
    getNamespace(namespace, key) {
        return this.get(`${namespace}:${key}`);
    }

    // حذف البيانات من namespace
    deleteNamespace(namespace, key) {
        return this.delete(`${namespace}:${key}`);
    }

    // مسح namespace كامل
    clearNamespace(namespace) {
        const keys = this.keys().filter(key => key.startsWith(`${namespace}:`));
        keys.forEach(key => this.delete(key));
        return keys.length;
    }

    // إنشاء نسخة احتياطية
    backup() {
        const backup = {};
        for (const [key, entry] of this.data.entries()) {
            backup[key] = {
                value: entry.value,
                timestamp: entry.timestamp,
                ttl: entry.ttl
            };
        }
        return backup;
    }

    // استعادة من النسخة الاحتياطية
    restore(backup) {
        this.clear();
        for (const [key, entry] of Object.entries(backup)) {
            this.data.set(key, entry);
            this.cache.set(key, entry.value);
        }
        return true;
    }
}

// إنشاء instance واحد للاستخدام العام
const memoryDB = new MemoryDB();

// تنظيف دوري كل 5 دقائق
setInterval(() => {
    const cleaned = memoryDB.cleanup();
    if (cleaned > 0) {
        console.log(`🧹 تم تنظيف ${cleaned} عنصر منتهي الصلاحية من الذاكرة`);
    }
}, 5 * 60 * 1000);

module.exports = memoryDB;
