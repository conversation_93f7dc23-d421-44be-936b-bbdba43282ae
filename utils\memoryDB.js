/**
 * قاعدة بيانات مؤقتة في الذاكرة
 * لحل مشكلة MongoDB timeout
 */

class MemoryDB {
    constructor() {
        this.users = new Map();
        this.guilds = new Map();
        this.stats = {
            commandsExecuted: 0,
            messagesProcessed: 0,
            premiumUsers: 0,
            startTime: Date.now()
        };
    }

    // إدارة المستخدمين
    async getUser(userId, username = '', discriminator = '', avatar = '') {
        if (!this.users.has(userId)) {
            this.users.set(userId, {
                id: userId,
                username: username,
                discriminator: discriminator,
                avatar: avatar,
                premium: {
                    enabled: false,
                    expiresAt: null,
                    tier: null,
                    addedBy: null,
                    addedAt: null
                },
                createdAt: new Date(),
                lastSeen: new Date(),
                language: 'ar'
            });
        }
        
        // تحديث البيانات
        const user = this.users.get(userId);
        if (username) user.username = username;
        if (discriminator) user.discriminator = discriminator;
        if (avatar) user.avatar = avatar;
        user.lastSeen = new Date();
        
        return user;
    }

    async updateUser(userId, data) {
        const user = await this.getUser(userId);
        Object.assign(user, data);
        return user;
    }

    // إدارة السيرفرات
    async getGuild(guildId, guildName = '') {
        if (!this.guilds.has(guildId)) {
            this.guilds.set(guildId, {
                id: guildId,
                name: guildName,
                prefix: '!',
                welcome: {
                    enabled: false,
                    channel: null,
                    message: 'مرحباً {user} في {server}!'
                },
                goodbye: {
                    enabled: false,
                    channel: null,
                    message: 'وداعاً {user}!'
                },
                autoRole: {
                    enabled: false,
                    role: null
                },
                logs: {
                    enabled: false,
                    channel: null
                },
                tickets: {
                    enabled: false,
                    category: null,
                    supportRole: null
                },
                economy: {
                    enabled: false,
                    currency: 'نقطة',
                    dailyAmount: 100
                },
                autoReply: {
                    enabled: false,
                    replies: []
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        const guild = this.guilds.get(guildId);
        if (guildName) guild.name = guildName;
        guild.updatedAt = new Date();
        
        return guild;
    }

    async updateGuild(guildId, data) {
        const guild = await this.getGuild(guildId);
        Object.assign(guild, data);
        guild.updatedAt = new Date();
        return guild;
    }

    // الإحصائيات
    incrementCommand() {
        this.stats.commandsExecuted++;
    }

    incrementMessage() {
        this.stats.messagesProcessed++;
    }

    updatePremiumCount() {
        this.stats.premiumUsers = Array.from(this.users.values())
            .filter(user => user.premium && user.premium.enabled).length;
    }

    getStats() {
        this.updatePremiumCount();
        return { ...this.stats };
    }

    // حفظ البيانات (مؤقت)
    exportData() {
        return {
            users: Array.from(this.users.entries()),
            guilds: Array.from(this.guilds.entries()),
            stats: this.stats
        };
    }

    importData(data) {
        if (data.users) {
            this.users = new Map(data.users);
        }
        if (data.guilds) {
            this.guilds = new Map(data.guilds);
        }
        if (data.stats) {
            this.stats = { ...this.stats, ...data.stats };
        }
    }
}

// إنشاء instance واحد
const memoryDB = new MemoryDB();

module.exports = memoryDB;
