// اختبار بسيط للتأكد من عمل الكود
console.log('🔄 بدء اختبار البوت...');

try {
    // تحميل الإعدادات
    const config = require('./config');
    console.log('✅ تم تحميل الإعدادات بنجاح');
    
    // اختبار الاتصال بقاعدة البيانات
    const mongoose = require('mongoose');
    console.log('✅ تم تحميل Mongoose بنجاح');
    
    // اختبار تحميل النماذج
    const { getGuildSettings } = require('./models/guild');
    const { getUserData } = require('./models/user');
    console.log('✅ تم تحميل النماذج بنجاح');
    
    // اختبار تحميل لوحة التحكم
    const dashboard = require('./dashboard/app');
    console.log('✅ تم تحميل لوحة التحكم بنجاح');
    
    console.log('🎉 جميع الاختبارات نجحت! البوت جاهز للتشغيل.');
    
} catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    console.error('📍 تفاصيل الخطأ:', error.stack);
}
