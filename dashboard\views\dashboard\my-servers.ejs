<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سيرفراتي - CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <style>
        .server-card {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
        }
        .server-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .server-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
        }
        .server-stats {
            font-size: 0.875rem;
        }
        .badge-owner {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
        }
        .badge-admin {
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
        }
        .no-servers {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .add-bot-btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .add-bot-btn:hover {
            transform: scale(1.05);
            color: white;
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-server text-primary"></i>
                            <% if (isOwner) { %>
                                جميع السيرفرات
                            <% } else { %>
                                سيرفراتي
                            <% } %>
                        </h1>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="refreshServers()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                            <a href="https://discord.com/api/oauth2/authorize?client_id=<%= config.clientId %>&permissions=8&scope=bot%20applications.commands" 
                               target="_blank" class="btn add-bot-btn">
                                <i class="fas fa-plus"></i> إضافة البوت لسيرفر جديد
                            </a>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                                    <h4 class="card-title"><%= servers.length %></h4>
                                    <p class="card-text text-muted">إجمالي السيرفرات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                                    <h4 class="card-title"><%= servers.reduce((total, server) => total + server.memberCount, 0).toLocaleString() %></h4>
                                    <p class="card-text text-muted">إجمالي الأعضاء</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                                    <h4 class="card-title"><%= servers.filter(s => s.isOwner).length %></h4>
                                    <p class="card-text text-muted">سيرفرات أملكها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-hashtag fa-2x text-info mb-2"></i>
                                    <h4 class="card-title"><%= servers.reduce((total, server) => total + server.channelCount, 0) %></h4>
                                    <p class="card-text text-muted">إجمالي القنوات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- قائمة السيرفرات -->
                    <% if (servers.length > 0) { %>
                        <div class="row" id="serversContainer">
                            <% servers.forEach(server => { %>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card server-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                <% if (server.icon) { %>
                                                    <img src="https://cdn.discordapp.com/icons/<%= server.id %>/<%= server.icon %>.png" 
                                                         alt="<%= server.name %>" class="server-icon me-3">
                                                <% } else { %>
                                                    <div class="server-icon me-3 bg-primary d-flex align-items-center justify-content-center text-white">
                                                        <i class="fas fa-server fa-2x"></i>
                                                    </div>
                                                <% } %>
                                                <div class="flex-grow-1">
                                                    <h5 class="card-title mb-1"><%= server.name %></h5>
                                                    <div class="d-flex gap-1">
                                                        <% if (server.isOwner) { %>
                                                            <span class="badge badge-owner">مالك</span>
                                                        <% } else { %>
                                                            <span class="badge badge-admin">مدير</span>
                                                        <% } %>
                                                        <% if (server.boostLevel > 0) { %>
                                                            <span class="badge bg-gradient-primary">
                                                                <i class="fas fa-rocket"></i> Level <%= server.boostLevel %>
                                                            </span>
                                                        <% } %>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="server-stats mb-3">
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div class="text-muted">الأعضاء</div>
                                                        <div class="fw-bold"><%= server.memberCount.toLocaleString() %></div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="text-muted">القنوات</div>
                                                        <div class="fw-bold"><%= server.channelCount %></div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="text-muted">الرتب</div>
                                                        <div class="fw-bold"><%= server.roleCount %></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <% if (server.botInGuild) { %>
                                                    <a href="/dashboard/server/<%= server.id %>" class="btn btn-primary btn-sm flex-fill">
                                                        <i class="fas fa-cogs"></i> إدارة السيرفر
                                                    </a>
                                                <% } else { %>
                                                    <a href="https://discord.com/api/oauth2/authorize?client_id=<%= config.clientId %>&permissions=8&scope=bot%20applications.commands&guild_id=<%= server.id %>" 
                                                       target="_blank" class="btn btn-success btn-sm flex-fill">
                                                        <i class="fas fa-plus"></i> إضافة البوت
                                                    </a>
                                                <% } %>
                                                <button class="btn btn-outline-info btn-sm" onclick="showServerInfo('<%= server.id %>')">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt"></i>
                                                انضم في <%= new Date(server.joinedAt).toLocaleDateString('ar-SA') %>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        </div>
                    <% } else { %>
                        <div class="no-servers">
                            <i class="fas fa-server fa-5x text-muted mb-4"></i>
                            <h3>لا توجد سيرفرات</h3>
                            <p class="lead">لم يتم العثور على أي سيرفرات تملك صلاحيات إدارة فيها.</p>
                            <a href="https://discord.com/api/oauth2/authorize?client_id=<%= config.clientId %>&permissions=8&scope=bot%20applications.commands" 
                               target="_blank" class="btn add-bot-btn btn-lg">
                                <i class="fas fa-plus"></i> إضافة البوت لسيرفر
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>

    <!-- Server Info Modal -->
    <div class="modal fade" id="serverInfoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle"></i> معلومات السيرفر
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="serverInfoContent">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function refreshServers() {
            location.reload();
        }

        function showServerInfo(serverId) {
            const server = <%- JSON.stringify(servers) %>.find(s => s.id === serverId);
            if (!server) return;

            const content = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        ${server.icon ? 
                            `<img src="https://cdn.discordapp.com/icons/${server.id}/${server.icon}.png" class="img-fluid rounded-circle mb-3" style="max-width: 128px;">` :
                            `<div class="bg-primary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center text-white" style="width: 128px; height: 128px;"><i class="fas fa-server fa-3x"></i></div>`
                        }
                        <h4>${server.name}</h4>
                    </div>
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr><td><strong>معرف السيرفر:</strong></td><td><code>${server.id}</code></td></tr>
                            <tr><td><strong>عدد الأعضاء:</strong></td><td>${server.memberCount.toLocaleString()}</td></tr>
                            <tr><td><strong>عدد القنوات:</strong></td><td>${server.channelCount}</td></tr>
                            <tr><td><strong>عدد الرتب:</strong></td><td>${server.roleCount}</td></tr>
                            <tr><td><strong>مستوى البوست:</strong></td><td>${server.boostLevel}</td></tr>
                            <tr><td><strong>عدد البوستات:</strong></td><td>${server.boostCount}</td></tr>
                            <tr><td><strong>تاريخ الانضمام:</strong></td><td>${new Date(server.joinedAt).toLocaleDateString('ar-SA')}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('serverInfoContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('serverInfoModal')).show();
        }
    </script>
</body>
</html>
