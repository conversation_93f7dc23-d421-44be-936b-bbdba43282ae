const { EmbedBuilder, ActionRow<PERSON>uilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'image',
    description: 'مولد الصور والميمز المتقدم',
    usage: '!image <نوع> [نص]',
    category: 'صور',
    cooldown: 5,
    
    async execute(message, args) {
        try {
            const imageType = args[0]?.toLowerCase();
            
            switch (imageType) {
                case 'meme':
                case 'ميم':
                    return this.generateMeme(message, args.slice(1));
                case 'quote':
                case 'اقتباس':
                    return this.generateQuote(message, args.slice(1));
                case 'avatar':
                case 'صورة':
                    return this.generateAvatar(message, args.slice(1));
                case 'banner':
                case 'بانر':
                    return this.generateBanner(message, args.slice(1));
                case 'logo':
                case 'شعار':
                    return this.generateLogo(message, args.slice(1));
                case 'random':
                case 'عشوائي':
                    return this.getRandomImage(message, args.slice(1));
                default:
                    return this.showImageMenu(message);
            }
            
        } catch (error) {
            console.error('خطأ في أمر الصور:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ في مولد الصور')
                .setDescription('حدث خطأ أثناء إنشاء الصورة')
                .setTimestamp();
                
            return message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async showImageMenu(message) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b9d')
            .setTitle('🎨 مولد الصور والميمز المتقدم')
            .setDescription('أنشئ صوراً مذهلة وميمز مضحكة!')
            .addFields([
                {
                    name: '😂 الميمز والمرح',
                    value: '• **ميم** - إنشاء ميم مضحك\n• **اقتباس** - صورة اقتباس ملهم\n• **عشوائي** - صورة مضحكة عشوائية',
                    inline: true
                },
                {
                    name: '🎭 الصور الشخصية',
                    value: '• **صورة** - مولد أفاتار\n• **بانر** - بانر شخصي\n• **شعار** - تصميم شعار',
                    inline: true
                },
                {
                    name: '🌟 المميزات',
                    value: '• **جودة عالية** - صور HD\n• **تخصيص كامل** - ألوان وخطوط\n• **تحميل سريع** - ثوان معدودة',
                    inline: false
                }
            ])
            .setFooter({ text: 'اختر نوع الصورة من الأزرار!' })
            .setTimestamp();
            
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('img_meme')
                    .setLabel('ميم مضحك')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('😂'),
                new ButtonBuilder()
                    .setCustomId('img_quote')
                    .setLabel('اقتباس ملهم')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💭'),
                new ButtonBuilder()
                    .setCustomId('img_avatar')
                    .setLabel('أفاتار')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎭'),
                new ButtonBuilder()
                    .setCustomId('img_banner')
                    .setLabel('بانر')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎨')
            );
            
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('img_logo')
                    .setLabel('شعار')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🏷️'),
                new ButtonBuilder()
                    .setCustomId('img_random')
                    .setLabel('صورة عشوائية')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎲'),
                new ButtonBuilder()
                    .setCustomId('img_gallery')
                    .setLabel('معرض الصور')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🖼️'),
                new ButtonBuilder()
                    .setCustomId('img_help')
                    .setLabel('مساعدة')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
            );
            
        return message.reply({ embeds: [embed], components: [row1, row2] });
    },
    
    async generateMeme(message, args) {
        const memeText = args.join(' ') || 'عندما تنسى حفظ الكود';
        
        const embed = new EmbedBuilder()
            .setColor('#feca57')
            .setTitle('😂 مولد الميمز')
            .setDescription('جاري إنشاء ميم مضحك...')
            .addFields([
                { name: '📝 النص', value: memeText, inline: true },
                { name: '🎨 القالب', value: 'Drake Pointing', inline: true }
            ])
            .setFooter({ text: 'قد يستغرق هذا بضع ثوان...' })
            .setTimestamp();
            
        const loadingMsg = await message.reply({ embeds: [embed] });
        
        setTimeout(async () => {
            const memeEmbed = new EmbedBuilder()
                .setColor('#ff6b9d')
                .setTitle('😂 ميمك جاهز!')
                .setDescription(`**${memeText}**`)
                .setImage('https://i.imgflip.com/1bij.jpg') // صورة ميم وهمية
                .addFields([
                    { name: '👤 أنشأ بواسطة', value: message.author.toString(), inline: true },
                    { name: '⭐ التقييم', value: '⭐⭐⭐⭐⭐', inline: true },
                    { name: '📊 المشاهدات', value: '1', inline: true }
                ])
                .setFooter({ text: 'CS Bot Meme Generator' })
                .setTimestamp();
                
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('meme_regenerate')
                        .setLabel('إعادة إنشاء')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('🔄'),
                    new ButtonBuilder()
                        .setCustomId('meme_share')
                        .setLabel('مشاركة')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('📤'),
                    new ButtonBuilder()
                        .setCustomId('meme_save')
                        .setLabel('حفظ')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('💾'),
                    new ButtonBuilder()
                        .setCustomId('meme_rate')
                        .setLabel('تقييم')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('⭐')
                );
                
            await loadingMsg.edit({ embeds: [memeEmbed], components: [row] });
        }, 3000);
    },
    
    async generateQuote(message, args) {
        const quoteText = args.join(' ') || 'النجاح هو الانتقال من فشل إلى فشل دون فقدان الحماس';
        
        const quotes = [
            'النجاح هو الانتقال من فشل إلى فشل دون فقدان الحماس',
            'الطريق إلى النجاح دائماً تحت الإنشاء',
            'لا تنتظر الفرصة، بل اصنعها',
            'المستحيل هو مجرد رأي وليس حقيقة',
            'كل إنجاز عظيم بدأ بقرار المحاولة'
        ];
        
        const randomQuote = args.length ? quoteText : quotes[Math.floor(Math.random() * quotes.length)];
        
        const embed = new EmbedBuilder()
            .setColor('#a55eea')
            .setTitle('💭 مولد الاقتباسات')
            .setDescription(`**"${randomQuote}"**`)
            .setImage('https://via.placeholder.com/600x300/a55eea/ffffff?text=' + encodeURIComponent(randomQuote))
            .addFields([
                { name: '✍️ الكاتب', value: message.author.username, inline: true },
                { name: '🎨 النمط', value: 'كلاسيكي', inline: true },
                { name: '📅 التاريخ', value: new Date().toLocaleDateString('ar-SA'), inline: true }
            ])
            .setFooter({ text: 'CS Bot Quote Generator' })
            .setTimestamp();
            
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('quote_new')
                    .setLabel('اقتباس جديد')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔄'),
                new ButtonBuilder()
                    .setCustomId('quote_style')
                    .setLabel('تغيير النمط')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId('quote_download')
                    .setLabel('تحميل')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('⬇️')
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    },
    
    async generateAvatar(message, args) {
        const user = message.mentions.users.first() || message.author;
        const style = args[0] || 'cartoon';
        
        const embed = new EmbedBuilder()
            .setColor('#4ecdc4')
            .setTitle('🎭 مولد الأفاتار')
            .setDescription('جاري إنشاء أفاتار مخصص...')
            .addFields([
                { name: '👤 المستخدم', value: user.username, inline: true },
                { name: '🎨 النمط', value: style, inline: true }
            ])
            .setThumbnail(user.displayAvatarURL({ dynamic: true, size: 256 }))
            .setTimestamp();
            
        const loadingMsg = await message.reply({ embeds: [embed] });
        
        setTimeout(async () => {
            const avatarEmbed = new EmbedBuilder()
                .setColor('#4ecdc4')
                .setTitle('🎭 أفاتارك الجديد!')
                .setDescription(`أفاتار مخصص لـ **${user.username}**`)
                .setImage(user.displayAvatarURL({ dynamic: true, size: 512 }))
                .addFields([
                    { name: '🎨 النمط', value: style, inline: true },
                    { name: '📐 الدقة', value: '512x512', inline: true },
                    { name: '🎯 الجودة', value: 'HD', inline: true }
                ])
                .setFooter({ text: 'CS Bot Avatar Generator' })
                .setTimestamp();
                
            await loadingMsg.edit({ embeds: [avatarEmbed] });
        }, 2000);
    },
    
    async generateBanner(message, args) {
        const bannerText = args.join(' ') || message.author.username;
        
        const embed = new EmbedBuilder()
            .setColor('#ff7675')
            .setTitle('🎨 مولد البانر')
            .setDescription(`**${bannerText}**`)
            .setImage('https://via.placeholder.com/800x200/ff7675/ffffff?text=' + encodeURIComponent(bannerText))
            .addFields([
                { name: '📝 النص', value: bannerText, inline: true },
                { name: '🎨 اللون', value: '#ff7675', inline: true },
                { name: '📐 الحجم', value: '800x200', inline: true }
            ])
            .setFooter({ text: 'CS Bot Banner Generator' })
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async generateLogo(message, args) {
        const logoText = args.join(' ') || 'CS Bot';
        
        const embed = new EmbedBuilder()
            .setColor('#00b894')
            .setTitle('🏷️ مولد الشعارات')
            .setDescription(`شعار مخصص: **${logoText}**`)
            .setImage('https://via.placeholder.com/400x400/00b894/ffffff?text=' + encodeURIComponent(logoText))
            .addFields([
                { name: '📝 النص', value: logoText, inline: true },
                { name: '🎨 النمط', value: 'احترافي', inline: true },
                { name: '📐 الشكل', value: 'مربع', inline: true }
            ])
            .setFooter({ text: 'CS Bot Logo Generator' })
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async getRandomImage(message, args) {
        const categories = ['cats', 'dogs', 'nature', 'space', 'art'];
        const category = args[0] || categories[Math.floor(Math.random() * categories.length)];
        
        const images = {
            cats: 'https://cataas.com/cat',
            dogs: 'https://dog.ceo/api/breeds/image/random',
            nature: 'https://picsum.photos/800/600?nature',
            space: 'https://picsum.photos/800/600?space',
            art: 'https://picsum.photos/800/600?art'
        };
        
        const embed = new EmbedBuilder()
            .setColor('#fd79a8')
            .setTitle('🎲 صورة عشوائية')
            .setDescription(`فئة: **${category}**`)
            .setImage(images[category] || images.nature)
            .addFields([
                { name: '📂 الفئة', value: category, inline: true },
                { name: '🎯 الجودة', value: 'عالية', inline: true },
                { name: '📊 المصدر', value: 'عشوائي', inline: true }
            ])
            .setFooter({ text: 'CS Bot Random Image' })
            .setTimestamp();
            
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('random_new')
                    .setLabel('صورة جديدة')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔄'),
                new ButtonBuilder()
                    .setCustomId('random_category')
                    .setLabel('تغيير الفئة')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📂')
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    }
};
