<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ - CS Bot</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        .error-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .bot-logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 4px solid #fff;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>

<div class="error-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card error-card border-0">
                    <div class="card-body text-center p-5">
                        <!-- شعار البوت -->
                        <% if (config && config.clientId) { %>
                            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=256"
                                 alt="CS Bot"
                                 class="bot-logo mb-4"
                                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
                        <% } else { %>
                            <img src="https://cdn.discordapp.com/embed/avatars/0.png"
                                 alt="CS Bot"
                                 class="bot-logo mb-4">
                        <% } %>

                        <h3 class="text-primary mb-2">CS Bot</h3>

                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-4"></i>
                        <h2 class="text-danger mb-3"><%= error || 'حدث خطأ غير متوقع' %></h2>
                        <p class="text-muted mb-4"><%- (typeof message !== 'undefined' ? message : 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.') %></p>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>نصيحة:</strong> إذا استمر هذا الخطأ، يرجى التواصل مع المطور مع تفاصيل ما كنت تحاول فعله.
                        </div>

                        <div class="d-grid gap-2">
                            <a href="/" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                            </a>
                            <a href="/auth/login" class="btn btn-outline-secondary">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                            <button onclick="window.location.reload()" class="btn btn-outline-info">
                                <i class="fas fa-redo me-2"></i>إعادة المحاولة
                            </button>
                        </div>

                        <% if (user) { %>
                        <div class="mt-4">
                            <small class="text-muted">
                                مسجل الدخول كـ: <strong><%= user.username %></strong>
                            </small>
                        </div>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
