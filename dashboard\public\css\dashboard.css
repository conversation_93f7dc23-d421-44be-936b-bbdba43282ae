/* CS Bot Dashboard - تصميم أسطوري ومتقدم */

/* متغيرات CSS */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --sidebar-width: 280px;
    --navbar-height: 70px;
    
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* تخطيط الصفحة الرئيسي */
.main-content {
    margin-right: var(--sidebar-width);
    padding-top: var(--navbar-height);
    min-height: 100vh;
    transition: var(--transition);
}

/* البطاقات المحسنة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    background: white;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1rem;
}

.stats-icon.primary { background: var(--primary-gradient); }
.stats-icon.success { background: var(--success-gradient); }
.stats-icon.warning { background: var(--warning-gradient); }
.stats-icon.danger { background: var(--danger-gradient); }

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #7f8c8d;
    font-size: 0.875rem;
    font-weight: 500;
}

/* الأزرار المحسنة */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-gradient {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: white;
}

/* الجداول المحسنة */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: white;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* النماذج المحسنة */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* التنبيهات المحسنة */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
}

.alert-primary::before { background: #667eea; }
.alert-success::before { background: #28a745; }
.alert-warning::before { background: #ffc107; }
.alert-danger::before { background: #dc3545; }
.alert-info::before { background: #17a2b8; }

/* الشارات المحسنة */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

.badge-gradient-primary {
    background: var(--primary-gradient);
    color: white;
}

.badge-gradient-success {
    background: var(--success-gradient);
    color: white;
}

/* التحميل والرسوم المتحركة */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تحسينات للموبايل */
@media (max-width: 991.98px) {
    .main-content {
        margin-right: 0;
        padding-top: var(--navbar-height);
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تأثيرات خاصة */
.glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* تخصيصات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
    }
    
    .card {
        background: #34495e;
        color: white;
    }
    
    .table {
        background: #34495e;
        color: white;
    }
    
    .form-control {
        background: #2c3e50;
        border-color: #34495e;
        color: white;
    }
}

/* تحسينات الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* دعم RTL محسن */
[dir="rtl"] .main-content {
    margin-right: 0;
    margin-left: var(--sidebar-width);
}

[dir="rtl"] .slide-in-right {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}
