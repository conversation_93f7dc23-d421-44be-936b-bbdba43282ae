<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات المتقدمة - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64" 
                 alt="CS Bot" 
                 class="rounded-circle me-2" 
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-cogs"></i> CS Bot - الإعدادات المتقدمة</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-cogs"></i> الإعدادات المتقدمة
                    </h2>
                    <p class="card-text">إعدادات البوت والنظام العامة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Sections -->
    <div class="row">
        <!-- Bot Settings -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-robot"></i> إعدادات البوت
                    </h5>
                </div>
                <div class="card-body">
                    <form id="botSettingsForm">
                        <div class="mb-3">
                            <label for="botPrefix" class="form-label">البادئة</label>
                            <input type="text" class="form-control" id="botPrefix" value="<%= config.prefix || '!' %>">
                        </div>
                        <div class="mb-3">
                            <label for="botStatus" class="form-label">حالة البوت</label>
                            <select class="form-control" id="botStatus">
                                <option value="online">متصل</option>
                                <option value="idle">خامل</option>
                                <option value="dnd">مشغول</option>
                                <option value="invisible">غير مرئي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="botActivity" class="form-label">نشاط البوت</label>
                            <input type="text" class="form-control" id="botActivity" placeholder="يلعب CS Bot">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ إعدادات البوت
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <form id="systemSettingsForm">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableLogs" checked>
                                <label class="form-check-label" for="enableLogs">
                                    تفعيل السجلات
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableAnalytics" checked>
                                <label class="form-check-label" for="enableAnalytics">
                                    تفعيل التحليلات
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableBackup">
                                <label class="form-check-label" for="enableBackup">
                                    النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ إعدادات النظام
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Database Settings -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i> إعدادات قاعدة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">حالة الاتصال</label>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">متصل</span>
                            <small class="text-muted">MongoDB Atlas</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">حجم قاعدة البيانات</label>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 25%">25%</div>
                        </div>
                        <small class="text-muted">2.5 MB / 10 MB</small>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                            <i class="fas fa-tools"></i> تحسين قاعدة البيانات
                        </button>
                        <button class="btn btn-outline-danger" onclick="backupDatabase()">
                            <i class="fas fa-download"></i> نسخة احتياطية
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt"></i> إعدادات الأمان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="maxCommandsPerMinute" class="form-label">الحد الأقصى للأوامر في الدقيقة</label>
                        <input type="number" class="form-control" id="maxCommandsPerMinute" value="10">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableAntiSpam" checked>
                            <label class="form-check-label" for="enableAntiSpam">
                                تفعيل مانع السبام
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableRateLimit" checked>
                            <label class="form-check-label" for="enableRateLimit">
                                تحديد معدل الطلبات
                            </label>
                        </div>
                    </div>
                    <button class="btn btn-danger" onclick="saveSecuritySettings()">
                        <i class="fas fa-save"></i> حفظ إعدادات الأمان
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- System Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i> إجراءات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="clearCache()">
                                <i class="fas fa-broom"></i> مسح الذاكرة المؤقتة
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="updateBot()">
                                <i class="fas fa-sync"></i> تحديث البوت
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="restartBot()">
                                <i class="fas fa-redo"></i> إعادة تشغيل البوت
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="shutdownBot()">
                                <i class="fas fa-power-off"></i> إيقاف البوت
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// حفظ إعدادات البوت
document.getElementById('botSettingsForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const settings = {
        prefix: document.getElementById('botPrefix').value,
        status: document.getElementById('botStatus').value,
        activity: document.getElementById('botActivity').value
    };
    
    try {
        const response = await fetch('/api/settings/bot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم حفظ إعدادات البوت بنجاح!');
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في حفظ الإعدادات');
    }
});

// حفظ إعدادات النظام
document.getElementById('systemSettingsForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const settings = {
        enableLogs: document.getElementById('enableLogs').checked,
        enableAnalytics: document.getElementById('enableAnalytics').checked,
        enableBackup: document.getElementById('enableBackup').checked
    };
    
    try {
        const response = await fetch('/api/settings/system', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم حفظ إعدادات النظام بنجاح!');
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في حفظ الإعدادات');
    }
});

// إجراءات النظام
async function clearCache() {
    if (!confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) return;
    
    try {
        const response = await fetch('/api/system/clear-cache', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
            alert('تم مسح الذاكرة المؤقتة بنجاح!');
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في مسح الذاكرة المؤقتة');
    }
}

async function restartBot() {
    if (!confirm('هل أنت متأكد من إعادة تشغيل البوت؟')) return;
    
    try {
        const response = await fetch('/api/restart', { method: 'POST' });
        const result = await response.json();
        
        if (response.ok) {
            alert('سيتم إعادة تشغيل البوت خلال 5 ثوان...');
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إعادة تشغيل البوت');
    }
}

function optimizeDatabase() {
    alert('جاري تحسين قاعدة البيانات...');
}

function backupDatabase() {
    alert('جاري إنشاء نسخة احتياطية...');
}

function saveSecuritySettings() {
    alert('تم حفظ إعدادات الأمان بنجاح!');
}

function updateBot() {
    alert('جاري البحث عن تحديثات...');
}

function shutdownBot() {
    if (!confirm('هل أنت متأكد من إيقاف البوت؟')) return;
    alert('تم إيقاف البوت');
}
</script>

</body>
</html>
