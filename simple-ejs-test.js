const express = require('express');
const path = require('path');

const app = express();

// إعداد EJS
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'dashboard/views'));

// مسار اختبار بسيط
app.get('/', (req, res) => {
    console.log('تجربة عرض صفحة بسيطة...');
    res.render('index-simple');
});

// تشغيل الخادم
app.listen(3001, () => {
    console.log('خادم اختبار EJS يعمل على المنفذ 3001');
    console.log('اذهب إلى: http://localhost:3001');
});
