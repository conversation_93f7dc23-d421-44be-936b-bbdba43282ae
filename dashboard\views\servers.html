<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيرفرات - CS Bot</title>
    
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot"></i> CS Bot
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/servers"><i class="fas fa-server"></i> السيرفرات</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <a href="/auth/login" class="btn btn-outline-light">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold text-center">
                    <i class="fas fa-server text-primary"></i> إدارة السيرفرات
                </h1>
                <p class="lead text-center text-muted">
                    اختر السيرفر الذي تريد إدارته أو أضف البوت لسيرفر جديد
                </p>
            </div>
        </div>

        <!-- Add Bot Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-0 shadow-lg bg-gradient text-white">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-plus-circle fa-4x mb-3"></i>
                        <h3 class="card-title">أضف البوت لسيرفر جديد</h3>
                        <p class="card-text mb-4">
                            احصل على جميع مميزات CS Bot في سيرفرك الآن!
                        </p>
                        <a href="https://discord.com/api/oauth2/authorize?client_id={{config.clientId}}&permissions=8&scope=bot%20applications.commands" 
                           class="btn btn-warning btn-lg" target="_blank">
                            <i class="fas fa-external-link-alt"></i> إضافة البوت
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Servers List -->
        <div class="row" id="serversList">
            <!-- سيتم تحميل السيرفرات هنا بـ JavaScript -->
            <div class="col-12 text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري تحميل السيرفرات...</p>
            </div>
        </div>

        <!-- No Servers Message -->
        <div class="row d-none" id="noServersMessage">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-server fa-4x text-muted mb-3"></i>
                        <h4>لا توجد سيرفرات</h4>
                        <p class="text-muted">
                            لم يتم العثور على أي سيرفرات. قم بإضافة البوت لسيرفرك أولاً.
                        </p>
                        <a href="https://discord.com/api/oauth2/authorize?client_id={{config.clientId}}&permissions=8&scope=bot%20applications.commands" 
                           class="btn btn-primary" target="_blank">
                            <i class="fas fa-plus"></i> إضافة البوت
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-robot"></i> CS Bot</h5>
                    <p>بوت ديسكورد شامل ومتطور</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>&copy; 2024 CS Bot. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحميل السيرفرات
        async function loadServers() {
            try {
                const response = await fetch('/api/servers');
                const data = await response.json();
                
                const serversList = document.getElementById('serversList');
                const noServersMessage = document.getElementById('noServersMessage');
                
                if (data.servers && data.servers.length > 0) {
                    serversList.innerHTML = '';
                    
                    data.servers.forEach(server => {
                        const serverCard = createServerCard(server);
                        serversList.appendChild(serverCard);
                    });
                } else {
                    serversList.classList.add('d-none');
                    noServersMessage.classList.remove('d-none');
                }
            } catch (error) {
                console.error('خطأ في تحميل السيرفرات:', error);
                document.getElementById('serversList').innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            حدث خطأ في تحميل السيرفرات. يرجى المحاولة مرة أخرى.
                        </div>
                    </div>
                `;
            }
        }
        
        // إنشاء بطاقة سيرفر
        function createServerCard(server) {
            const col = document.createElement('div');
            col.className = 'col-lg-4 col-md-6 mb-4';
            
            col.innerHTML = `
                <div class="card h-100 border-0 shadow-sm server-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <img src="${server.icon || '/img/default-server.png'}" 
                                 alt="${server.name}" 
                                 class="rounded-circle me-3" 
                                 width="50" height="50">
                            <div>
                                <h5 class="card-title mb-1">${server.name}</h5>
                                <small class="text-muted">
                                    <i class="fas fa-users"></i> ${server.memberCount || 0} عضو
                                </small>
                            </div>
                        </div>
                        
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="stat-mini">
                                    <i class="fas fa-hashtag text-primary"></i>
                                    <small class="d-block">${server.channelCount || 0}</small>
                                    <small class="text-muted">قناة</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-mini">
                                    <i class="fas fa-user-tag text-success"></i>
                                    <small class="d-block">${server.roleCount || 0}</small>
                                    <small class="text-muted">رتبة</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-mini">
                                    <i class="fas fa-crown text-warning"></i>
                                    <small class="d-block">${server.owner ? 'نعم' : 'لا'}</small>
                                    <small class="text-muted">مالك</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            ${server.canManage ? `
                                <a href="/dashboard/${server.id}" class="btn btn-primary">
                                    <i class="fas fa-cog"></i> إدارة السيرفر
                                </a>
                            ` : `
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-lock"></i> لا توجد صلاحيات
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            `;
            
            return col;
        }
        
        // تحميل السيرفرات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', loadServers);
    </script>
    
    <style>
        .server-card {
            transition: all 0.3s ease;
        }
        
        .server-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }
        
        .stat-mini {
            padding: 10px;
            border-radius: 10px;
            background: rgba(0,0,0,0.05);
        }
    </style>
</body>
</html>
