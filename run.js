#!/usr/bin/env node

/**
 * CS Bot - مشغل البوت النهائي
 * 
 * هذا الملف يوفر واجهة سهلة لتشغيل البوت مع خيارات متعددة
 */

const { spawn } = require('child_process');
const fs = require('fs');

// ألوان للنص
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(color, message) {
    console.log(colors[color] + message + colors.reset);
}

function showBanner() {
    console.clear();
    colorLog('cyan', '╔══════════════════════════════════════════════════════════════╗');
    colorLog('cyan', '║                        🤖 بوت CS                            ║');
    colorLog('cyan', '║                نظام شامل للسيرفرات الاحترافية                ║');
    colorLog('cyan', '╚══════════════════════════════════════════════════════════════╝');
    console.log();
}

function showMenu() {
    colorLog('yellow', '📋 اختر طريقة التشغيل:');
    console.log();
    colorLog('green', '1. 🚀 تشغيل البوت كاملاً (مُوصى به)');
    colorLog('blue', '2. 🌐 تشغيل لوحة التحكم فقط');
    colorLog('magenta', '3. 🔧 وضع التطوير (مع إعادة التحميل التلقائي)');
    colorLog('cyan', '4. 🔍 فحص المتطلبات والإعدادات');
    colorLog('yellow', '5. 🧪 اختبار تحميل المكونات');
    colorLog('red', '0. ❌ خروج');
    console.log();
}

function runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
        const child = spawn('node', [command, ...args], {
            stdio: 'inherit',
            shell: true
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`العملية انتهت بكود خطأ ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

async function handleChoice(choice) {
    switch (choice) {
        case '1':
            colorLog('green', '🚀 بدء تشغيل البوت كاملاً...');
            try {
                await runCommand('start.js');
            } catch (error) {
                colorLog('red', '❌ فشل في تشغيل البوت: ' + error.message);
            }
            break;

        case '2':
            colorLog('blue', '🌐 بدء تشغيل لوحة التحكم فقط...');
            try {
                await runCommand('dashboard-only.js');
            } catch (error) {
                colorLog('red', '❌ فشل في تشغيل لوحة التحكم: ' + error.message);
            }
            break;

        case '3':
            colorLog('magenta', '🔧 بدء وضع التطوير...');
            try {
                await runCommand('nodemon', ['start.js']);
            } catch (error) {
                colorLog('red', '❌ فشل في تشغيل وضع التطوير: ' + error.message);
                colorLog('yellow', '💡 تأكد من تثبيت nodemon: npm install -g nodemon');
            }
            break;

        case '4':
            colorLog('cyan', '🔍 فحص المتطلبات والإعدادات...');
            try {
                await runCommand('check-requirements.js');
            } catch (error) {
                colorLog('red', '❌ فشل في فحص المتطلبات: ' + error.message);
            }
            break;

        case '5':
            colorLog('yellow', '🧪 اختبار تحميل المكونات...');
            try {
                await runCommand('test.js');
            } catch (error) {
                colorLog('red', '❌ فشل في الاختبار: ' + error.message);
            }
            break;

        case '0':
            colorLog('red', '👋 وداعاً!');
            process.exit(0);
            break;

        default:
            colorLog('red', '❌ خيار غير صحيح. يرجى اختيار رقم من 0 إلى 5.');
            break;
    }
}

async function main() {
    showBanner();

    // فحص وجود ملف .env
    if (!fs.existsSync('.env')) {
        colorLog('red', '❌ ملف .env غير موجود!');
        colorLog('yellow', '💡 قم بنسخ .env.example إلى .env وتعبئة البيانات المطلوبة');
        colorLog('cyan', '📖 راجع ملف SETUP.md للحصول على تعليمات مفصلة');
        process.exit(1);
    }

    // فحص وجود node_modules
    if (!fs.existsSync('node_modules')) {
        colorLog('red', '❌ الحزم غير مثبتة!');
        colorLog('yellow', '💡 قم بتشغيل: npm install');
        process.exit(1);
    }

    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    while (true) {
        showMenu();
        
        const choice = await new Promise((resolve) => {
            rl.question(colors.bright + 'اختر رقماً: ' + colors.reset, resolve);
        });

        console.log();
        await handleChoice(choice.trim());
        
        if (choice.trim() !== '0') {
            console.log();
            await new Promise((resolve) => {
                rl.question(colors.yellow + 'اضغط Enter للمتابعة...' + colors.reset, resolve);
            });
        }
    }
}

// تشغيل البرنامج
main().catch((error) => {
    colorLog('red', '❌ خطأ غير متوقع: ' + error.message);
    process.exit(1);
});
