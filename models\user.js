/**
 * CS Bot - نموذج المستخدم (محدث لقاعدة البيانات المؤقتة)
 *
 * هذا الملف يحتوي على نموذج قاعدة البيانات للمستخدمين
 */

const memoryDB = require('../utils/memoryDB');

// مخطط المستخدم (معطل مؤقتاً - نستخدم قاعدة البيانات المؤقتة)
/*
const userSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true,
        unique: true
    },
    username: {
        type: String,
        required: true
    },
    discriminator: {
        type: String
    },
    avatar: {
        type: String
    },
    premium: {
        enabled: {
            type: Boolean,
            default: false
        },
        expiresAt: {
            type: Date,
            default: null
        },
        tier: {
            type: String,
            enum: ['basic', 'pro', null],
            default: null
        }
    },
    economy: {
        type: Map,
        of: {
            balance: {
                type: Number,
                default: 0
            },
            bank: {
                type: Number,
                default: 0
            },
            lastDaily: {
                type: Date,
                default: null
            },
            lastWork: {
                type: Date,
                default: null
            },
            inventory: {
                type: [Object],
                default: []
            }
        },
        default: new Map()
    },
    warnings: {
        type: Map,
        of: [
            {
                reason: {
                    type: String,
                    default: 'No reason provided'
                },
                moderator: {
                    type: String,
                    required: true
                },
                timestamp: {
                    type: Date,
                    default: Date.now
                }
            }
        ],
        default: new Map()
    },
    tickets: {
        type: Map,
        of: [String],
        default: new Map()
    },
    language: {
        type: String,
        enum: ['ar', 'en'],
        default: 'ar'
    },
    lastSeen: {
        type: Date,
        default: Date.now
    }
}, { timestamps: true });

// إنشاء النموذج
const User = mongoose.model('User', userSchema);
*/

// دالة للحصول على بيانات المستخدم (محدثة لقاعدة البيانات المؤقتة)
async function getUserData(userId, username = '', discriminator = '', avatar = '') {
    try {
        const user = await memoryDB.getUser(userId, username, discriminator, avatar);

        // إضافة دالة save للتوافق مع الكود الموجود
        user.save = async function() {
            return memoryDB.updateUser(userId, this);
        };

        return user;
    } catch (err) {
        console.error('خطأ في الحصول على بيانات المستخدم:', err);
        throw err;
    }
}

// دالة لتحديث رصيد المستخدم (معطلة مؤقتاً)
/*
async function updateUserBalance(userId, guildId, amount, type = 'add', location = 'balance') {
    try {
        const user = await User.findOne({ userId });

        if (!user) {
            throw new Error('المستخدم غير موجود');
        }

        // التأكد من وجود بيانات الاقتصاد للسيرفر
        if (!user.economy.has(guildId)) {
            user.economy.set(guildId, {
                balance: 0,
                bank: 0,
                lastDaily: null,
                lastWork: null,
                inventory: []
            });
        }

        const guildEconomy = user.economy.get(guildId);

        // تحديث الرصيد
        if (type === 'add') {
            guildEconomy[location] += amount;
        } else if (type === 'remove') {
            guildEconomy[location] -= amount;
            if (guildEconomy[location] < 0) guildEconomy[location] = 0;
        } else if (type === 'set') {
            guildEconomy[location] = amount;
        }

        // حفظ التغييرات
        user.economy.set(guildId, guildEconomy);
        await user.save();

        return user;
    } catch (err) {
        console.error('خطأ في تحديث رصيد المستخدم:', err);
        throw err;
    }
}

// دالة لإضافة تحذير للمستخدم
async function addWarning(userId, guildId, reason, moderatorId) {
    try {
        const user = await User.findOne({ userId });

        if (!user) {
            throw new Error('المستخدم غير موجود');
        }

        // التأكد من وجود تحذيرات للسيرفر
        if (!user.warnings.has(guildId)) {
            user.warnings.set(guildId, []);
        }

        const guildWarnings = user.warnings.get(guildId);

        // إضافة التحذير
        guildWarnings.push({
            reason,
            moderator: moderatorId,
            timestamp: new Date()
        });

        // حفظ التغييرات
        user.warnings.set(guildId, guildWarnings);
        await user.save();

        return user;
    } catch (err) {
        console.error('خطأ في إضافة تحذير للمستخدم:', err);
        throw err;
    }
}

*/

module.exports = {
    getUserData
};
