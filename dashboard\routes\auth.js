/**
 * CS Bot - مسارات المصادقة
 *
 * هذا الملف يحتوي على مسارات تسجيل الدخول والخروج
 */

const express = require('express');
const router = express.Router();
const axios = require('axios');

// صفحة تسجيل الدخول
router.get('/login', (req, res) => {
    // التحقق من وجود جلسة مستخدم
    if (req.session && req.session.user) {
        return res.redirect('/dashboard');
    }

    res.render('login', {
        user: null,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// تسجيل دخول سريع للمطور (للطوارئ فقط)
router.post('/emergency-login', (req, res) => {
    const { code } = req.body;
    const config = req.app.locals.config;

    // كود طوارئ خاص (يمكن تغييره)
    const emergencyCode = `${config.owner.id.slice(-4)}${config.clientId.slice(-4)}`;

    if (code === emergencyCode) {
        req.session.user = {
            id: config.owner.id,
            username: 'المطور',
            discriminator: '0001',
            avatar: null,
            isEmergency: true
        };

        res.json({ success: true, message: 'تم تسجيل الدخول الطارئ بنجاح' });
    } else {
        res.status(401).json({ error: 'كود الطوارئ غير صحيح' });
    }
});

// مسار تسجيل الدخول باستخدام Discord
router.get('/discord', (req, res) => {
    const config = req.app.locals.config;

    // إنشاء رابط OAuth Discord
    const discordAuthURL = `https://discord.com/api/oauth2/authorize?` +
        `client_id=${config.clientId}&` +
        `redirect_uri=${encodeURIComponent(config.dashboard.callbackURL)}&` +
        `response_type=code&` +
        `scope=identify%20guilds`;

    res.redirect(discordAuthURL);
});

// مسار استقبال البيانات من Discord بعد تسجيل الدخول
router.get('/callback', async (req, res) => {
    const { code } = req.query;
    const config = req.app.locals.config;

    if (!code) {
        return res.redirect('/auth/login?error=no_code');
    }

    try {
        // الحصول على access token
        const tokenResponse = await axios.post('https://discord.com/api/oauth2/token',
            new URLSearchParams({
                client_id: config.clientId,
                client_secret: config.clientSecret,
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: config.dashboard.callbackURL
            }),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        const { access_token } = tokenResponse.data;

        // الحصول على معلومات المستخدم
        const userResponse = await axios.get('https://discord.com/api/users/@me', {
            headers: {
                Authorization: `Bearer ${access_token}`
            }
        });

        const user = userResponse.data;

        // قائمة المطورين المسموح لهم
        const allowedOwners = [
            config.owner.id,
            "1289581696995561495", // معرفك الأساسي
            // يمكن إضافة معرفات أخرى هنا
        ];

        // التحقق من أن المستخدم مطور مسموح له
        if (!allowedOwners.includes(user.id)) {
            return res.status(403).render('error', {
                error: 'ممنوع الدخول',
                message: `هذه لوحة التحكم مخصصة للمطورين فقط.<br>معرفك: ${user.id}<br>المطورين المسموح لهم: ${allowedOwners.join(', ')}`,
                user: null,
                config: config
            });
        }

        // حفظ بيانات المستخدم في الجلسة
        req.session.user = {
            id: user.id,
            username: user.username,
            discriminator: user.discriminator,
            avatar: user.avatar,
            access_token: access_token
        };

        // حفظ بيانات المستخدم في قاعدة البيانات المؤقتة
        const memoryDB = require('../../utils/memoryDB');
        await memoryDB.getUser(user.id, user.username, user.discriminator, user.avatar);

        res.redirect('/dashboard');

    } catch (error) {
        console.error('خطأ في مصادقة Discord:', error);
        console.error('تفاصيل الخطأ:', error.response?.data || error.message);

        // إرسال تفاصيل أكثر عن الخطأ
        let errorType = 'auth_failed';
        if (error.response?.status === 400) {
            errorType = 'invalid_code';
        } else if (error.response?.status === 401) {
            errorType = 'unauthorized';
        }

        res.redirect(`/auth/login?error=${errorType}&message=${encodeURIComponent(error.message)}`);
    }
});

// مسار تسجيل الخروج
router.get('/logout', (req, res) => {
    if (req.session) {
        req.session.destroy((err) => {
            if (err) {
                console.error('خطأ في تسجيل الخروج:', err);
            }
            res.redirect('/');
        });
    } else {
        res.redirect('/');
    }
});

module.exports = router;
