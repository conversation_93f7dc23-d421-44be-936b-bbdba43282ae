/**
 * CS Bot - مسارات المصادقة
 * 
 * هذا الملف يحتوي على مسارات تسجيل الدخول والخروج
 */

const express = require('express');
const passport = require('passport');
const router = express.Router();

// صفحة تسجيل الدخول
router.get('/login', (req, res) => {
    if (req.isAuthenticated()) return res.redirect('/dashboard');
    res.render('login', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// مسار تسجيل الدخول باستخدام Discord
router.get('/discord', passport.authenticate('discord'));

// مسار استقبال البيانات من Discord بعد تسجيل الدخول
router.get('/callback', passport.authenticate('discord', {
    failureRedirect: '/auth/login'
}), (req, res) => {
    if (req.session.backURL) {
        const url = req.session.backURL;
        req.session.backURL = null;
        res.redirect(url);
    } else {
        res.redirect('/dashboard');
    }
});

// مسار تسجيل الخروج
router.get('/logout', (req, res) => {
    req.session.destroy(() => {
        req.logout(() => {
            res.redirect('/');
        });
    });
});

module.exports = router;
