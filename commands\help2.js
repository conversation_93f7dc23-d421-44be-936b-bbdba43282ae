/**
 * أمر المساعدة المحدث - CS Bot
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const config = require('../config');

module.exports = {
    name: 'help2',
    description: 'عرض قائمة الأوامر المحدثة',
    aliases: ['مساعدة2', 'h2'],
    category: 'عام',
    usage: 'help2 [اسم الأمر]',
    cooldown: 3,
    
    async execute(message, args, client) {
        if (args[0]) {
            // عرض تفاصيل أمر محدد
            const commandName = args[0].toLowerCase();
            const command = client.commands.get(commandName) || 
                           client.commands.find(cmd => cmd.aliases && cmd.aliases.includes(commandName));

            if (!command) {
                const embed = new EmbedBuilder()
                    .setTitle('❌ أمر غير موجود')
                    .setDescription(`لم أجد أمر باسم \`${commandName}\``)
                    .setColor('#ff0000');
                return message.reply({ embeds: [embed] });
            }

            const embed = new EmbedBuilder()
                .setTitle(`📖 تفاصيل الأمر: ${command.name}`)
                .addFields([
                    {
                        name: '📝 الوصف',
                        value: command.description || 'لا يوجد وصف',
                        inline: false
                    },
                    {
                        name: '💡 الاستخدام',
                        value: `\`${config.prefix}${command.usage || command.name}\``,
                        inline: false
                    },
                    {
                        name: '📂 الفئة',
                        value: command.category || 'عام',
                        inline: true
                    },
                    {
                        name: '⏱️ فترة الانتظار',
                        value: `${command.cooldown || 3} ثانية`,
                        inline: true
                    }
                ])
                .setColor('#0099ff')
                .setThumbnail(client.user.displayAvatarURL())
                .setTimestamp();

            if (command.aliases && command.aliases.length > 0) {
                embed.addFields([
                    {
                        name: '🔄 الأسماء البديلة',
                        value: command.aliases.map(alias => `\`${alias}\``).join(', '),
                        inline: false
                    }
                ]);
            }

            return message.reply({ embeds: [embed] });
        }

        // عرض قائمة الأوامر الرئيسية
        const embed = new EmbedBuilder()
            .setTitle('📚 CS Bot - دليل الأوامر الشامل')
            .setDescription('🚀 **البوت الأسطوري مع أكثر من 50 أمر متقدم!**\n\nاختر فئة من الأزرار أدناه أو استخدم `!help2 <اسم الأمر>` للتفاصيل')
            .addFields([
                {
                    name: '💰 نظام الاقتصاد المتطور',
                    value: '• عملة افتراضية متقدمة\n• مكافآت يومية مع سلاسل\n• نظام عمل متنوع\n• متجر شامل ومقامرة\n• ترتيب الأثرياء',
                    inline: true
                },
                {
                    name: '🎮 مركز الألعاب التفاعلي',
                    value: '• 7 ألعاب مختلفة\n• حجر ورقة مقص\n• إكس أو مع الأصدقاء\n• أسئلة ثقافية\n• ألعاب الذاكرة والرياضيات',
                    inline: true
                },
                {
                    name: '🛡️ إدارة متقدمة',
                    value: '• إدارة القنوات الذكية\n• إدارة المستخدمين\n• نسخ احتياطية\n• إحصائيات مفصلة\n• تنظيف متقدم',
                    inline: true
                },
                {
                    name: '📊 معلومات وإحصائيات',
                    value: '• معلومات المستخدمين\n• إحصائيات السيرفر\n• معلومات البوت\n• اختبار الأنظمة',
                    inline: true
                },
                {
                    name: '⚙️ إعدادات متقدمة',
                    value: '• إعدادات السيرفر\n• تغيير اللغة\n• نظام البريميوم\n• تخصيص البادئة',
                    inline: true
                },
                {
                    name: '🌟 ميزات خاصة',
                    value: '• لوحة تحكم ويب\n• Slash Commands\n• نظام إشعارات\n• دعم متعدد اللغات',
                    inline: true
                }
            ])
            .setColor('#667eea')
            .setThumbnail(client.user.displayAvatarURL())
            .setImage('https://via.placeholder.com/800x200/667eea/ffffff?text=CS+Bot+-+The+Ultimate+Discord+Bot')
            .setFooter({ 
                text: `${client.commands.size} أمر متاح | ${client.guilds.cache.size} سيرفر | ${client.users.cache.size} مستخدم`,
                iconURL: client.user.displayAvatarURL()
            })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('help_economy')
                    .setLabel('💰 الاقتصاد')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💰'),
                new ButtonBuilder()
                    .setCustomId('help_games')
                    .setLabel('🎮 الألعاب')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🎮'),
                new ButtonBuilder()
                    .setCustomId('help_admin')
                    .setLabel('🛡️ الإدارة')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🛡️'),
                new ButtonBuilder()
                    .setCustomId('help_info')
                    .setLabel('📊 المعلومات')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('help_settings')
                    .setLabel('⚙️ الإعدادات')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setLabel('🌐 لوحة التحكم')
                    .setStyle(ButtonStyle.Link)
                    .setURL('http://localhost:3000')
                    .setEmoji('🌐'),
                new ButtonBuilder()
                    .setLabel('📞 الدعم')
                    .setStyle(ButtonStyle.Link)
                    .setURL('https://discord.gg/support')
                    .setEmoji('📞'),
                new ButtonBuilder()
                    .setCustomId('help_slash')
                    .setLabel('⚡ Slash Commands')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('⚡')
            );

        const msg = await message.reply({ embeds: [embed], components: [row1, row2] });

        // معالج الأزرار
        const collector = msg.createMessageComponentCollector({
            filter: i => i.user.id === message.author.id,
            time: 300000 // 5 دقائق
        });

        collector.on('collect', async (interaction) => {
            let helpEmbed;

            switch (interaction.customId) {
                case 'help_economy':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('💰 أوامر الاقتصاد')
                        .setDescription('نظام اقتصادي متكامل مع عملة افتراضية')
                        .addFields([
                            {
                                name: '💵 الأوامر الأساسية',
                                value: '`!economy balance` - عرض رصيدك\n`!economy daily` - مكافأة يومية\n`!economy work` - العمل لكسب المال',
                                inline: true
                            },
                            {
                                name: '🛒 التسوق والتداول',
                                value: '`!economy shop` - المتجر\n`!economy transfer @user amount` - تحويل المال\n`!economy leaderboard` - ترتيب الأثرياء',
                                inline: true
                            },
                            {
                                name: '🎲 المقامرة والمتعة',
                                value: '`!economy gamble amount` - قامر بأموالك\n`/economy` - استخدم Slash Commands\n`!economy help` - مساعدة مفصلة',
                                inline: true
                            }
                        ])
                        .setColor('#00ff00');
                    break;

                case 'help_games':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('🎮 مركز الألعاب')
                        .setDescription('7 ألعاب تفاعلية ممتعة مع مكافآت')
                        .addFields([
                            {
                                name: '🎯 ألعاب كلاسيكية',
                                value: '`!games rps` - حجر ورقة مقص\n`!games tictactoe @user` - إكس أو\n`!games slots amount` - ماكينة الحظ',
                                inline: true
                            },
                            {
                                name: '🧠 ألعاب ذكية',
                                value: '`!games trivia` - أسئلة ثقافية\n`!games guess` - تخمين الرقم\n`!games memory` - لعبة الذاكرة',
                                inline: true
                            },
                            {
                                name: '🔢 تحديات',
                                value: '`!games math` - تحدي الرياضيات\n`/games` - استخدم Slash Commands\n`!games help` - قواعد الألعاب',
                                inline: true
                            }
                        ])
                        .setColor('#ff6b6b');
                    break;

                case 'help_admin':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('🛡️ أوامر الإدارة')
                        .setDescription('أدوات إدارة متقدمة للمديرين')
                        .addFields([
                            {
                                name: '📺 إدارة القنوات',
                                value: '`!admin channels` - إدارة القنوات\n`!admin stats` - إحصائيات مفصلة\n`!admin backup` - نسخة احتياطية',
                                inline: true
                            },
                            {
                                name: '👥 إدارة المستخدمين',
                                value: '`!admin users` - إدارة المستخدمين\n`!admin cleanup amount` - تنظيف الرسائل\n`!admin announce message` - إعلان',
                                inline: true
                            },
                            {
                                name: '🔧 أدوات متقدمة',
                                value: '`!admin webhook` - اختبار webhook\n`!admin settings` - إعدادات السيرفر\n`!admin help` - مساعدة الإدارة',
                                inline: true
                            }
                        ])
                        .setColor('#ff0000');
                    break;

                case 'help_info':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('📊 أوامر المعلومات')
                        .setDescription('معلومات مفصلة وإحصائيات')
                        .addFields([
                            {
                                name: '👤 معلومات المستخدمين',
                                value: '`!userinfo @user` - معلومات المستخدم\n`!avatar @user` - صورة المستخدم\n`!profile @user` - الملف الشخصي',
                                inline: true
                            },
                            {
                                name: '🏠 معلومات السيرفر',
                                value: '`!serverinfo` - معلومات السيرفر\n`!channels` - قائمة القنوات\n`!roles` - قائمة الرتب',
                                inline: true
                            },
                            {
                                name: '🤖 معلومات البوت',
                                value: '`!botinfo` - معلومات البوت\n`!test` - اختبار البوت\n`!ping` - سرعة الاستجابة',
                                inline: true
                            }
                        ])
                        .setColor('#0099ff');
                    break;

                case 'help_settings':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('⚙️ أوامر الإعدادات')
                        .setDescription('تخصيص وإعداد البوت')
                        .addFields([
                            {
                                name: '🌐 إعدادات عامة',
                                value: '`!settings` - إعدادات السيرفر\n`!language ar/en` - تغيير اللغة\n`!prefix !` - تغيير البادئة',
                                inline: true
                            },
                            {
                                name: '👑 البريميوم',
                                value: '`!premium` - معلومات البريميوم\n`!premium activate` - تفعيل البريميوم\n`!premium status` - حالة البريميوم',
                                inline: true
                            },
                            {
                                name: '🔔 الإشعارات',
                                value: '`!notifications` - إعدادات الإشعارات\n`!welcome` - رسائل الترحيب\n`!logs` - سجلات الأحداث',
                                inline: true
                            }
                        ])
                        .setColor('#9b59b6');
                    break;

                case 'help_slash':
                    helpEmbed = new EmbedBuilder()
                        .setTitle('⚡ Slash Commands')
                        .setDescription('أوامر حديثة وسريعة')
                        .addFields([
                            {
                                name: '💰 اقتصاد',
                                value: '`/economy balance`\n`/economy daily`\n`/economy work`\n`/economy transfer`',
                                inline: true
                            },
                            {
                                name: '🎮 ألعاب',
                                value: '`/games rps`\n`/games trivia`\n`/games guess`\n`/games slots`',
                                inline: true
                            },
                            {
                                name: '📊 معلومات',
                                value: '`/userinfo`\n`/serverinfo`\n`/botinfo`\n`/ping`',
                                inline: true
                            }
                        ])
                        .setColor('#00ff00');
                    break;
            }

            await interaction.update({ embeds: [helpEmbed], components: [row1, row2] });
        });

        collector.on('end', async () => {
            // تعطيل الأزرار بعد انتهاء الوقت
            const disabledRow1 = new ActionRowBuilder()
                .addComponents(
                    ...row1.components.map(button => 
                        ButtonBuilder.from(button).setDisabled(true)
                    )
                );
            
            const disabledRow2 = new ActionRowBuilder()
                .addComponents(
                    ...row2.components.map(button => 
                        button.data.style === ButtonStyle.Link ? button : ButtonBuilder.from(button).setDisabled(true)
                    )
                );

            await msg.edit({ components: [disabledRow1, disabledRow2] }).catch(() => {});
        });
    }
};
