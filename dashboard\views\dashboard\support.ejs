<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدعم والمساعدة | CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .support-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }

        .support-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .support-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .contact-method {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .contact-method:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .faq-item {
            background: #f8f9fa;
            border-radius: 15px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-question {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            cursor: pointer;
            margin: 0;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .faq-answer {
            padding: 1.5rem;
            display: none;
            line-height: 1.6;
        }

        .faq-answer.show {
            display: block;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 0.5rem;
        }

        .status-online { background: #28a745; }
        .status-busy { background: #ffc107; }
        .status-offline { background: #dc3545; }

        .ticket-form {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس الصفحة -->
            <div class="support-header">
                <h1 class="text-white mb-3">
                    <i class="fas fa-life-ring fa-3x mb-3 d-block"></i>
                    الدعم والمساعدة
                </h1>
                <p class="text-white-50 lead mb-0">
                    نحن هنا لمساعدتك! اختر الطريقة المناسبة للحصول على الدعم
                </p>
            </div>

            <div class="row">
                <!-- طرق التواصل -->
                <div class="col-lg-8">
                    <div class="support-card">
                        <h2 class="mb-4">
                            <i class="fas fa-headset text-primary me-2"></i>
                            طرق التواصل
                        </h2>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="contact-method">
                                    <i class="fab fa-discord fa-3x text-primary mb-3"></i>
                                    <h4>خادم Discord</h4>
                                    <p class="text-muted mb-3">انضم لخادم الدعم الخاص بنا للحصول على مساعدة فورية</p>
                                    <div class="mb-3">
                                        <span class="status-indicator status-online"></span>
                                        <small>متاح 24/7</small>
                                    </div>
                                    <a href="https://discord.gg/csbot" target="_blank" class="btn btn-primary">
                                        <i class="fab fa-discord me-2"></i>انضم الآن
                                    </a>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="contact-method">
                                    <i class="fas fa-envelope fa-3x text-success mb-3"></i>
                                    <h4>البريد الإلكتروني</h4>
                                    <p class="text-muted mb-3">أرسل لنا رسالة مفصلة وسنرد عليك خلال 24 ساعة</p>
                                    <div class="mb-3">
                                        <span class="status-indicator status-online"></span>
                                        <small>رد خلال 24 ساعة</small>
                                    </div>
                                    <a href="mailto:<EMAIL>" class="btn btn-success">
                                        <i class="fas fa-envelope me-2"></i>إرسال رسالة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="contact-method">
                                    <i class="fas fa-ticket-alt fa-3x text-warning mb-3"></i>
                                    <h4>نظام التذاكر</h4>
                                    <p class="text-muted mb-3">أنشئ تذكرة دعم لمتابعة مشكلتك بشكل منظم</p>
                                    <div class="mb-3">
                                        <span class="status-indicator status-online"></span>
                                        <small>متابعة مخصصة</small>
                                    </div>
                                    <button class="btn btn-warning" onclick="openTicketModal()">
                                        <i class="fas fa-ticket-alt me-2"></i>إنشاء تذكرة
                                    </button>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="contact-method">
                                    <i class="fas fa-book fa-3x text-info mb-3"></i>
                                    <h4>التوثيق</h4>
                                    <p class="text-muted mb-3">اطلع على الدليل الشامل لاستخدام البوت</p>
                                    <div class="mb-3">
                                        <span class="status-indicator status-online"></span>
                                        <small>محدث باستمرار</small>
                                    </div>
                                    <a href="/docs" class="btn btn-info">
                                        <i class="fas fa-book me-2"></i>عرض التوثيق
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأسئلة الشائعة -->
                    <div class="support-card">
                        <h2 class="mb-4">
                            <i class="fas fa-question-circle text-primary me-2"></i>
                            الأسئلة الشائعة
                        </h2>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFaq(this)">
                                <span>كيف أضيف البوت لخادمي؟</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>لإضافة البوت لخادمك، اتبع هذه الخطوات:</p>
                                <ol>
                                    <li>اذهب إلى صفحة "سيرفراتي" في لوحة التحكم</li>
                                    <li>اضغط على زر "إضافة البوت" بجانب الخادم المطلوب</li>
                                    <li>ستتم إعادة توجيهك لصفحة Discord للموافقة على الصلاحيات</li>
                                    <li>اضغط "Authorize" لإضافة البوت</li>
                                </ol>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFaq(this)">
                                <span>كيف أغير إعدادات البوت؟</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>يمكنك تغيير إعدادات البوت من خلال:</p>
                                <ul>
                                    <li>الدخول لصفحة "إدارة السيرفر" من لوحة التحكم</li>
                                    <li>استخدام الإعدادات السريعة في أعلى الصفحة</li>
                                    <li>أو الدخول لصفحات الميزات المختلفة لتخصيص كل ميزة</li>
                                </ul>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFaq(this)">
                                <span>ما هي ميزات البريميوم؟</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>ميزات البريميوم تشمل:</p>
                                <ul>
                                    <li>أوامر متقدمة إضافية</li>
                                    <li>تخصيص أكبر للرسائل والإعدادات</li>
                                    <li>إحصائيات مفصلة</li>
                                    <li>دعم أولوية</li>
                                    <li>ميزات حصرية جديدة</li>
                                </ul>
                            </div>
                        </div>

                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFaq(this)">
                                <span>البوت لا يستجيب للأوامر، ما الحل؟</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="faq-answer">
                                <p>إذا كان البوت لا يستجيب، تحقق من:</p>
                                <ol>
                                    <li>أن البوت متصل (يظهر كـ Online)</li>
                                    <li>أن البوت لديه الصلاحيات المطلوبة</li>
                                    <li>أن تكتب الأوامر بالشكل الصحيح</li>
                                    <li>أن القناة ليست محظورة من استخدام البوت</li>
                                </ol>
                                <p>إذا استمرت المشكلة، تواصل معنا عبر Discord.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-lg-4">
                    <div class="support-card">
                        <h3 class="mb-3">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            معلومات مفيدة
                        </h3>
                        
                        <div class="alert alert-success">
                            <i class="fas fa-clock me-2"></i>
                            <strong>أوقات الدعم:</strong><br>
                            24/7 عبر Discord<br>
                            9 ص - 9 م عبر البريد الإلكتروني
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-language me-2"></i>
                            <strong>اللغات المدعومة:</strong><br>
                            العربية، الإنجليزية
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>قبل التواصل:</strong><br>
                            تأكد من مراجعة الأسئلة الشائعة أولاً
                        </div>

                        <h4 class="mt-4 mb-3">روابط سريعة</h4>
                        <div class="d-grid gap-2">
                            <a href="/dashboard/terms" class="btn btn-outline-primary">
                                <i class="fas fa-file-contract me-2"></i>الشروط والأحكام
                            </a>
                            <a href="/dashboard/privacy" class="btn btn-outline-primary">
                                <i class="fas fa-user-shield me-2"></i>سياسة الخصوصية
                            </a>
                            <a href="/docs" class="btn btn-outline-primary">
                                <i class="fas fa-book me-2"></i>التوثيق
                            </a>
                        </div>
                    </div>

                    <!-- حالة الخدمة -->
                    <div class="support-card">
                        <h4 class="mb-3">
                            <i class="fas fa-server text-success me-2"></i>
                            حالة الخدمة
                        </h4>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>البوت الرئيسي</span>
                            <span class="badge bg-success">متاح</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>لوحة التحكم</span>
                            <span class="badge bg-success">متاح</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>قاعدة البيانات</span>
                            <span class="badge bg-success">متاح</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>خادم الدعم</span>
                            <span class="badge bg-success">متاح</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إنشاء تذكرة -->
    <div class="modal fade" id="ticketModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-ticket-alt me-2"></i>إنشاء تذكرة دعم
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ticketForm">
                        <div class="mb-3">
                            <label for="ticketSubject" class="form-label">موضوع التذكرة</label>
                            <input type="text" class="form-control" id="ticketSubject" required>
                        </div>
                        <div class="mb-3">
                            <label for="ticketCategory" class="form-label">فئة المشكلة</label>
                            <select class="form-select" id="ticketCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="technical">مشكلة تقنية</option>
                                <option value="feature">طلب ميزة</option>
                                <option value="billing">مشكلة في الفوترة</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="ticketPriority" class="form-label">الأولوية</label>
                            <select class="form-select" id="ticketPriority" required>
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="ticketDescription" class="form-label">وصف المشكلة</label>
                            <textarea class="form-control" id="ticketDescription" rows="5" required 
                                      placeholder="اشرح المشكلة بالتفصيل..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitTicket()">
                        <i class="fas fa-paper-plane me-2"></i>إرسال التذكرة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            answer.classList.toggle('show');
            icon.classList.toggle('fa-chevron-down');
            icon.classList.toggle('fa-chevron-up');
        }

        function openTicketModal() {
            new bootstrap.Modal(document.getElementById('ticketModal')).show();
        }

        function submitTicket() {
            const form = document.getElementById('ticketForm');
            const formData = new FormData(form);
            
            // هنا يمكن إضافة منطق إرسال التذكرة
            alert('تم إرسال التذكرة بنجاح! سنتواصل معك قريباً.');
            bootstrap.Modal.getInstance(document.getElementById('ticketModal')).hide();
        }
    </script>
</body>
</html>
