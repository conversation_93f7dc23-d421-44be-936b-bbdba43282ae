<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-robot"></i> CS Bot - إدارة المستخدمين
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </h2>
                    <p class="card-text">إدارة شاملة لجميع مستخدمي البوت مع إمكانية إعطاء البريميوم والحظر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchUser" placeholder="البحث عن مستخدم...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterPremium">
                                <option value="">جميع المستخدمين</option>
                                <option value="premium">أعضاء البريميوم فقط</option>
                                <option value="normal">أعضاء عاديين فقط</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="banned">محظور</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadUsers()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> قائمة المستخدمين
                        <span class="badge bg-light text-dark ms-2" id="userCountBadge">0</span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الصورة</th>
                                    <th>المستخدم</th>
                                    <th>المعرف</th>
                                    <th>الحالة</th>
                                    <th>آخر نشاط</th>
                                    <th>البريميوم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p class="mt-2">جاري تحميل المستخدمين...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium Modal -->
<div class="modal fade" id="premiumModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-crown"></i> إدارة البريميوم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="premiumForm">
                    <input type="hidden" id="premiumUserId">
                    <div class="mb-3">
                        <label class="form-label">المستخدم:</label>
                        <p id="premiumUserName" class="fw-bold"></p>
                    </div>
                    <div class="mb-3">
                        <label for="premiumDuration" class="form-label">مدة البريميوم:</label>
                        <select class="form-select" id="premiumDuration" required>
                            <option value="">اختر المدة</option>
                            <option value="1m">1 دقيقة (للاختبار)</option>
                            <option value="1h">1 ساعة</option>
                            <option value="1d">1 يوم</option>
                            <option value="7d">1 أسبوع</option>
                            <option value="30d">1 شهر</option>
                            <option value="365d">1 سنة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="givePremium()">
                    <i class="fas fa-crown"></i> إعطاء البريميوم
                </button>
                <button type="button" class="btn btn-danger" onclick="removePremium()">
                    <i class="fas fa-times"></i> إزالة البريميوم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-ban"></i> حظر المستخدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="banForm">
                    <input type="hidden" id="banUserId">
                    <div class="mb-3">
                        <label class="form-label">المستخدم:</label>
                        <p id="banUserName" class="fw-bold"></p>
                    </div>
                    <div class="mb-3">
                        <label for="banReason" class="form-label">سبب الحظر:</label>
                        <textarea class="form-control" id="banReason" rows="3" placeholder="اكتب سبب الحظر..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="banUser()">
                    <i class="fas fa-ban"></i> حظر المستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let allUsers = [];

// تحميل المستخدمين
async function loadUsers() {
    try {
        const response = await fetch('/api/dashboard/users');
        const data = await response.json();
        
        allUsers = data.users || [];
        displayUsers(allUsers);
        
        document.getElementById('userCountBadge').textContent = allUsers.length;
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        document.getElementById('usersTableBody').innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4 text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                    <p class="mt-2">حدث خطأ في تحميل المستخدمين</p>
                </td>
            </tr>
        `;
    }
}

// عرض المستخدمين
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-users fa-2x text-muted"></i>
                    <p class="mt-2">لا توجد مستخدمين</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <img src="https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png" 
                     alt="${user.username}" 
                     class="rounded-circle" 
                     width="40" height="40"
                     onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            </td>
            <td>
                <strong>${user.username}</strong>
                ${user.premium?.enabled ? '<span class="badge bg-warning ms-1"><i class="fas fa-crown"></i></span>' : ''}
            </td>
            <td><code>${user.id}</code></td>
            <td>
                <span class="badge ${user.banned ? 'bg-danger' : 'bg-success'}">
                    ${user.banned ? 'محظور' : 'نشط'}
                </span>
            </td>
            <td>
                <small class="text-muted">
                    ${user.lastSeen ? new Date(user.lastSeen).toLocaleString('ar-SA') : 'غير معروف'}
                </small>
            </td>
            <td>
                ${user.premium?.enabled ? `
                    <span class="badge bg-warning">
                        <i class="fas fa-crown"></i> نشط
                    </span>
                    <br>
                    <small class="text-muted">
                        ينتهي: ${new Date(user.premium.expiresAt).toLocaleDateString('ar-SA')}
                    </small>
                ` : '<span class="badge bg-secondary">غير مشترك</span>'}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-warning" onclick="openPremiumModal('${user.id}', '${user.username}')">
                        <i class="fas fa-crown"></i>
                    </button>
                    <button class="btn ${user.banned ? 'btn-success' : 'btn-danger'}" 
                            onclick="${user.banned ? `unbanUser('${user.id}')` : `openBanModal('${user.id}', '${user.username}')`}">
                        <i class="fas fa-${user.banned ? 'check' : 'ban'}"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// فتح نافذة البريميوم
function openPremiumModal(userId, username) {
    document.getElementById('premiumUserId').value = userId;
    document.getElementById('premiumUserName').textContent = username;
    new bootstrap.Modal(document.getElementById('premiumModal')).show();
}

// إعطاء البريميوم
async function givePremium() {
    const userId = document.getElementById('premiumUserId').value;
    const duration = document.getElementById('premiumDuration').value;
    
    if (!duration) {
        alert('يرجى اختيار مدة البريميوم');
        return;
    }
    
    try {
        const response = await fetch('/api/dashboard/premium/add', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId, duration })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم إعطاء البريميوم بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('premiumModal')).hide();
            loadUsers();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إعطاء البريميوم');
    }
}

// إزالة البريميوم
async function removePremium() {
    const userId = document.getElementById('premiumUserId').value;
    
    if (!confirm('هل أنت متأكد من إزالة البريميوم؟')) return;
    
    try {
        const response = await fetch('/api/dashboard/premium/remove', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم إزالة البريميوم بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('premiumModal')).hide();
            loadUsers();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إزالة البريميوم');
    }
}

// فتح نافذة الحظر
function openBanModal(userId, username) {
    document.getElementById('banUserId').value = userId;
    document.getElementById('banUserName').textContent = username;
    new bootstrap.Modal(document.getElementById('banModal')).show();
}

// حظر المستخدم
async function banUser() {
    const userId = document.getElementById('banUserId').value;
    const reason = document.getElementById('banReason').value;
    
    if (!confirm('هل أنت متأكد من حظر هذا المستخدم؟')) return;
    
    try {
        const response = await fetch('/api/dashboard/ban', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId, reason })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم حظر المستخدم بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('banModal')).hide();
            loadUsers();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في حظر المستخدم');
    }
}

// إلغاء حظر المستخدم
async function unbanUser(userId) {
    if (!confirm('هل أنت متأكد من إلغاء حظر هذا المستخدم؟')) return;
    
    try {
        const response = await fetch('/api/dashboard/unban', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم إلغاء حظر المستخدم بنجاح!');
            loadUsers();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إلغاء الحظر');
    }
}

// تحميل المستخدمين عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadUsers);
</script>

</body>
</html>
