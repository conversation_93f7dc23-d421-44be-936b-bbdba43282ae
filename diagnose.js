/**
 * أداة تشخيص المشاكل - CS Bot
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 تشخيص مشاكل CS Bot...\n');

// فحص الملفات الأساسية
console.log('📁 فحص الملفات الأساسية:');
const coreFiles = [
    'index.js',
    'config.js',
    'package.json',
    'start.js'
];

coreFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const stats = fs.statSync(file);
        console.log(`✅ ${file} (${Math.round(stats.size / 1024)}KB)`);
    } else {
        console.log(`❌ ${file} - غير موجود`);
    }
});

// فحص المجلدات
console.log('\n📂 فحص المجلدات:');
const directories = [
    'commands',
    'slashCommands', 
    'dashboard',
    'utils',
    'models'
];

directories.forEach(dir => {
    if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        console.log(`✅ ${dir}/ (${files.length} ملف)`);
    } else {
        console.log(`❌ ${dir}/ - غير موجود`);
    }
});

// فحص الأوامر
console.log('\n⚡ فحص الأوامر:');
if (fs.existsSync('commands')) {
    const commandFiles = [];
    
    function scanCommands(dir, prefix = '') {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanCommands(fullPath, prefix + item + '/');
            } else if (item.endsWith('.js')) {
                commandFiles.push(prefix + item);
            }
        });
    }
    
    scanCommands('commands');
    console.log(`📝 تم العثور على ${commandFiles.length} أمر:`);
    commandFiles.forEach(file => console.log(`   • ${file}`));
} else {
    console.log('❌ مجلد commands غير موجود');
}

// فحص Slash Commands
console.log('\n⚡ فحص Slash Commands:');
if (fs.existsSync('slashCommands')) {
    const slashFiles = [];
    
    function scanSlashCommands(dir, prefix = '') {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanSlashCommands(fullPath, prefix + item + '/');
            } else if (item.endsWith('.js')) {
                slashFiles.push(prefix + item);
            }
        });
    }
    
    scanSlashCommands('slashCommands');
    console.log(`📝 تم العثور على ${slashFiles.length} slash command:`);
    slashFiles.forEach(file => console.log(`   • ${file}`));
} else {
    console.log('❌ مجلد slashCommands غير موجود');
}

// فحص ملف .env
console.log('\n🔐 فحص ملف .env:');
if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    const lines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    console.log(`✅ ملف .env موجود (${lines.length} إعداد)`);
    
    const importantVars = ['DISCORD_TOKEN', 'CLIENT_ID', 'MONGODB_URI'];
    importantVars.forEach(varName => {
        const hasVar = lines.some(line => line.startsWith(varName + '='));
        console.log(`   ${hasVar ? '✅' : '❌'} ${varName}`);
    });
} else {
    console.log('⚠️ ملف .env غير موجود - سيتم استخدام القيم الافتراضية');
}

// فحص package.json
console.log('\n📦 فحص package.json:');
try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    console.log(`✅ اسم المشروع: ${packageJson.name}`);
    console.log(`✅ الإصدار: ${packageJson.version}`);
    
    const deps = Object.keys(packageJson.dependencies || {});
    console.log(`✅ المكتبات: ${deps.length} مكتبة`);
    
    const requiredDeps = ['discord.js', 'mongoose', 'express', 'dotenv'];
    requiredDeps.forEach(dep => {
        const hasDepency = deps.includes(dep);
        console.log(`   ${hasDepency ? '✅' : '❌'} ${dep}`);
    });
    
} catch (error) {
    console.log('❌ خطأ في قراءة package.json:', error.message);
}

// فحص Node.js
console.log('\n💻 معلومات النظام:');
console.log(`✅ Node.js: ${process.version}`);
console.log(`✅ المنصة: ${process.platform}`);
console.log(`✅ المعمارية: ${process.arch}`);
console.log(`✅ الذاكرة: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);

// اختبار تحميل الملفات الأساسية
console.log('\n🧪 اختبار تحميل الملفات:');

try {
    const config = require('./config.js');
    console.log('✅ config.js - تم التحميل بنجاح');
    console.log(`   البادئة: ${config.prefix}`);
    console.log(`   منفذ لوحة التحكم: ${config.dashboard?.port || 'غير محدد'}`);
} catch (error) {
    console.log('❌ config.js - فشل التحميل:', error.message);
}

// فحص لوحة التحكم
console.log('\n🌐 فحص لوحة التحكم:');
const dashboardFiles = [
    'dashboard/app.js',
    'dashboard/views',
    'dashboard/public',
    'dashboard/routes'
];

dashboardFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - غير موجود`);
    }
});

// نصائح لحل المشاكل
console.log('\n💡 نصائح لحل المشاكل:');
console.log('1. تأكد من تثبيت جميع المكتبات: npm install');
console.log('2. تأكد من وجود ملف .env مع التوكن الصحيح');
console.log('3. تأكد من إصدار Node.js 16 أو أحدث');
console.log('4. تحقق من صلاحيات الملفات والمجلدات');
console.log('5. راجع سجل الأخطاء في وحدة التحكم');

console.log('\n✅ انتهى التشخيص!');
console.log('📞 للدعم: راجع ملف README.md أو اتصل بالمطور');
