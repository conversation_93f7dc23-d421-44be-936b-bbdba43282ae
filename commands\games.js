const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, But<PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'games',
    description: 'ألعاب تفاعلية ممتعة',
    usage: '!games [نوع اللعبة]',
    category: 'ألعاب',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            const gameType = args[0]?.toLowerCase();
            
            switch (gameType) {
                case 'tictactoe':
                case 'xo':
                    return this.startTicTacToe(message);
                case 'guess':
                case 'number':
                    return this.startNumberGuess(message);
                case 'word':
                case 'كلمة':
                    return this.startWordGame(message);
                case 'math':
                case 'رياضيات':
                    return this.startMathGame(message);
                case 'memory':
                case 'ذاكرة':
                    return this.startMemoryGame(message);
                case 'riddle':
                case 'لغز':
                    return this.startRiddleGame(message);
                default:
                    return this.showGamesMenu(message);
            }
            
        } catch (error) {
            console.error('خطأ في أمر الألعاب:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ في الألعاب')
                .setDescription('حدث خطأ أثناء تشغيل اللعبة')
                .setTimestamp();
                
            return message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async showGamesMenu(message) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('🎮 مركز الألعاب التفاعلية')
            .setDescription('اختر لعبتك المفضلة واستمتع!')
            .addFields([
                {
                    name: '🎯 ألعاب الذكاء',
                    value: '• **XO** - لعبة إكس أو\n• **تخمين الرقم** - خمن الرقم السري\n• **الرياضيات** - حل المعادلات',
                    inline: true
                },
                {
                    name: '🧩 ألعاب الكلمات',
                    value: '• **لعبة الكلمات** - اكمل الكلمة\n• **الألغاز** - حل اللغز\n• **الذاكرة** - تذكر التسلسل',
                    inline: true
                },
                {
                    name: '🏆 المكافآت',
                    value: '• **نقاط الخبرة** عند الفوز\n• **عملات ذهبية** للألعاب الصعبة\n• **ألقاب خاصة** للمحترفين',
                    inline: false
                }
            ])
            .setFooter({ text: 'اختر لعبة من الأزرار أدناه!' })
            .setTimestamp();
            
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('game_tictactoe')
                    .setLabel('XO')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⭕'),
                new ButtonBuilder()
                    .setCustomId('game_guess')
                    .setLabel('تخمين الرقم')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🔢'),
                new ButtonBuilder()
                    .setCustomId('game_word')
                    .setLabel('لعبة الكلمات')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId('game_math')
                    .setLabel('الرياضيات')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🧮')
            );
            
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('game_memory')
                    .setLabel('الذاكرة')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🧠'),
                new ButtonBuilder()
                    .setCustomId('game_riddle')
                    .setLabel('الألغاز')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧩'),
                new ButtonBuilder()
                    .setCustomId('game_leaderboard')
                    .setLabel('لوحة المتصدرين')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🏆'),
                new ButtonBuilder()
                    .setCustomId('game_stats')
                    .setLabel('إحصائياتي')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊')
            );
            
        return message.reply({ embeds: [embed], components: [row1, row2] });
    },
    
    async startTicTacToe(message) {
        const board = ['⬜', '⬜', '⬜', '⬜', '⬜', '⬜', '⬜', '⬜', '⬜'];
        
        const embed = new EmbedBuilder()
            .setColor('#4ecdc4')
            .setTitle('⭕ لعبة XO')
            .setDescription('أنت ❌ والبوت ⭕\nاختر مربعاً للعب!')
            .addFields([
                {
                    name: '🎯 اللوحة',
                    value: `${board[0]} ${board[1]} ${board[2]}\n${board[3]} ${board[4]} ${board[5]}\n${board[6]} ${board[7]} ${board[8]}`,
                    inline: false
                }
            ])
            .setFooter({ text: 'اضغط على الأرقام للعب!' })
            .setTimestamp();
            
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder().setCustomId('xo_0').setLabel('1').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_1').setLabel('2').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_2').setLabel('3').setStyle(ButtonStyle.Secondary)
            );
            
        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder().setCustomId('xo_3').setLabel('4').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_4').setLabel('5').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_5').setLabel('6').setStyle(ButtonStyle.Secondary)
            );
            
        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder().setCustomId('xo_6').setLabel('7').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_7').setLabel('8').setStyle(ButtonStyle.Secondary),
                new ButtonBuilder().setCustomId('xo_8').setLabel('9').setStyle(ButtonStyle.Secondary)
            );
            
        return message.reply({ embeds: [embed], components: [row1, row2, row3] });
    },
    
    async startNumberGuess(message) {
        const secretNumber = Math.floor(Math.random() * 100) + 1;
        
        const embed = new EmbedBuilder()
            .setColor('#feca57')
            .setTitle('🔢 لعبة تخمين الرقم')
            .setDescription('لقد اخترت رقماً بين 1 و 100\nحاول تخمينه!')
            .addFields([
                { name: '🎯 الهدف', value: 'خمن الرقم في أقل عدد من المحاولات', inline: true },
                { name: '💡 نصيحة', value: 'ابدأ بالرقم 50', inline: true },
                { name: '🏆 المكافأة', value: '10 نقاط للفوز', inline: true }
            ])
            .setFooter({ text: 'اكتب رقماً بين 1 و 100' })
            .setTimestamp();
            
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('guess_low')
                    .setLabel('1-25')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('guess_mid_low')
                    .setLabel('26-50')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('guess_mid_high')
                    .setLabel('51-75')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('guess_high')
                    .setLabel('76-100')
                    .setStyle(ButtonStyle.Primary)
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    },
    
    async startWordGame(message) {
        const words = [
            { word: 'برمجة', hint: 'كتابة الكود للحاسوب' },
            { word: 'ذكاء', hint: 'القدرة على التفكير والفهم' },
            { word: 'تقنية', hint: 'استخدام العلم في التطبيقات العملية' },
            { word: 'إبداع', hint: 'القدرة على الابتكار والتجديد' },
            { word: 'تعلم', hint: 'اكتساب المعرفة والمهارات' }
        ];
        
        const randomWord = words[Math.floor(Math.random() * words.length)];
        const hiddenWord = randomWord.word.split('').map(() => '\_').join(' ');
        
        const embed = new EmbedBuilder()
            .setColor('#a55eea')
            .setTitle('📝 لعبة الكلمات')
            .setDescription('خمن الكلمة حرفاً بحرف!')
            .addFields([
                { name: '🔤 الكلمة', value: hiddenWord, inline: true },
                { name: '💡 التلميح', value: randomWord.hint, inline: true },
                { name: '❤️ المحاولات المتبقية', value: '6', inline: true }
            ])
            .setFooter({ text: 'اكتب حرفاً واحداً' })
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async startMathGame(message) {
        const operations = ['+', '-', '*'];
        const operation = operations[Math.floor(Math.random() * operations.length)];
        
        let num1, num2, answer;
        
        switch (operation) {
            case '+':
                num1 = Math.floor(Math.random() * 50) + 1;
                num2 = Math.floor(Math.random() * 50) + 1;
                answer = num1 + num2;
                break;
            case '-':
                num1 = Math.floor(Math.random() * 50) + 25;
                num2 = Math.floor(Math.random() * 25) + 1;
                answer = num1 - num2;
                break;
            case '*':
                num1 = Math.floor(Math.random() * 12) + 1;
                num2 = Math.floor(Math.random() * 12) + 1;
                answer = num1 * num2;
                break;
        }
        
        const embed = new EmbedBuilder()
            .setColor('#26de81')
            .setTitle('🧮 لعبة الرياضيات')
            .setDescription('حل المعادلة بسرعة!')
            .addFields([
                { name: '📊 المعادلة', value: `**${num1} ${operation} ${num2} = ?**`, inline: true },
                { name: '⏱️ الوقت المحدد', value: '30 ثانية', inline: true },
                { name: '🏆 المكافأة', value: '15 نقطة', inline: true }
            ])
            .setFooter({ text: 'اكتب الإجابة الصحيحة' })
            .setTimestamp();
            
        const wrongAnswers = [
            answer + Math.floor(Math.random() * 10) + 1,
            answer - Math.floor(Math.random() * 10) - 1,
            answer + Math.floor(Math.random() * 5) + 1
        ];
        
        const allAnswers = [answer, ...wrongAnswers].sort(() => Math.random() - 0.5);
        
        const row = new ActionRowBuilder()
            .addComponents(
                allAnswers.slice(0, 4).map((ans, index) => 
                    new ButtonBuilder()
                        .setCustomId(`math_${ans}`)
                        .setLabel(ans.toString())
                        .setStyle(ans === answer ? ButtonStyle.Success : ButtonStyle.Secondary)
                )
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    },
    
    async startMemoryGame(message) {
        const sequence = [];
        for (let i = 0; i < 4; i++) {
            sequence.push(Math.floor(Math.random() * 4) + 1);
        }
        
        const embed = new EmbedBuilder()
            .setColor('#fd79a8')
            .setTitle('🧠 لعبة الذاكرة')
            .setDescription('احفظ التسلسل وأعد كتابته!')
            .addFields([
                { name: '🎯 التسلسل', value: `**${sequence.join(' - ')}**`, inline: true },
                { name: '⏱️ وقت الحفظ', value: '5 ثوان', inline: true },
                { name: '🏆 المكافأة', value: '20 نقطة', inline: true }
            ])
            .setFooter({ text: 'احفظ التسلسل جيداً!' })
            .setTimestamp();
            
        const msg = await message.reply({ embeds: [embed] });
        
        setTimeout(async () => {
            const hiddenEmbed = new EmbedBuilder()
                .setColor('#fd79a8')
                .setTitle('🧠 لعبة الذاكرة')
                .setDescription('الآن أعد كتابة التسلسل!')
                .addFields([
                    { name: '🎯 المطلوب', value: 'اكتب التسلسل بنفس الترتيب', inline: true },
                    { name: '💡 تلميح', value: 'كان هناك 4 أرقام', inline: true }
                ])
                .setFooter({ text: 'اكتب الأرقام مفصولة بمسافات' })
                .setTimestamp();
                
            await msg.edit({ embeds: [hiddenEmbed] });
        }, 5000);
        
        return msg;
    },
    
    async startRiddleGame(message) {
        const riddles = [
            {
                question: 'ما الشيء الذي يكتب ولا يقرأ؟',
                answer: 'القلم',
                hint: 'أداة للكتابة'
            },
            {
                question: 'ما الشيء الذي له عين ولا يرى؟',
                answer: 'الإبرة',
                hint: 'تستخدم في الخياطة'
            },
            {
                question: 'ما الشيء الذي يمشي بلا أرجل؟',
                answer: 'الوقت',
                hint: 'لا يتوقف أبداً'
            }
        ];
        
        const riddle = riddles[Math.floor(Math.random() * riddles.length)];
        
        const embed = new EmbedBuilder()
            .setColor('#00b894')
            .setTitle('🧩 لعبة الألغاز')
            .setDescription('حل اللغز واربح النقاط!')
            .addFields([
                { name: '❓ اللغز', value: riddle.question, inline: false },
                { name: '💡 تلميح', value: riddle.hint, inline: true },
                { name: '🏆 المكافأة', value: '25 نقطة', inline: true }
            ])
            .setFooter({ text: 'اكتب إجابتك' })
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    }
};
