/**
 * أوامر الألعاب - CS Bot
 * مجموعة متنوعة من الألعاب التفاعلية
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const User = require('../models/User');

module.exports = {
    name: 'games',
    description: 'ألعاب تفاعلية ممتعة',
    aliases: ['game', 'العاب', 'لعبة'],
    category: 'ترفيه',
    usage: 'games <rps|tictactoe|trivia|guess|slots>',
    cooldown: 5,
    
    async execute(message, args, client) {
        const subcommand = args[0]?.toLowerCase();

        if (!subcommand) {
            return this.showGamesMenu(message);
        }

        switch (subcommand) {
            case 'rps':
            case 'حجر':
                return this.rockPaperScissors(message, args);
            
            case 'tictactoe':
            case 'xo':
            case 'اكس_او':
                return this.ticTacToe(message, args);
            
            case 'trivia':
            case 'سؤال':
                return this.trivia(message);
            
            case 'guess':
            case 'تخمين':
                return this.guessNumber(message);
            
            case 'slots':
            case 'سلوت':
                return this.slotMachine(message, args);
            
            case 'memory':
            case 'ذاكرة':
                return this.memoryGame(message);
            
            case 'math':
            case 'رياضيات':
                return this.mathChallenge(message);
            
            default:
                return this.showGamesMenu(message);
        }
    },

    async showGamesMenu(message) {
        const embed = new EmbedBuilder()
            .setTitle('🎮 مركز الألعاب')
            .setDescription('اختر لعبة من الألعاب المتاحة:')
            .addFields([
                {
                    name: '✂️ حجر ورقة مقص',
                    value: '`games rps` - العب ضد البوت',
                    inline: true
                },
                {
                    name: '⭕ إكس أو',
                    value: '`games tictactoe @user` - العب مع صديق',
                    inline: true
                },
                {
                    name: '🧠 أسئلة ثقافية',
                    value: '`games trivia` - اختبر معلوماتك',
                    inline: true
                },
                {
                    name: '🔢 تخمين الرقم',
                    value: '`games guess` - خمن الرقم السري',
                    inline: true
                },
                {
                    name: '🎰 ماكينة الحظ',
                    value: '`games slots` - جرب حظك',
                    inline: true
                },
                {
                    name: '🧩 لعبة الذاكرة',
                    value: '`games memory` - اختبر ذاكرتك',
                    inline: true
                },
                {
                    name: '🔢 تحدي الرياضيات',
                    value: '`games math` - حل المسائل',
                    inline: true
                }
            ])
            .setColor('#ff6b6b')
            .setFooter({ text: 'CS Bot Games Center' });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('game_rps')
                    .setLabel('✂️ حجر ورقة مقص')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('game_trivia')
                    .setLabel('🧠 سؤال ثقافي')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('game_guess')
                    .setLabel('🔢 تخمين')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('game_slots')
                    .setLabel('🎰 حظ')
                    .setStyle(ButtonStyle.Danger)
            );

        return message.reply({ embeds: [embed], components: [row] });
    },

    async rockPaperScissors(message, args) {
        const choices = ['🗿', '📄', '✂️'];
        const choiceNames = ['حجر', 'ورقة', 'مقص'];
        
        const embed = new EmbedBuilder()
            .setTitle('✂️ حجر ورقة مقص')
            .setDescription('اختر خيارك:')
            .setColor('#4ecdc4');

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('rps_rock')
                    .setLabel('🗿 حجر')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('rps_paper')
                    .setLabel('📄 ورقة')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('rps_scissors')
                    .setLabel('✂️ مقص')
                    .setStyle(ButtonStyle.Secondary)
            );

        const msg = await message.reply({ embeds: [embed], components: [row] });

        const collector = msg.createMessageComponentCollector({
            filter: i => i.user.id === message.author.id,
            time: 30000
        });

        collector.on('collect', async (interaction) => {
            const userChoice = interaction.customId.split('_')[1];
            const userIndex = userChoice === 'rock' ? 0 : userChoice === 'paper' ? 1 : 2;
            const botIndex = Math.floor(Math.random() * 3);

            let result;
            if (userIndex === botIndex) {
                result = 'تعادل! 🤝';
            } else if (
                (userIndex === 0 && botIndex === 2) ||
                (userIndex === 1 && botIndex === 0) ||
                (userIndex === 2 && botIndex === 1)
            ) {
                result = 'فزت! 🎉';
                // إضافة نقاط للفائز
                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { 
                        $inc: { 
                            'economy.balance': 50,
                            'economy.xp': 10,
                            'stats.gamesWon': 1
                        }
                    },
                    { upsert: true }
                );
            } else {
                result = 'خسرت! 😢';
                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { $inc: { 'stats.gamesLost': 1 } },
                    { upsert: true }
                );
            }

            const resultEmbed = new EmbedBuilder()
                .setTitle('✂️ نتيجة حجر ورقة مقص')
                .addFields([
                    {
                        name: 'اختيارك',
                        value: `${choices[userIndex]} ${choiceNames[userIndex]}`,
                        inline: true
                    },
                    {
                        name: 'اختيار البوت',
                        value: `${choices[botIndex]} ${choiceNames[botIndex]}`,
                        inline: true
                    },
                    {
                        name: 'النتيجة',
                        value: result,
                        inline: false
                    }
                ])
                .setColor(result.includes('فزت') ? '#00ff00' : result.includes('خسرت') ? '#ff0000' : '#ffff00');

            await interaction.update({ embeds: [resultEmbed], components: [] });
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ انتهى الوقت')
                    .setDescription('لم تختر في الوقت المحدد')
                    .setColor('#ff9900');
                await msg.edit({ embeds: [timeoutEmbed], components: [] });
            }
        });
    },

    async trivia(message) {
        const questions = [
            {
                question: 'ما هي عاصمة فرنسا؟',
                options: ['لندن', 'باريس', 'روما', 'برلين'],
                correct: 1,
                category: 'جغرافيا'
            },
            {
                question: 'كم عدد أيام السنة الكبيسة؟',
                options: ['365', '366', '364', '367'],
                correct: 1,
                category: 'عامة'
            },
            {
                question: 'من هو مؤسس شركة مايكروسوفت؟',
                options: ['ستيف جوبز', 'بيل غيتس', 'مارك زوكربيرغ', 'إيلون ماسك'],
                correct: 1,
                category: 'تكنولوجيا'
            },
            {
                question: 'ما هو أكبر كوكب في النظام الشمسي؟',
                options: ['الأرض', 'المريخ', 'المشتري', 'زحل'],
                correct: 2,
                category: 'علوم'
            },
            {
                question: 'في أي عام تم اختراع الإنترنت؟',
                options: ['1969', '1975', '1983', '1991'],
                correct: 0,
                category: 'تكنولوجيا'
            }
        ];

        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        const emojis = ['🇦', '🇧', '🇨', '🇩'];

        const embed = new EmbedBuilder()
            .setTitle('🧠 سؤال ثقافي')
            .setDescription(`**الفئة:** ${randomQuestion.category}\n\n**${randomQuestion.question}**`)
            .setColor('#9b59b6');

        const row = new ActionRowBuilder();
        randomQuestion.options.forEach((option, index) => {
            embed.addFields([
                {
                    name: `${emojis[index]} الخيار ${index + 1}`,
                    value: option,
                    inline: true
                }
            ]);

            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`trivia_${index}`)
                    .setLabel(`${emojis[index]} ${option}`)
                    .setStyle(ButtonStyle.Secondary)
            );
        });

        const msg = await message.reply({ embeds: [embed], components: [row] });

        const collector = msg.createMessageComponentCollector({
            filter: i => i.user.id === message.author.id,
            time: 30000
        });

        collector.on('collect', async (interaction) => {
            const answerIndex = parseInt(interaction.customId.split('_')[1]);
            const isCorrect = answerIndex === randomQuestion.correct;

            let resultEmbed;
            if (isCorrect) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('🎉 إجابة صحيحة!')
                    .setDescription(`أحسنت! الإجابة الصحيحة هي: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#00ff00');

                // إضافة مكافآت
                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { 
                        $inc: { 
                            'economy.balance': 100,
                            'economy.xp': 25,
                            'stats.triviaCorrect': 1
                        }
                    },
                    { upsert: true }
                );
            } else {
                resultEmbed = new EmbedBuilder()
                    .setTitle('❌ إجابة خاطئة')
                    .setDescription(`للأسف، الإجابة الصحيحة هي: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { $inc: { 'stats.triviaWrong': 1 } },
                    { upsert: true }
                );
            }

            await interaction.update({ embeds: [resultEmbed], components: [] });
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ انتهى الوقت')
                    .setDescription(`الإجابة الصحيحة كانت: **${randomQuestion.options[randomQuestion.correct]}**`)
                    .setColor('#ff9900');
                await msg.edit({ embeds: [timeoutEmbed], components: [] });
            }
        });
    },

    async guessNumber(message) {
        const secretNumber = Math.floor(Math.random() * 100) + 1;
        let attempts = 0;
        const maxAttempts = 7;

        const embed = new EmbedBuilder()
            .setTitle('🔢 لعبة تخمين الرقم')
            .setDescription(`خمن الرقم بين 1 و 100!\nلديك **${maxAttempts}** محاولات`)
            .setColor('#3498db');

        await message.reply({ embeds: [embed] });

        const filter = m => m.author.id === message.author.id && !isNaN(m.content);
        const collector = message.channel.createMessageCollector({ filter, time: 120000 });

        collector.on('collect', async (msg) => {
            attempts++;
            const guess = parseInt(msg.content);

            if (guess < 1 || guess > 100) {
                return msg.reply('❌ يرجى إدخال رقم بين 1 و 100');
            }

            let resultEmbed;
            if (guess === secretNumber) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('🎉 تهانينا!')
                    .setDescription(`أحسنت! الرقم السري كان **${secretNumber}**\nحصلت عليه في **${attempts}** محاولة`)
                    .setColor('#00ff00');

                const reward = Math.max(200 - (attempts * 20), 50);
                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { 
                        $inc: { 
                            'economy.balance': reward,
                            'economy.xp': 30,
                            'stats.gamesWon': 1
                        }
                    },
                    { upsert: true }
                );

                resultEmbed.addFields([
                    {
                        name: '💰 المكافأة',
                        value: `${reward} عملة`,
                        inline: true
                    }
                ]);

                collector.stop();
            } else if (attempts >= maxAttempts) {
                resultEmbed = new EmbedBuilder()
                    .setTitle('😢 انتهت المحاولات')
                    .setDescription(`للأسف، الرقم السري كان **${secretNumber}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { $inc: { 'stats.gamesLost': 1 } },
                    { upsert: true }
                );

                collector.stop();
            } else {
                const hint = guess < secretNumber ? 'أكبر ⬆️' : 'أصغر ⬇️';
                const remaining = maxAttempts - attempts;
                
                resultEmbed = new EmbedBuilder()
                    .setTitle('🔍 محاولة أخرى')
                    .setDescription(`الرقم السري ${hint}\nتبقى لديك **${remaining}** محاولة`)
                    .setColor('#ffff00');
            }

            await msg.reply({ embeds: [resultEmbed] });
        });

        collector.on('end', async (collected, reason) => {
            if (reason === 'time') {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ انتهى الوقت')
                    .setDescription(`الرقم السري كان **${secretNumber}**`)
                    .setColor('#ff9900');
                await message.channel.send({ embeds: [timeoutEmbed] });
            }
        });
    },

    async slotMachine(message, args) {
        const bet = parseInt(args[1]) || 50;
        
        const userData = await User.findOne({ userId: message.author.id });
        if (!userData || userData.economy.balance < bet) {
            const embed = new EmbedBuilder()
                .setTitle('❌ رصيد غير كافي')
                .setDescription('ليس لديك رصيد كافي للعب')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const symbols = ['🍎', '🍊', '🍋', '🍇', '🍓', '💎', '⭐', '🔔'];
        const reels = [
            symbols[Math.floor(Math.random() * symbols.length)],
            symbols[Math.floor(Math.random() * symbols.length)],
            symbols[Math.floor(Math.random() * symbols.length)]
        ];

        let multiplier = 0;
        if (reels[0] === reels[1] && reels[1] === reels[2]) {
            // ثلاثة متطابقة
            if (reels[0] === '💎') multiplier = 10;
            else if (reels[0] === '⭐') multiplier = 8;
            else if (reels[0] === '🔔') multiplier = 6;
            else multiplier = 4;
        } else if (reels[0] === reels[1] || reels[1] === reels[2] || reels[0] === reels[2]) {
            // اثنان متطابقان
            multiplier = 2;
        }

        const winnings = bet * multiplier;
        const profit = winnings - bet;

        // تحديث الرصيد
        userData.economy.balance += profit;
        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle('🎰 ماكينة الحظ')
            .setDescription(`**${reels.join(' | ')}**`)
            .addFields([
                {
                    name: '💰 الرهان',
                    value: `${bet} عملة`,
                    inline: true
                },
                {
                    name: '🎯 المضاعف',
                    value: `${multiplier}x`,
                    inline: true
                },
                {
                    name: profit >= 0 ? '💵 الأرباح' : '💸 الخسارة',
                    value: `${Math.abs(profit)} عملة`,
                    inline: true
                },
                {
                    name: '💰 رصيدك الجديد',
                    value: `${userData.economy.balance} عملة`,
                    inline: false
                }
            ])
            .setColor(profit > 0 ? '#00ff00' : profit < 0 ? '#ff0000' : '#ffff00');

        return message.reply({ embeds: [embed] });
    },

    async memoryGame(message) {
        const sequence = [];
        const emojis = ['🔴', '🟡', '🟢', '🔵'];
        let round = 1;
        const maxRounds = 10;

        const embed = new EmbedBuilder()
            .setTitle('🧩 لعبة الذاكرة')
            .setDescription('احفظ التسلسل وأعد كتابته!\nسأبدأ بعرض التسلسل...')
            .setColor('#e74c3c');

        await message.reply({ embeds: [embed] });

        const playRound = async () => {
            // إضافة عنصر جديد للتسلسل
            sequence.push(emojis[Math.floor(Math.random() * emojis.length)]);

            // عرض التسلسل
            const showEmbed = new EmbedBuilder()
                .setTitle(`🧩 الجولة ${round}`)
                .setDescription(`احفظ هذا التسلسل:\n\n**${sequence.join(' ')}**`)
                .setColor('#3498db');

            const showMsg = await message.channel.send({ embeds: [showEmbed] });

            // إخفاء التسلسل بعد 3 ثوان
            setTimeout(async () => {
                const hideEmbed = new EmbedBuilder()
                    .setTitle(`🧩 الجولة ${round}`)
                    .setDescription('الآن أعد كتابة التسلسل!')
                    .setColor('#f39c12');

                await showMsg.edit({ embeds: [hideEmbed] });

                // انتظار إجابة المستخدم
                const filter = m => m.author.id === message.author.id;
                const collector = message.channel.createMessageCollector({ filter, time: 30000, max: 1 });

                collector.on('collect', async (msg) => {
                    const userSequence = msg.content.split(' ');
                    
                    if (userSequence.length === sequence.length && 
                        userSequence.every((emoji, index) => emoji === sequence[index])) {
                        
                        // إجابة صحيحة
                        round++;
                        if (round <= maxRounds) {
                            const successEmbed = new EmbedBuilder()
                                .setTitle('✅ صحيح!')
                                .setDescription(`أحسنت! ننتقل للجولة ${round}`)
                                .setColor('#00ff00');
                            
                            await msg.reply({ embeds: [successEmbed] });
                            setTimeout(() => playRound(), 2000);
                        } else {
                            // فوز كامل
                            const winEmbed = new EmbedBuilder()
                                .setTitle('🏆 تهانينا!')
                                .setDescription('أكملت جميع الجولات بنجاح!')
                                .setColor('#ffd700');

                            await User.findOneAndUpdate(
                                { userId: message.author.id },
                                { 
                                    $inc: { 
                                        'economy.balance': 500,
                                        'economy.xp': 100,
                                        'stats.gamesWon': 1
                                    }
                                },
                                { upsert: true }
                            );

                            await msg.reply({ embeds: [winEmbed] });
                        }
                    } else {
                        // إجابة خاطئة
                        const failEmbed = new EmbedBuilder()
                            .setTitle('❌ خطأ!')
                            .setDescription(`التسلسل الصحيح كان: **${sequence.join(' ')}**\nوصلت للجولة ${round}`)
                            .setColor('#ff0000');

                        const reward = (round - 1) * 25;
                        if (reward > 0) {
                            await User.findOneAndUpdate(
                                { userId: message.author.id },
                                { 
                                    $inc: { 
                                        'economy.balance': reward,
                                        'economy.xp': reward / 5
                                    }
                                },
                                { upsert: true }
                            );

                            failEmbed.addFields([
                                {
                                    name: '💰 المكافأة',
                                    value: `${reward} عملة`,
                                    inline: true
                                }
                            ]);
                        }

                        await msg.reply({ embeds: [failEmbed] });
                    }
                });

                collector.on('end', async (collected) => {
                    if (collected.size === 0) {
                        const timeoutEmbed = new EmbedBuilder()
                            .setTitle('⏰ انتهى الوقت')
                            .setDescription(`التسلسل الصحيح كان: **${sequence.join(' ')}**`)
                            .setColor('#ff9900');
                        await message.channel.send({ embeds: [timeoutEmbed] });
                    }
                });
            }, 3000);
        };

        setTimeout(() => playRound(), 2000);
    },

    async mathChallenge(message) {
        const operations = ['+', '-', '*'];
        const operation = operations[Math.floor(Math.random() * operations.length)];
        
        let num1, num2, answer;
        
        switch (operation) {
            case '+':
                num1 = Math.floor(Math.random() * 100) + 1;
                num2 = Math.floor(Math.random() * 100) + 1;
                answer = num1 + num2;
                break;
            case '-':
                num1 = Math.floor(Math.random() * 100) + 50;
                num2 = Math.floor(Math.random() * 50) + 1;
                answer = num1 - num2;
                break;
            case '*':
                num1 = Math.floor(Math.random() * 12) + 1;
                num2 = Math.floor(Math.random() * 12) + 1;
                answer = num1 * num2;
                break;
        }

        const embed = new EmbedBuilder()
            .setTitle('🔢 تحدي الرياضيات')
            .setDescription(`**${num1} ${operation} ${num2} = ?**\n\nلديك 30 ثانية للإجابة!`)
            .setColor('#9b59b6');

        await message.reply({ embeds: [embed] });

        const filter = m => m.author.id === message.author.id && !isNaN(m.content);
        const collector = message.channel.createMessageCollector({ filter, time: 30000, max: 1 });

        collector.on('collect', async (msg) => {
            const userAnswer = parseInt(msg.content);
            
            if (userAnswer === answer) {
                const successEmbed = new EmbedBuilder()
                    .setTitle('🎉 إجابة صحيحة!')
                    .setDescription(`أحسنت! **${num1} ${operation} ${num2} = ${answer}**`)
                    .setColor('#00ff00');

                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { 
                        $inc: { 
                            'economy.balance': 75,
                            'economy.xp': 15,
                            'stats.mathCorrect': 1
                        }
                    },
                    { upsert: true }
                );

                await msg.reply({ embeds: [successEmbed] });
            } else {
                const failEmbed = new EmbedBuilder()
                    .setTitle('❌ إجابة خاطئة')
                    .setDescription(`للأسف، الإجابة الصحيحة هي: **${answer}**`)
                    .setColor('#ff0000');

                await User.findOneAndUpdate(
                    { userId: message.author.id },
                    { $inc: { 'stats.mathWrong': 1 } },
                    { upsert: true }
                );

                await msg.reply({ embeds: [failEmbed] });
            }
        });

        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                    .setTitle('⏰ انتهى الوقت')
                    .setDescription(`الإجابة الصحيحة كانت: **${answer}**`)
                    .setColor('#ff9900');
                await message.channel.send({ embeds: [timeoutEmbed] });
            }
        });
    }
};
