/**
 * CS Bot - مسارات لوحة التحكم
 *
 * هذا الملف يحتوي على مسارات لوحة التحكم الخاصة بالسيرفرات
 */

const express = require('express');
const router = express.Router();

// Middleware للتحقق من تسجيل الدخول
function requireAuth(req, res, next) {
    try {
        if (req.session && req.session.user) {
            req.user = req.session.user;
            return next();
        }

        console.log('المستخدم غير مسجل الدخول، إعادة توجيه لصفحة تسجيل الدخول');
        return res.redirect('/auth/login');
    } catch (error) {
        console.error('خطأ في requireAuth middleware:', error);
        return res.status(500).send(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>خطأ في التحقق من تسجيل الدخول - CS Bot</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-0 shadow">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-4"></i>
                                    <h2 class="text-danger mb-3">خطأ في التحقق من تسجيل الدخول</h2>
                                    <p class="text-muted mb-4">حدث خطأ أثناء التحقق من تسجيل الدخول. يرجى المحاولة مرة أخرى.</p>
                                    <a href="/auth/login" class="btn btn-primary">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `);
    }
}

// تطبيق middleware على جميع مسارات dashboard
router.use(requireAuth);
const { getGuildSettings, saveGuildSettings } = require('../../models/guild');
const { getUserData } = require('../../models/user');

// Middleware للتحقق من صلاحيات المطور فقط
function checkOwner(req, res, next) {
    try {
        const config = req.app.locals.config;
        const user = req.session?.user || req.user;

        if (!user) {
            return res.redirect('/auth/login');
        }

        // قائمة المطورين المسموح لهم
        const allowedOwners = [
            config.owner?.id,
            "1289581696995561495", // معرفك الأساسي
            // يمكن إضافة معرفات أخرى هنا
        ].filter(Boolean); // إزالة القيم الفارغة

        if (allowedOwners.includes(user.id)) {
            req.user = user;
            return next();
        }

        return res.status(403).send(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>ممنوع الدخول - CS Bot</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-0 shadow">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-ban fa-3x text-danger mb-4"></i>
                                    <h2 class="text-danger mb-3">ممنوع الدخول</h2>
                                    <p class="text-muted mb-4">هذه الصفحة مخصصة للمطورين فقط.</p>
                                    <div class="alert alert-info">
                                        <strong>معرفك:</strong> ${user.id}<br>
                                        <strong>المطورين المسموح لهم:</strong> ${allowedOwners.join(', ')}
                                    </div>
                                    <a href="/" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `);
    } catch (error) {
        console.error('خطأ في checkOwner middleware:', error);
        return res.status(500).send(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>خطأ في النظام - CS Bot</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-0 shadow">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-4"></i>
                                    <h2 class="text-danger mb-3">خطأ في النظام</h2>
                                    <p class="text-muted mb-4">حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.</p>
                                    <a href="/" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `);
    }
}

// دالة مساعدة للتحقق من صلاحيات السيرفر
const checkGuildPermission = (req, res, guildId) => {
    const guild = req.user.guilds.find(g => g.id === guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return null;
    }

    return guild;
};

// دالة مساعدة لعرض صفحة السيرفر
const renderGuildPage = async (req, res, guildId, template, additionalData = {}) => {
    const guild = checkGuildPermission(req, res, guildId);

    if (!guild) {
        return res.redirect('/dashboard');
    }

    try {
        const settings = await getGuildSettings(guildId);

        res.render(`dashboard/${template}`, {
            user: req.user,
            guild,
            settings,
            config: req.app.locals.config,
            bot: req.app.locals.bot,
            ...additionalData
        });
    } catch (err) {
        console.error(err);
        res.redirect('/dashboard');
    }
};

// الصفحة الرئيسية للوحة التحكم (للمطور فقط)
router.get('/', checkOwner, (req, res) => {
    try {
        console.log('عرض لوحة التحكم الرئيسية للمطور:', req.user?.username);

        res.render('dashboard/index', {
            user: req.user,
            config: req.app.locals.config,
            bot: req.app.locals.bot
        });
    } catch (error) {
        console.error('خطأ في عرض لوحة التحكم:', error);
        res.status(500).send(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>خطأ في لوحة التحكم - CS Bot</title>
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            </head>
            <body>
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card border-0 shadow">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-4"></i>
                                    <h2 class="text-danger mb-3">خطأ في عرض لوحة التحكم</h2>
                                    <p class="text-muted mb-4">حدث خطأ أثناء تحميل لوحة التحكم. يرجى المحاولة مرة أخرى.</p>
                                    <a href="/" class="btn btn-primary">
                                        <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
        `);
    }
});

// صفحة إدارة المستخدمين (للمطور فقط)
router.get('/users', checkOwner, (req, res) => {
    res.render('dashboard/users', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة إدارة السيرفرات (للمطور فقط)
router.get('/servers', checkOwner, (req, res) => {
    res.render('dashboard/servers', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة التحليلات المتقدمة (للمطور فقط)
router.get('/analytics', checkOwner, (req, res) => {
    res.render('dashboard/analytics', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة إدارة البريميوم (للمطور فقط)
router.get('/premium', checkOwner, (req, res) => {
    res.render('dashboard/premium', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الإعدادات المتقدمة (للمطور فقط)
router.get('/settings', checkOwner, (req, res) => {
    res.render('dashboard/settings', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة تحكم المطور في سيرفراته الشخصية (للمطور فقط)
router.get('/my-servers', checkOwner, (req, res) => {
    res.render('dashboard/my-servers', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الملف الشخصي
router.get('/profile', async (req, res) => {
    try {
        const userData = await getUserData(req.user.id, req.user.username, req.user.discriminator, req.user.avatar);

        res.render('dashboard/profile', {
            user: req.user,
            userData,
            config: req.app.locals.config,
            bot: req.app.locals.bot
        });
    } catch (err) {
        console.error(err);
        res.redirect('/dashboard');
    }
});

// صفحة إعدادات السيرفر (الرئيسية)
router.get('/server/:guildId', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'server');
});

// صفحة إعدادات الترحيب
router.get('/server/:guildId/welcome', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'welcome');
});

// صفحة إعدادات المغادرة
router.get('/server/:guildId/leave', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'leave');
});

// صفحة إعدادات اللوجات
router.get('/server/:guildId/logs', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'logs');
});

// صفحة إعدادات التذاكر
router.get('/server/:guildId/tickets', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'tickets');
});

// صفحة إعدادات الرتب التلقائية
router.get('/server/:guildId/autorole', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'autorole');
});

// صفحة إعدادات الردود التلقائية
router.get('/server/:guildId/autoresponse', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'autoresponse');
});

// صفحة إعدادات الاقتصاد
router.get('/server/:guildId/economy', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'economy');
});

// صفحة إعدادات الإدارة
router.get('/server/:guildId/moderation', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'moderation');
});

// صفحة الأوامر المخصصة
router.get('/server/:guildId/commands', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'commands');
});

// صفحة الإعدادات العامة
router.get('/server/:guildId/settings', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'settings');
});

// صفحة مانع السبام
router.get('/server/:guildId/antispam', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'antispam');
});

// صفحة البريميوم
router.get('/server/:guildId/premium', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'premium');
});

// صفحة لوحة تحكم الأدمن (لصاحب البوت فقط)
router.get('/admin', async (req, res) => {
    // التحقق من أن المستخدم هو صاحب البوت
    if (req.app.locals.config.owner.id !== req.user.id) {
        return res.redirect('/dashboard');
    }

    res.render('dashboard/admin', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// حفظ إعدادات السيرفر
router.post('/server/:guildId/save', async (req, res) => {
    const guild = checkGuildPermission(req, res, req.params.guildId);

    if (!guild) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }

    try {
        await saveGuildSettings(req.params.guildId, req.body);
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

// API endpoints للمستخدمين والسيرفرات
router.get('/api/users', checkOwner, async (req, res) => {
    try {
        // بيانات وهمية للاختبار
        const dummyUsers = [
            {
                id: '123456789',
                username: 'مستخدم تجريبي 1',
                avatar: null,
                joinedAt: new Date(),
                lastActive: new Date(),
                commandsUsed: 25,
                premium: { active: true, expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) },
                banned: false,
                mutualGuilds: 2
            },
            {
                id: '987654321',
                username: 'مستخدم تجريبي 2',
                avatar: null,
                joinedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                commandsUsed: 12,
                premium: { active: false },
                banned: false,
                mutualGuilds: 1
            }
        ];

        res.json({
            users: dummyUsers,
            stats: {
                total: 11,
                active: 8,
                premium: 3,
                banned: 0
            },
            totalPages: 1
        });
    } catch (error) {
        console.error('خطأ في API المستخدمين:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل المستخدمين' });
    }
});

router.get('/api/servers', checkOwner, async (req, res) => {
    try {
        // بيانات وهمية للاختبار
        const dummyServers = [
            {
                id: '123456789',
                name: 'سيرفر الاختبار الأول',
                icon: null,
                memberCount: 150,
                channelCount: 25,
                roleCount: 12,
                premium: true,
                verified: false,
                partnered: false,
                joinedAt: new Date(),
                lastActivity: new Date()
            },
            {
                id: '987654321',
                name: 'مجتمع المطورين',
                icon: null,
                memberCount: 500,
                channelCount: 40,
                roleCount: 20,
                premium: false,
                verified: true,
                partnered: false,
                joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
        ];

        res.json({
            servers: dummyServers,
            stats: {
                total: 2,
                totalMembers: 650,
                active: 2,
                premium: 1
            },
            totalPages: 1
        });
    } catch (error) {
        console.error('خطأ في API السيرفرات:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل السيرفرات' });
    }
});

router.get('/api/analytics', checkOwner, async (req, res) => {
    try {
        // بيانات وهمية للتحليلات
        res.json({
            stats: {
                totalViews: '1,234',
                commandsExecuted: '5,678',
                newUsers: '89',
                uptime: '99.9%'
            },
            charts: {
                commands: [12, 19, 3, 5, 2, 3, 9],
                users: [65, 25, 10],
                servers: [4, 7, 2, 5]
            },
            tables: {
                topCommands: [
                    { name: 'help', count: 1234, percentage: 25 },
                    { name: 'ping', count: 987, percentage: 20 },
                    { name: 'music', count: 654, percentage: 13 },
                    { name: 'fun', count: 432, percentage: 9 },
                    { name: 'mod', count: 321, percentage: 7 }
                ],
                topServers: [
                    { name: 'سيرفر الاختبار الأول', members: 150, activity: 'عالي' },
                    { name: 'مجتمع المطورين', members: 500, activity: 'متوسط' },
                    { name: 'سيرفر الألعاب', members: 75, activity: 'منخفض' }
                ]
            }
        });
    } catch (error) {
        console.error('خطأ في API التحليلات:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل التحليلات' });
    }
});

// إضافة مالك جديد
router.post('/api/owners/add', checkOwner, async (req, res) => {
    try {
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'معرف المستخدم مطلوب' });
        }

        // هنا يمكن إضافة المنطق لإضافة المالك إلى قاعدة البيانات
        console.log(`إضافة مالك جديد: ${userId} بواسطة ${req.user.username}`);

        res.json({
            success: true,
            message: 'تم إضافة المالك بنجاح',
            newOwner: userId
        });
    } catch (error) {
        console.error('خطأ في إضافة المالك:', error);
        res.status(500).json({ error: 'حدث خطأ في إضافة المالك' });
    }
});

// صفحات شروط الاستخدام وسياسة الخصوصية
router.get('/terms', (req, res) => {
    try {
        res.render('terms', {
            config: req.app.locals.config,
            bot: req.app.locals.bot
        });
    } catch (error) {
        console.error('خطأ في عرض شروط الاستخدام:', error);
        res.status(500).send('خطأ في تحميل الصفحة');
    }
});

router.get('/privacy', (req, res) => {
    try {
        res.render('privacy', {
            config: req.app.locals.config,
            bot: req.app.locals.bot
        });
    } catch (error) {
        console.error('خطأ في عرض سياسة الخصوصية:', error);
        res.status(500).send('خطأ في تحميل الصفحة');
    }
});

module.exports = router;
