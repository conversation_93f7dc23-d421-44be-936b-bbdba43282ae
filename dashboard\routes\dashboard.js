/**
 * CS Bot - مسارات لوحة التحكم
 *
 * هذا الملف يحتوي على مسارات لوحة التحكم الخاصة بالسيرفرات
 */

const express = require('express');
const router = express.Router();
const { getGuildSettings, saveGuildSettings } = require('../../models/guild');
const { getUserData } = require('../../models/user');

// دالة مساعدة للتحقق من صلاحيات السيرفر
const checkGuildPermission = (req, res, guildId) => {
    const guild = req.user.guilds.find(g => g.id === guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return null;
    }

    return guild;
};

// دالة مساعدة لعرض صفحة السيرفر
const renderGuildPage = async (req, res, guildId, template, additionalData = {}) => {
    const guild = checkGuildPermission(req, res, guildId);

    if (!guild) {
        return res.redirect('/dashboard');
    }

    try {
        const settings = await getGuildSettings(guildId);

        res.render(`dashboard/${template}`, {
            user: req.user,
            guild,
            settings,
            config: req.app.locals.config,
            bot: req.app.locals.bot,
            ...additionalData
        });
    } catch (err) {
        console.error(err);
        res.redirect('/dashboard');
    }
};

// الصفحة الرئيسية للوحة التحكم
router.get('/', (req, res) => {
    const guilds = req.user.guilds.filter(g => (g.permissions & 0x20) === 0x20);

    res.render('dashboard/index', {
        user: req.user,
        guilds,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// صفحة الملف الشخصي
router.get('/profile', async (req, res) => {
    try {
        const userData = await getUserData(req.user.id, req.user.username, req.user.discriminator, req.user.avatar);

        res.render('dashboard/profile', {
            user: req.user,
            userData,
            config: req.app.locals.config,
            bot: req.app.locals.bot
        });
    } catch (err) {
        console.error(err);
        res.redirect('/dashboard');
    }
});

// صفحة إعدادات السيرفر (الرئيسية)
router.get('/server/:guildId', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'server');
});

// صفحة إعدادات الترحيب
router.get('/server/:guildId/welcome', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'welcome');
});

// صفحة إعدادات المغادرة
router.get('/server/:guildId/leave', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'leave');
});

// صفحة إعدادات اللوجات
router.get('/server/:guildId/logs', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'logs');
});

// صفحة إعدادات التذاكر
router.get('/server/:guildId/tickets', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'tickets');
});

// صفحة إعدادات الرتب التلقائية
router.get('/server/:guildId/autorole', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'autorole');
});

// صفحة إعدادات الردود التلقائية
router.get('/server/:guildId/autoresponse', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'autoresponse');
});

// صفحة إعدادات الاقتصاد
router.get('/server/:guildId/economy', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'economy');
});

// صفحة إعدادات الإدارة
router.get('/server/:guildId/moderation', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'moderation');
});

// صفحة الأوامر المخصصة
router.get('/server/:guildId/commands', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'commands');
});

// صفحة الإعدادات العامة
router.get('/server/:guildId/settings', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'settings');
});

// صفحة مانع السبام
router.get('/server/:guildId/antispam', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'antispam');
});

// صفحة البريميوم
router.get('/server/:guildId/premium', async (req, res) => {
    await renderGuildPage(req, res, req.params.guildId, 'premium');
});

// صفحة لوحة تحكم الأدمن (لصاحب البوت فقط)
router.get('/admin', async (req, res) => {
    // التحقق من أن المستخدم هو صاحب البوت
    if (req.app.locals.config.owner.id !== req.user.id) {
        return res.redirect('/dashboard');
    }

    res.render('dashboard/admin', {
        user: req.user,
        config: req.app.locals.config,
        bot: req.app.locals.bot
    });
});

// حفظ إعدادات السيرفر
router.post('/server/:guildId/save', async (req, res) => {
    const guild = checkGuildPermission(req, res, req.params.guildId);

    if (!guild) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }

    try {
        await saveGuildSettings(req.params.guildId, req.body);
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

module.exports = router;
