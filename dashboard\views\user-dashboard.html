<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/user-dashboard">
            <i class="fas fa-robot"></i> CS Bot - لوحة التحكم
        </a>
        <div class="d-flex">
            <span class="navbar-text me-3">
                مرحباً، {{user.username}}
            </span>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-user"></i> مرحباً بك {{user.username}}
                    </h2>
                    <p class="card-text">إدارة سيرفراتك مع CS Bot</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="userServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفراتك</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="premiumStatus">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">حالة البريميوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-terminal fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="commandsUsed">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">الأوامر المستخدمة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="joinDate">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">تاريخ الانضمام</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Servers -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> جميع سيرفرات البوت
                        <span class="badge bg-light text-dark ms-2" id="serverCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="serversContainer">
                        <div class="col-12 text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">جاري تحميل سيرفراتك...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-crown"></i> البريميوم
                    </h5>
                </div>
                <div class="card-body">
                    <div id="premiumSection">
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Server Management Modal -->
<div class="modal fade" id="serverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog"></i> إدارة السيرفر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="saveServerSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let userServers = [];

// تحميل بيانات المستخدم
async function loadUserData() {
    try {
        const response = await fetch('/api/user/data');
        const data = await response.json();

        if (response.ok) {
            // تحديث الإحصائيات
            document.getElementById('userServers').textContent = data.servers?.length || 0;
            document.getElementById('premiumStatus').textContent = data.premium?.active ? 'نشط' : 'غير نشط';
            document.getElementById('commandsUsed').textContent = data.commandsUsed || 0;
            document.getElementById('joinDate').textContent = new Date(data.joinedAt || Date.now()).toLocaleDateString('ar-SA');

            // تحديث السيرفرات
            userServers = data.servers || [];
            displayUserServers(userServers);

            // تحديث قسم البريميوم
            displayPremiumSection(data.premium);

        } else {
            console.error('خطأ في تحميل بيانات المستخدم:', data.error);
        }

    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// عرض سيرفرات المستخدم
function displayUserServers(servers) {
    const container = document.getElementById('serversContainer');
    document.getElementById('serverCount').textContent = servers.length;

    if (servers.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                <h5>لا توجد سيرفرات</h5>
                <p class="text-muted">البوت غير موجود في أي سيرفر</p>
            </div>
        `;
        return;
    }

    container.innerHTML = servers.map(server => `
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm h-100 ${!server.isInServer ? 'border-warning' : ''}">
                <div class="card-body text-center">
                    <img src="${server.icon || 'https://cdn.discordapp.com/embed/avatars/0.png'}"
                         alt="${server.name}"
                         class="rounded-circle mb-3"
                         width="64" height="64">
                    <h6 class="card-title">${server.name}</h6>
                    <p class="card-text text-muted small">
                        <i class="fas fa-users"></i> ${server.memberCount || 0} عضو
                    </p>

                    <!-- حالة المستخدم في السيرفر -->
                    <div class="mb-2">
                        ${server.isInServer ?
                            `<span class="badge bg-success"><i class="fas fa-check"></i> أنت في هذا السيرفر</span>` :
                            `<span class="badge bg-warning"><i class="fas fa-times"></i> لست في هذا السيرفر</span>`
                        }
                        ${server.owner ? `<span class="badge bg-primary ms-1"><i class="fas fa-crown"></i> مالك</span>` : ''}
                        ${server.permissions?.administrator ? `<span class="badge bg-danger ms-1"><i class="fas fa-shield-alt"></i> أدمن</span>` : ''}
                        ${server.permissions?.manageGuild ? `<span class="badge bg-info ms-1"><i class="fas fa-cog"></i> مدير</span>` : ''}
                    </div>

                    <!-- تفاصيل الصلاحيات -->
                    ${server.permissions ? `
                    <div class="mb-2">
                        <small class="text-muted">
                            <strong>صلاحياتك:</strong>
                            ${server.permissions.administrator ? '• أدمن كامل ' : ''}
                            ${server.permissions.manageGuild ? '• إدارة السيرفر ' : ''}
                            ${server.permissions.manageChannels ? '• إدارة القنوات ' : ''}
                            ${server.permissions.manageRoles ? '• إدارة الرتب ' : ''}
                            ${server.permissions.kickMembers ? '• طرد الأعضاء ' : ''}
                            ${server.permissions.banMembers ? '• حظر الأعضاء ' : ''}
                        </small>
                    </div>
                    ` : ''}

                    <!-- أزرار الإجراءات -->
                    <div class="btn-group w-100">
                        ${server.isInServer && server.canManage ?
                            `<button class="btn btn-primary btn-sm" onclick="manageServer('${server.id}')">
                                <i class="fas fa-cog"></i> إدارة
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="viewServerStats('${server.id}')">
                                <i class="fas fa-chart-bar"></i> إحصائيات
                            </button>` :
                            server.isInServer ?
                                `<button class="btn btn-secondary btn-sm" disabled>
                                    <i class="fas fa-lock"></i> لا توجد صلاحيات
                                </button>` :
                                `<a href="${server.inviteUrl}" class="btn btn-success btn-sm w-100" target="_blank">
                                    <i class="fas fa-plus"></i> انضم للسيرفر
                                </a>`
                        }
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// عرض قسم البريميوم
function displayPremiumSection(premium) {
    const container = document.getElementById('premiumSection');

    if (premium && premium.active) {
        const expiresAt = new Date(premium.expiresAt);
        const daysLeft = Math.ceil((expiresAt - new Date()) / (1000 * 60 * 60 * 24));

        container.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6><i class="fas fa-crown text-warning"></i> لديك اشتراك بريميوم نشط!</h6>
                    <p class="text-muted mb-0">
                        ينتهي في: ${expiresAt.toLocaleDateString('ar-SA')}
                        (${daysLeft} يوم متبقي)
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6">نشط</span>
                </div>
            </div>
        `;
    } else {
        container.innerHTML = `
            <div class="text-center">
                <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                <h6>ليس لديك اشتراك بريميوم</h6>
                <p class="text-muted">احصل على مميزات إضافية مع البريميوم</p>
                <button class="btn btn-warning" onclick="requestPremium()">
                    <i class="fas fa-crown"></i> طلب البريميوم
                </button>
            </div>
        `;
    }
}

// إدارة السيرفر
function manageServer(serverId) {
    const server = userServers.find(s => s.id === serverId);
    if (!server) return;

    document.getElementById('serverModalBody').innerHTML = `
        <div class="text-center mb-4">
            <img src="${server.icon || 'https://cdn.discordapp.com/embed/avatars/0.png'}"
                 alt="${server.name}"
                 class="rounded-circle mb-2"
                 width="80" height="80">
            <h5>${server.name}</h5>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>الإعدادات الأساسية</h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="welcomeEnabled">
                    <label class="form-check-label" for="welcomeEnabled">
                        رسائل الترحيب
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="leaveEnabled">
                    <label class="form-check-label" for="leaveEnabled">
                        رسائل المغادرة
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="autoRoleEnabled">
                    <label class="form-check-label" for="autoRoleEnabled">
                        الرتب التلقائية
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <h6>إعدادات الإدارة</h6>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="antiSpamEnabled">
                    <label class="form-check-label" for="antiSpamEnabled">
                        مانع السبام
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="autoModEnabled">
                    <label class="form-check-label" for="autoModEnabled">
                        الإدارة التلقائية
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="logsEnabled">
                    <label class="form-check-label" for="logsEnabled">
                        تسجيل الأحداث
                    </label>
                </div>
            </div>
        </div>
    `;

    new bootstrap.Modal(document.getElementById('serverModal')).show();
}

// طلب البريميوم
function requestPremium() {
    alert('يرجى التواصل مع المطور للحصول على البريميوم');
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadUserData);

// تحديث البيانات كل دقيقة
setInterval(loadUserData, 60000);
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group .btn {
    font-size: 0.875rem;
}
</style>

</body>
</html>
