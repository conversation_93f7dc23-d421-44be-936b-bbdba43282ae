# 🤖 CS Bot - البوت الأسطوري المتكامل

<div align="center">

![CS Bot Logo](https://via.placeholder.com/200x200/667eea/ffffff?text=CS+BOT)

**بوت Discord متقدم ومتكامل مع لوحة تحكم أسطورية**

[![Discord.js](https://img.shields.io/badge/discord.js-v14-blue.svg)](https://discord.js.org/)
[![Node.js](https://img.shields.io/badge/node.js-v18+-green.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

[🚀 إضافة البوت](https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=8&scope=bot%20applications.commands) • [📖 الدليل](docs/) • [🐛 الإبلاغ عن خطأ](issues/) • [💬 الدعم](https://discord.gg/your-server)

</div>

## ✨ الميزات الرئيسية

### 🎮 نظام الألعاب المتقدم
- 🎯 حجر ورقة مقص تفاعلي
- ⭕ لعبة إكس أو مع الأصدقاء
- 🧠 أسئلة ثقافية متنوعة
- 🔢 تخمين الأرقام
- 🎰 ماكينة الحظ
- 🧩 لعبة الذاكرة
- 🔢 تحدي الرياضيات

### 💰 نظام الاقتصاد الشامل
- 💵 عملة افتراضية متطورة
- 🎁 مكافآت يومية مع سلاسل التتالي
- 💼 نظام عمل متنوع
- 🛒 متجر شامل مع عناصر متنوعة
- 💸 تحويل الأموال بين المستخدمين
- 🎲 نظام مقامرة مثير
- 🏆 ترتيب الأثرياء
- 📊 نظام مستويات ونقاط

### 🛡️ إدارة متقدمة
- 📺 إدارة القنوات الذكية
- 👥 إدارة المستخدمين الشاملة
- 🧹 تنظيف الرسائل المتقدم
- 💾 نسخ احتياطية تلقائية
- 📊 إحصائيات مفصلة
- 🔗 نظام Webhook للإشعارات
- 📢 نظام إعلانات متطور

### 🌐 لوحة التحكم الأسطورية
- 🎨 تصميم عصري وجذاب
- 🔐 نظام تسجيل دخول آمن
- 📊 إحصائيات حية ومتقدمة
- ⚙️ إعدادات شاملة
- 👑 نظام بريميوم متكامل
- 🔔 نظام إشعارات متطور
- 📱 متجاوب مع جميع الأجهزة

### 🌍 دعم متعدد اللغات
- 🇸🇦 العربية (افتراضي)
- 🇺🇸 الإنجليزية
- 🔄 تبديل اللغة السهل
- 📝 ترجمة تلقائية (قريباً)

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- Node.js v18 أو أحدث
- MongoDB (اختياري - يستخدم قاعدة بيانات مؤقتة افتراضياً)
- حساب Discord Developer

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/cs-bot.git
cd cs-bot
```

2. **تثبيت المكتبات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتعديل ملف .env وأدخل القيم الصحيحة
```

4. **تشغيل البوت**
```bash
npm start
# أو للتطوير
npm run dev
```

### إعداد البوت في Discord

1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. أنشئ تطبيق جديد
3. اذهب إلى قسم "Bot" وأنشئ بوت
4. انسخ التوكن وضعه في ملف `.env`
5. فعل جميع الـ Intents المطلوبة
6. اذهب إلى قسم "OAuth2" وأنشئ رابط دعوة

## ⚙️ الإعدادات

### ملف .env الأساسي
```env
# إعدادات البوت
DISCORD_TOKEN=your_bot_token_here
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here

# قاعدة البيانات
MONGODB_URI=your_mongodb_connection_string

# لوحة التحكم
PORT=3000
SESSION_SECRET=your_session_secret

# Webhook للإشعارات
WEBHOOK_URL=your_webhook_url_here
```

### الإعدادات المتقدمة
راجع ملف `.env.example` للحصول على قائمة كاملة بجميع الإعدادات المتاحة.

## 📝 الأوامر

### أوامر الاقتصاد
- `!economy balance` - عرض الرصيد
- `!economy daily` - المكافأة اليومية
- `!economy work` - العمل لكسب المال
- `!economy shop` - المتجر
- `!economy transfer @user amount` - تحويل المال
- `!economy gamble amount` - المقامرة
- `!economy leaderboard` - ترتيب الأثرياء

### أوامر الألعاب
- `!games rps` - حجر ورقة مقص
- `!games tictactoe @user` - إكس أو
- `!games trivia` - سؤال ثقافي
- `!games guess` - تخمين الرقم
- `!games slots` - ماكينة الحظ
- `!games memory` - لعبة الذاكرة
- `!games math` - تحدي الرياضيات

### أوامر الإدارة
- `!admin channels` - إدارة القنوات
- `!admin users` - إدارة المستخدمين
- `!admin stats` - الإحصائيات
- `!admin cleanup amount` - تنظيف الرسائل
- `!admin backup` - نسخة احتياطية
- `!admin announce message` - إعلان

### Slash Commands
جميع الأوامر متاحة أيضاً كـ Slash Commands:
- `/economy balance`
- `/games rps`
- `/admin stats`
- وغيرها...

## 🎨 لوحة التحكم

### الوصول للوحة التحكم
1. اذهب إلى `http://localhost:3000`
2. سجل دخولك باستخدام Discord
3. استمتع بالميزات المتقدمة!

### الميزات المتاحة
- 📊 إحصائيات شاملة ومتقدمة
- ⚙️ إعدادات السيرفرات
- 👥 إدارة المستخدمين
- 🔔 نظام الإشعارات
- 👑 إدارة البريميوم
- 📈 تحليلات مفصلة

## 🔧 التطوير

### هيكل المشروع
```
cs-bot/
├── commands/           # الأوامر العادية
├── slashCommands/      # Slash Commands
├── dashboard/          # لوحة التحكم
├── utils/             # الأدوات المساعدة
├── models/            # نماذج قاعدة البيانات
├── events/            # أحداث البوت
├── config.js          # الإعدادات
└── index.js           # الملف الرئيسي
```

### إضافة أوامر جديدة
1. أنشئ ملف في مجلد `commands/`
2. استخدم القالب الأساسي:
```javascript
module.exports = {
    name: 'command-name',
    description: 'وصف الأمر',
    async execute(message, args, client) {
        // كود الأمر هنا
    }
};
```

### إضافة Slash Commands
1. أنشئ ملف في مجلد `slashCommands/`
2. استخدم SlashCommandBuilder من discord.js

## 🛡️ الأمان

- 🔐 تشفير البيانات الحساسة
- 🚫 حماية من الـ Rate Limiting
- 🔍 تتبع الأنشطة المشبوهة
- 📝 سجلات مفصلة للأمان
- 🛡️ صلاحيات محدودة ومدروسة

## 📊 الإحصائيات والتحليلات

- 📈 تتبع استخدام الأوامر
- 👥 إحصائيات المستخدمين
- 🏠 إحصائيات السيرفرات
- ⚡ مراقبة الأداء
- 📊 تقارير مفصلة

## 🔔 نظام الإشعارات

### Webhook للمطورين
يرسل البوت إشعارات مهمة عبر Webhook:
- 🚀 بدء تشغيل البوت
- 🎉 انضمام لسيرفر جديد
- 😢 مغادرة سيرفر
- ❌ أخطاء مهمة
- 🔐 تسجيل دخول لوحة التحكم
- 📊 إحصائيات يومية

### إعداد الـ Webhook
1. أنشئ webhook في سيرفر Discord
2. انسخ الرابط وضعه في `.env`
3. فعل الـ webhook في الإعدادات

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

- 📖 [الوثائق الكاملة](docs/)
- 🐛 [الإبلاغ عن الأخطاء](issues/)
- 💬 [سيرفر الدعم](https://discord.gg/your-server)
- 📧 [البريد الإلكتروني](mailto:<EMAIL>)

## 🙏 شكر خاص

- Discord.js Team
- MongoDB Team
- جميع المساهمين في المشروع
- مجتمع Discord العربي

---

<div align="center">

**صنع بـ ❤️ للمجتمع العربي**

[⭐ ضع نجمة](../../stargazers) • [🍴 Fork](../../fork) • [📢 شارك](https://twitter.com/intent/tweet?text=Check%20out%20this%20awesome%20Discord%20bot!)

</div>
