# بوت CS – نظام شامل للسيرفرات الاحترافية

بوت CS هو بوت ديسكورد عام (Public) مصمم لخدمة جميع أنواع السيرفرات باحترافية، مع لوحة تحكم متقدمة وسهلة الاستخدام. مناسب لسيرفرات الألعاب، البرمجة، المجتمعات، المتاجر، وأكثر!

## 🎯 فكرة البوت

يوفر بوت CS مجموعة من الأنظمة المتكاملة لإدارة السيرفرات والتحكم فيها بشكل كامل، بدون الحاجة لمعرفة تقنية. كل شيء يتم من خلال لوحة تحكم أنيقة وبسيطة. يدمج البوت بين السهولة والاحتراف، مع أنظمة ذكية وتخصيص عميق لكل جزء.

## 🧩 الأنظمة المدعومة داخل البوت

| النظام | الوصف |
|--------|-------|
| ✅ الترحيب | رسائل ترحيب قابلة للتخصيص + صور ترحيبية تلقائية |
| ✅ المغادرة | رسائل وداع للمغادرين |
| ✅ التذاكر | نظام تذاكر احترافي لدعم الأعضاء |
| ✅ اللوجات | لوج لكل شيء: بان، كيك، رول، رسائل، ردود، بوتات… إلخ |
| ✅ الأدوار التلقائية | تعيين رتب تلقائيًا للأعضاء الجدد |
| ✅ الردود التلقائية | ردود ذكية على كلمات محددة يحددها الأدمين |
| ✅ نظام العملات (Economy) | عملة مخصصة، تحويل، شغل، سرقة، متجر، يومي، وأكثر |
| ✅ الرد على الرسائل + أوامر Slash | دعم للأوامر الرسائلية والـ Slash |
| ✅ نظام التحذيرات (Warnings) | تحذيرات تُسجل في قاعدة البيانات وتظهر في الداشبورد |
| ✅ مانع سبام | حماية تلقائية من التكرار أو السبام |
| ✅ صفحة إحصائيات كاملة | Dashboard stats – عدد الأعضاء، التذاكر، العملات، إلخ |

## 🛠️ مميزات لوحة التحكم

لوحة تحكم متقدمة مبنية بتقنيات حديثة، وتحتوي على:

- تسجيل دخول آمن بـ OAuth2 من ديسكورد
- تحكم كامل في كل إعدادات السيرفر (بضغطة زر)
- إمكانية تشغيل/إيقاف الأنظمة حسب رغبة صاحب السيرفر
- تعديل الرسائل بسهولة (ترحيب، مغادرة، ردود)
- التحكم في الرولات، اللوجات، التذاكر، العملات، الردود التلقائية
- نظام حماية من الضغط/السبام (rate limit لكل مستخدم كل 20 ثانية)
- واجهة موبايل متوافقة + تصميم احترافي + سرعة استجابة عالية

## 💎 نظام Premium

نظام البريميوم في بوت CS يسمح لك بإعطاء صلاحيات مميزة لأي مستخدم، بإدخال الأمر:

```
!add @User 1s 1m 1d 1y
```

🧠 المميزات:
- يتم حفظ مدة البريميوم
- يظهر في الداشبورد كـ Premium Member
- يقدر يضيف توكن بوت خاص بيه واستخدام بعض المميزات الحصرية

## 🌍 البوت يدعم كل أنواع السيرفرات

✔️ يدعم جميع اللغات (إعدادات اللغة قابلة للتخصيص مستقبلاً)
✔️ يعمل على كل استضافة (ZNXH أو جهازك)
✔️ لا يحتاج أكواد معقدة – كل شيء يتم من الداشبورد

## 🧪 معلومات تقنية (للمطورين)

- Node.js + Express
- MongoDB Database
- Discord.js v14
- OAuth2 login system
- RESTful API للوحة التحكم
- Rate Limiter مدمج لحماية اللوحة

## 🔐 الحماية والخصوصية

- يتم حماية كل صفحة بلوحة تحكم بمصادقة ديسكورد
- لا يتم حفظ أي توكن بدون تشفير
- النظام يحتوي على حماية ضد السبام والطلبات المتكررة

## التثبيت والإعداد

### التثبيت السريع
1. قم بنسخ ملف `.env.example` إلى ملف `.env` وتعبئة البيانات المطلوبة
2. قم بتثبيت الحزم المطلوبة: `npm install`
3. تحقق من المتطلبات: `npm run check`
4. قم بتشغيل البوت: `npm start`

### أوامر التشغيل المتاحة
- `npm start` - تشغيل البوت كاملاً (مُحسن)
- `npm run dev` - تشغيل في وضع التطوير مع إعادة التحميل التلقائي
- `npm run dashboard` - تشغيل لوحة التحكم فقط (للاختبار)
- `npm run check` - فحص المتطلبات والإعدادات
- `npm test` - اختبار تحميل المكونات
- `npm run original` - تشغيل الملف الأصلي

### للحصول على تعليمات مفصلة
راجع ملف [SETUP.md](SETUP.md) للحصول على دليل شامل للتثبيت والإعداد.

## المساهمة

نرحب بمساهماتكم في تطوير البوت! يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد للميزة: `git checkout -b feature/amazing-feature`
3. قم بعمل Commit للتغييرات: `git commit -m 'إضافة ميزة رائعة'`
4. قم بدفع الفرع: `git push origin feature/amazing-feature`
5. قم بفتح طلب Pull Request
