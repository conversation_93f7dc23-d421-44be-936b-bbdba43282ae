/**
 * CS Bot - أمر Server Info
 * 
 * يعرض معلومات شاملة عن السيرفر
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'serverinfo',
    aliases: ['server', 'guild', 'guildinfo'],
    description: 'عرض معلومات شاملة عن السيرفر',
    usage: '!serverinfo',
    category: 'معلومات',
    cooldown: 5,
    
    async execute(message, args) {
        try {
            const guild = message.guild;
            
            // حساب الإحصائيات
            const totalMembers = guild.memberCount;
            const onlineMembers = guild.members.cache.filter(member => 
                member.presence?.status !== 'offline' && !member.user.bot
            ).size;
            const botCount = guild.members.cache.filter(member => member.user.bot).size;
            const humanCount = totalMembers - botCount;
            
            // حساب القنوات
            const textChannels = guild.channels.cache.filter(channel => channel.type === 0).size;
            const voiceChannels = guild.channels.cache.filter(channel => channel.type === 2).size;
            const categories = guild.channels.cache.filter(channel => channel.type === 4).size;
            
            // مستوى التحقق
            const verificationLevels = {
                0: '🔓 بدون تحقق',
                1: '📧 بريد إلكتروني مؤكد',
                2: '⏰ مسجل منذ 5 دقائق',
                3: '👥 عضو منذ 10 دقائق',
                4: '📱 رقم هاتف مؤكد'
            };
            
            // مستوى المحتوى الصريح
            const explicitLevels = {
                0: '🔞 غير مفلتر',
                1: '⚠️ مفلتر للأعضاء بدون رتب',
                2: '🛡️ مفلتر للجميع'
            };
            
            // الميزات المتاحة
            const features = guild.features.map(feature => {
                const featureNames = {
                    'ANIMATED_ICON': '🎭 أيقونة متحركة',
                    'BANNER': '🖼️ بانر',
                    'COMMERCE': '🛒 متجر',
                    'COMMUNITY': '🌍 مجتمع',
                    'DISCOVERABLE': '🔍 قابل للاكتشاف',
                    'FEATURABLE': '⭐ مميز',
                    'INVITE_SPLASH': '🎨 شاشة دعوة مخصصة',
                    'MEMBER_VERIFICATION_GATE_ENABLED': '✅ بوابة التحقق',
                    'NEWS': '📰 قنوات الأخبار',
                    'PARTNERED': '🤝 شريك Discord',
                    'PREVIEW_ENABLED': '👁️ معاينة مفعلة',
                    'VANITY_URL': '🔗 رابط مخصص',
                    'VERIFIED': '✅ موثق',
                    'VIP_REGIONS': '🌟 مناطق VIP',
                    'WELCOME_SCREEN_ENABLED': '👋 شاشة ترحيب'
                };
                return featureNames[feature] || feature;
            });
            
            // إنشاء Embed مذهل
            const embed = new EmbedBuilder()
                .setTitle(`🏰 معلومات السيرفر: ${guild.name}`)
                .setThumbnail(guild.iconURL({ dynamic: true, size: 256 }))
                .setColor('#7289da')
                .addFields(
                    {
                        name: '👑 المالك',
                        value: `<@${guild.ownerId}>`,
                        inline: true
                    },
                    {
                        name: '🆔 معرف السيرفر',
                        value: `\`${guild.id}\``,
                        inline: true
                    },
                    {
                        name: '📅 تاريخ الإنشاء',
                        value: `<t:${Math.floor(guild.createdTimestamp / 1000)}:F>`,
                        inline: false
                    },
                    {
                        name: '👥 الأعضاء',
                        value: [
                            `**المجموع:** ${totalMembers.toLocaleString()}`,
                            `**البشر:** ${humanCount.toLocaleString()}`,
                            `**البوتات:** ${botCount.toLocaleString()}`,
                            `**متصل:** ${onlineMembers.toLocaleString()}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '📺 القنوات',
                        value: [
                            `**نصية:** ${textChannels}`,
                            `**صوتية:** ${voiceChannels}`,
                            `**فئات:** ${categories}`,
                            `**المجموع:** ${guild.channels.cache.size}`
                        ].join('\n'),
                        inline: true
                    },
                    {
                        name: '🎭 الرتب',
                        value: `${guild.roles.cache.size} رتبة`,
                        inline: true
                    },
                    {
                        name: '😀 الإيموجي',
                        value: `${guild.emojis.cache.size} إيموجي`,
                        inline: true
                    },
                    {
                        name: '🔒 مستوى التحقق',
                        value: verificationLevels[guild.verificationLevel] || 'غير معروف',
                        inline: true
                    },
                    {
                        name: '🛡️ فلتر المحتوى',
                        value: explicitLevels[guild.explicitContentFilter] || 'غير معروف',
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ 
                    text: `طلب بواسطة ${message.author.username}`, 
                    iconURL: message.author.displayAvatarURL() 
                });
            
            // إضافة البانر إذا كان متوفراً
            if (guild.bannerURL()) {
                embed.setImage(guild.bannerURL({ dynamic: true, size: 1024 }));
            }
            
            // إضافة الميزات إذا كانت متوفرة
            if (features.length > 0) {
                embed.addFields({
                    name: '✨ الميزات المتاحة',
                    value: features.slice(0, 10).join('\n') || 'لا توجد ميزات خاصة',
                    inline: false
                });
            }
            
            // إضافة معلومات Boost
            if (guild.premiumSubscriptionCount > 0) {
                embed.addFields({
                    name: '🚀 Nitro Boost',
                    value: [
                        `**المستوى:** ${guild.premiumTier}`,
                        `**عدد البوستات:** ${guild.premiumSubscriptionCount}`,
                        `**المطلوب للمستوى التالي:** ${guild.premiumTier === 3 ? 'الحد الأقصى' : 
                            guild.premiumTier === 2 ? (30 - guild.premiumSubscriptionCount) :
                            guild.premiumTier === 1 ? (15 - guild.premiumSubscriptionCount) :
                            (2 - guild.premiumSubscriptionCount)}`
                    ].join('\n'),
                    inline: false
                });
            }
            
            await message.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('خطأ في أمر serverinfo:', error);
            message.reply('❌ حدث خطأ أثناء جلب معلومات السيرفر!');
        }
    }
};
