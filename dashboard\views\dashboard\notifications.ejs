<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات | CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .notifications-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .notification-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
        }

        .notification-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        .notification-card.unread {
            border-left: 5px solid #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }

        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-left: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .filter-tabs {
            background: white;
            border-radius: 15px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .filter-tab {
            border: none;
            background: transparent;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            color: #666;
            font-weight: 500;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
        }

        .send-notification-form {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .user-selector {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
        }

        .user-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .user-item:hover {
            background: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس الإشعارات -->
            <div class="notifications-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="text-white mb-2">
                            <i class="fas fa-bell me-2"></i>
                            مركز الإشعارات
                        </h1>
                        <p class="text-white-50 mb-0">
                            إدارة وإرسال الإشعارات لجميع المستخدمين
                        </p>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex gap-2">
                            <button class="btn btn-light" onclick="refreshNotifications()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                            <button class="btn btn-warning" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i> تمييز الكل كمقروء
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الإشعارات -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div>إجمالي الإشعارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div>غير مقروءة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div>اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div>هذا الأسبوع</div>
                </div>
            </div>

            <% if (typeof isOwner !== 'undefined' && isOwner) { %>
            <!-- نموذج إرسال إشعار جديد -->
            <div class="send-notification-form">
                <h3 class="mb-4">
                    <i class="fas fa-paper-plane text-primary me-2"></i>
                    إرسال إشعار جديد
                </h3>
                <form id="sendNotificationForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="notificationTitle" class="form-label">عنوان الإشعار</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="notificationType" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notificationType" required>
                                <option value="info">معلومات</option>
                                <option value="success">نجاح</option>
                                <option value="warning">تحذير</option>
                                <option value="danger">خطر</option>
                                <option value="announcement">إعلان</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notificationMessage" class="form-label">رسالة الإشعار</label>
                        <textarea class="form-control" id="notificationMessage" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المستلمين</label>
                        <div class="d-flex gap-2 mb-2">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllUsers()">
                                <i class="fas fa-users"></i> الجميع
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="selectOnlineUsers()">
                                <i class="fas fa-circle text-success"></i> المتصلين فقط
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectOwners()">
                                <i class="fas fa-crown"></i> المالكين فقط
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="fas fa-times"></i> مسح التحديد
                            </button>
                        </div>
                        <div class="user-selector" id="userSelector">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> إرسال الإشعار
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewNotification()">
                            <i class="fas fa-eye"></i> معاينة
                        </button>
                    </div>
                </form>
            </div>
            <% } %>

            <!-- فلاتر الإشعارات -->
            <div class="filter-tabs">
                <div class="d-flex">
                    <button class="filter-tab active" onclick="filterNotifications('all')">
                        <i class="fas fa-list"></i> الجميع
                    </button>
                    <button class="filter-tab" onclick="filterNotifications('unread')">
                        <i class="fas fa-envelope"></i> غير مقروءة
                    </button>
                    <button class="filter-tab" onclick="filterNotifications('system')">
                        <i class="fas fa-cog"></i> النظام
                    </button>
                    <button class="filter-tab" onclick="filterNotifications('user')">
                        <i class="fas fa-user"></i> المستخدمين
                    </button>
                    <button class="filter-tab" onclick="filterNotifications('server')">
                        <i class="fas fa-server"></i> السيرفرات
                    </button>
                </div>
            </div>

            <!-- قائمة الإشعارات -->
            <div id="notificationsList">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>

            <!-- Pagination -->
            <nav aria-label="صفحات الإشعارات">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <!-- Modal معاينة الإشعار -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة الإشعار</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="previewContent">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="sendNotificationFromPreview()">إرسال</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentFilter = 'all';
        let selectedUsers = [];
        let allUsers = [];

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            loadUsers();
        });

        // تحميل الإشعارات
        async function loadNotifications() {
            try {
                // عرض إشعارات تجريبية
                displayDemoNotifications();
            } catch (error) {
                console.error('خطأ في تحميل الإشعارات:', error);
            }
        }

        // عرض إشعارات تجريبية
        function displayDemoNotifications() {
            const notifications = [
                {
                    id: '1',
                    type: 'server_join',
                    title: 'تم إضافة البوت لسيرفر جديد',
                    message: 'تم إضافة البوت لسيرفر "مجتمع المطورين" بنجاح',
                    createdAt: new Date(Date.now() - 5 * 60 * 1000),
                    read: false,
                    category: 'server'
                },
                {
                    id: '2',
                    type: 'user_register',
                    title: 'مستخدم جديد سجل',
                    message: 'المستخدم Ahmed#1234 سجل في لوحة التحكم',
                    createdAt: new Date(Date.now() - 15 * 60 * 1000),
                    read: false,
                    category: 'user'
                },
                {
                    id: '3',
                    type: 'system_alert',
                    title: 'تنبيه النظام',
                    message: 'استخدام عالي للذاكرة - 85% من الحد الأقصى',
                    createdAt: new Date(Date.now() - 60 * 60 * 1000),
                    read: true,
                    category: 'system'
                },
                {
                    id: '4',
                    type: 'premium_upgrade',
                    title: 'ترقية بريميوم',
                    message: 'تم ترقية سيرفر "الألعاب العربية" للبريميوم',
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    read: true,
                    category: 'server'
                }
            ];

            displayNotifications(notifications);
        }

        // عرض الإشعارات
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationsList');
            
            if (!notifications || notifications.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد إشعارات</h4>
                        <p class="text-muted">لم يتم العثور على إشعارات تطابق الفلتر المحدد</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = notifications.map(notification => `
                <div class="notification-card ${notification.read ? '' : 'unread'}" data-category="${notification.category}">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon ${getNotificationIconClass(notification.type)}">
                            <i class="fas ${getNotificationIcon(notification.type)}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="mb-1">${notification.title}</h5>
                                <div class="d-flex gap-2">
                                    <small class="text-muted">${formatTime(notification.createdAt)}</small>
                                    ${!notification.read ? '<span class="badge bg-primary">جديد</span>' : ''}
                                </div>
                            </div>
                            <p class="text-muted mb-2">${notification.message}</p>
                            <div class="d-flex gap-2">
                                ${!notification.read ? `<button class="btn btn-sm btn-outline-primary" onclick="markAsRead('${notification.id}')">تمييز كمقروء</button>` : ''}
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteNotification('${notification.id}')">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // باقي الدوال...
        function getNotificationIcon(type) {
            const icons = {
                'server_join': 'fa-server',
                'user_register': 'fa-user-plus',
                'system_alert': 'fa-exclamation-triangle',
                'premium_upgrade': 'fa-crown',
                'command_usage': 'fa-terminal',
                'default': 'fa-bell'
            };
            return icons[type] || icons.default;
        }

        function getNotificationIconClass(type) {
            const classes = {
                'server_join': 'bg-success',
                'user_register': 'bg-info',
                'system_alert': 'bg-warning',
                'premium_upgrade': 'bg-warning',
                'command_usage': 'bg-primary',
                'default': 'bg-secondary'
            };
            return classes[type] || classes.default;
        }

        function formatTime(date) {
            const now = new Date();
            const diff = now - new Date(date);
            const minutes = Math.floor(diff / 60000);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
            if (hours > 0) return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
            if (minutes > 0) return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
            return 'الآن';
        }

        function filterNotifications(filter) {
            currentFilter = filter;
            
            // تحديث الفلاتر
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // تطبيق الفلتر
            const cards = document.querySelectorAll('.notification-card');
            cards.forEach(card => {
                const category = card.dataset.category;
                const isUnread = card.classList.contains('unread');
                
                let show = false;
                switch(filter) {
                    case 'all':
                        show = true;
                        break;
                    case 'unread':
                        show = isUnread;
                        break;
                    default:
                        show = category === filter;
                }
                
                card.style.display = show ? 'block' : 'none';
            });
        }

        function refreshNotifications() {
            loadNotifications();
            showToast('تم تحديث الإشعارات', 'success');
        }

        function markAsRead(notificationId) {
            const card = document.querySelector(`[onclick*="${notificationId}"]`).closest('.notification-card');
            card.classList.remove('unread');
            card.querySelector('.badge')?.remove();
            card.querySelector(`[onclick*="${notificationId}"]`).remove();
            showToast('تم تمييز الإشعار كمقروء', 'success');
        }

        function deleteNotification(notificationId) {
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                const card = document.querySelector(`[onclick*="deleteNotification('${notificationId}')"]`).closest('.notification-card');
                card.remove();
                showToast('تم حذف الإشعار', 'success');
            }
        }

        function markAllAsRead() {
            document.querySelectorAll('.notification-card.unread').forEach(card => {
                card.classList.remove('unread');
                card.querySelector('.badge')?.remove();
                card.querySelector('[onclick*="markAsRead"]')?.remove();
            });
            showToast('تم تمييز جميع الإشعارات كمقروءة', 'success');
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : 'info'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
