/**
 * CS Bot - نموذج السيرفر (محدث لقاعدة البيانات المؤقتة)
 *
 * هذا الملف يحتوي على نموذج قاعدة البيانات للسيرفرات
 */

// const mongoose = require('mongoose'); // معطل مؤقتاً

// مخطط السيرفر (معطل مؤقتاً)
/*
const guildSchema = new mongoose.Schema({
    guildId: {
        type: String,
        required: true,
        unique: true
    },
    prefix: {
        type: String,
        default: '!'
    },
    language: {
        type: String,
        default: 'ar',
        enum: ['ar', 'en']
    },
    welcome: {
        enabled: {
            type: Boolean,
            default: false
        },
        channel: {
            type: String,
            default: null
        },
        message: {
            type: String,
            default: 'مرحباً {user} في سيرفر {server}! أنت العضو رقم {count}.'
        },
        embedEnabled: {
            type: Boolean,
            default: true
        },
        embedColor: {
            type: String,
            default: '#2F3136'
        },
        embedTitle: {
            type: String,
            default: 'عضو جديد!'
        },
        embedDescription: {
            type: String,
            default: 'مرحباً {user} في سيرفر {server}! أنت العضو رقم {count}.'
        },
        embedThumbnail: {
            type: Boolean,
            default: true
        },
        embedFooter: {
            type: String,
            default: 'CS Bot'
        }
    },
    logs: {
        enabled: {
            type: Boolean,
            default: false
        },
        channel: {
            type: String,
            default: null
        },
        events: {
            memberJoin: {
                type: Boolean,
                default: true
            },
            memberLeave: {
                type: Boolean,
                default: true
            },
            messageDelete: {
                type: Boolean,
                default: true
            },
            messageEdit: {
                type: Boolean,
                default: true
            },
            channelCreate: {
                type: Boolean,
                default: true
            },
            channelDelete: {
                type: Boolean,
                default: true
            },
            roleCreate: {
                type: Boolean,
                default: true
            },
            roleDelete: {
                type: Boolean,
                default: true
            },
            ban: {
                type: Boolean,
                default: true
            },
            unban: {
                type: Boolean,
                default: true
            }
        }
    },
    tickets: {
        enabled: {
            type: Boolean,
            default: false
        },
        category: {
            type: String,
            default: null
        },
        supportRole: {
            type: String,
            default: null
        },
        message: {
            type: String,
            default: 'شكراً لفتح تذكرة! سيتم الرد عليك قريباً.'
        },
        closeMessage: {
            type: String,
            default: 'سيتم إغلاق التذكرة خلال 5 ثوانٍ.'
        }
    },
    autoRole: {
        enabled: {
            type: Boolean,
            default: false
        },
        roles: {
            type: [String],
            default: []
        }
    },
    economy: {
        enabled: {
            type: Boolean,
            default: true
        },
        currency: {
            type: String,
            default: 'CS'
        },
        startBalance: {
            type: Number,
            default: 500
        },
        dailyAmount: {
            type: Number,
            default: 200
        },
        workMinAmount: {
            type: Number,
            default: 50
        },
        workMaxAmount: {
            type: Number,
            default: 200
        }
    },
    antiSpam: {
        enabled: {
            type: Boolean,
            default: false
        },
        maxMessages: {
            type: Number,
            default: 5
        },
        timeframe: {
            type: Number,
            default: 5000
        },
        muteTime: {
            type: Number,
            default: 300000 // 5 دقائق
        }
    }
}, { timestamps: true });

// إنشاء النموذج
const Guild = mongoose.model('Guild', guildSchema);
*/

// دالة للحصول على إعدادات السيرفر (محدثة لقاعدة البيانات المؤقتة)
async function getGuildSettings(guildId, guildName = '') {
    try {
        const memoryDB = require('../utils/memoryDB');
        const guild = await memoryDB.getGuild(guildId, guildName);

        // إضافة دالة save للتوافق مع الكود الموجود
        guild.save = async function() {
            return memoryDB.updateGuild(guildId, this);
        };

        return guild;
    } catch (err) {
        console.error('خطأ في الحصول على إعدادات السيرفر:', err);
        // إرجاع إعدادات افتراضية في حالة الخطأ
        return {
            id: guildId,
            name: guildName,
            prefix: '!',
            welcome: { enabled: false },
            goodbye: { enabled: false },
            autoRole: { enabled: false },
            logs: { enabled: false },
            tickets: { enabled: false },
            economy: { enabled: false },
            autoReply: { enabled: false },
            save: async function() { return this; }
        };
    }
}

// دالة لحفظ إعدادات السيرفر (محدثة لقاعدة البيانات المؤقتة)
async function saveGuildSettings(guildId, settings) {
    try {
        const memoryDB = require('../utils/memoryDB');
        const guild = await memoryDB.getGuild(guildId);

        // تحديث الإعدادات
        Object.keys(settings).forEach(key => {
            if (key !== 'id' && key !== '_id' && key !== '__v') {
                guild[key] = settings[key];
            }
        });

        // حفظ التغييرات
        await memoryDB.updateGuild(guildId, guild);

        return guild;
    } catch (err) {
        console.error('خطأ في حفظ إعدادات السيرفر:', err);
        throw err;
    }
}

module.exports = {
    getGuildSettings,
    saveGuildSettings
};
