const mongoose = require('mongoose');

const guildSchema = new mongoose.Schema({
    guildId: {
        type: String,
        required: true,
        unique: true
    },
    name: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        default: null
    },
    ownerId: {
        type: String,
        required: true
    },
    
    // إحصائيات السيرفر
    stats: {
        memberCount: {
            type: Number,
            default: 0
        },
        channelCount: {
            type: Number,
            default: 0
        },
        roleCount: {
            type: Number,
            default: 0
        },
        commandsUsed: {
            type: Number,
            default: 0
        },
        messagesCount: {
            type: Number,
            default: 0
        },
        joinedMembers: {
            type: Number,
            default: 0
        },
        leftMembers: {
            type: Number,
            default: 0
        }
    },
    
    // إعدادات السيرفر
    settings: {
        prefix: {
            type: String,
            default: '!'
        },
        language: {
            type: String,
            enum: ['ar', 'en'],
            default: 'ar'
        },
        timezone: {
            type: String,
            default: 'Asia/Riyadh'
        },
        autoRole: {
            enabled: {
                type: Boolean,
                default: false
            },
            roleId: {
                type: String,
                default: null
            }
        },
        welcome: {
            enabled: {
                type: Boolean,
                default: false
            },
            channelId: {
                type: String,
                default: null
            },
            message: {
                type: String,
                default: 'مرحباً {user} في {server}!'
            }
        },
        goodbye: {
            enabled: {
                type: Boolean,
                default: false
            },
            channelId: {
                type: String,
                default: null
            },
            message: {
                type: String,
                default: 'وداعاً {user}!'
            }
        },
        moderation: {
            enabled: {
                type: Boolean,
                default: true
            },
            logChannelId: {
                type: String,
                default: null
            },
            autoMod: {
                enabled: {
                    type: Boolean,
                    default: false
                },
                deleteSpam: {
                    type: Boolean,
                    default: true
                },
                deleteLinks: {
                    type: Boolean,
                    default: false
                },
                deleteBadWords: {
                    type: Boolean,
                    default: false
                }
            }
        },
        economy: {
            enabled: {
                type: Boolean,
                default: true
            },
            currency: {
                type: String,
                default: '💰'
            },
            dailyAmount: {
                type: Number,
                default: 100
            },
            workAmount: {
                type: Number,
                default: 50
            }
        },
        music: {
            enabled: {
                type: Boolean,
                default: true
            },
            maxQueueSize: {
                type: Number,
                default: 50
            },
            defaultVolume: {
                type: Number,
                default: 50
            }
        }
    },
    
    // البريميوم
    premium: {
        active: {
            type: Boolean,
            default: false
        },
        startDate: {
            type: Date,
            default: null
        },
        endDate: {
            type: Date,
            default: null
        },
        features: [{
            type: String,
            enum: ['custom_commands', 'advanced_moderation', 'premium_music', 'custom_welcome']
        }]
    },
    
    // الأوامر المخصصة
    customCommands: [{
        name: {
            type: String,
            required: true
        },
        response: {
            type: String,
            required: true
        },
        createdBy: {
            type: String,
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        usageCount: {
            type: Number,
            default: 0
        }
    }],
    
    // الكلمات المحظورة
    bannedWords: [{
        word: String,
        action: {
            type: String,
            enum: ['delete', 'warn', 'mute', 'kick', 'ban'],
            default: 'delete'
        }
    }],
    
    // تواريخ مهمة
    joinedAt: {
        type: Date,
        default: Date.now
    },
    lastActivity: {
        type: Date,
        default: Date.now
    },
    leftAt: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});

// إنشاء فهارس للبحث السريع
guildSchema.index({ guildId: 1 });
guildSchema.index({ ownerId: 1 });
guildSchema.index({ 'premium.active': 1 });

// دوال مساعدة
guildSchema.methods.isPremium = function() {
    return this.premium.active && this.premium.endDate > new Date();
};

guildSchema.methods.addCommand = function() {
    this.stats.commandsUsed += 1;
    this.lastActivity = new Date();
};

guildSchema.methods.updateMemberCount = function(count) {
    this.stats.memberCount = count;
    this.lastActivity = new Date();
};

module.exports = mongoose.model('Guild', guildSchema);
