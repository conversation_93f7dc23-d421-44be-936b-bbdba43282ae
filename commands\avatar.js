/**
 * CS Bot - أمر Avatar
 * 
 * يعرض صورة المستخدم بجودة عالية مع معلومات إضافية
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'avatar',
    description: 'عرض صورة المستخدم بجودة عالية',
    usage: '!avatar [@user]',
    category: 'عام',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            // تحديد المستخدم المطلوب
            let targetUser = message.author;
            
            if (args[0]) {
                // البحث عن المستخدم المذكور
                const mention = message.mentions.users.first();
                if (mention) {
                    targetUser = mention;
                } else {
                    // البحث بالاسم أو ID
                    const searchTerm = args.join(' ');
                    const foundUser = message.guild.members.cache.find(member => 
                        member.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        member.user.id === searchTerm ||
                        member.displayName.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                    
                    if (foundUser) {
                        targetUser = foundUser.user;
                    } else {
                        return message.reply('❌ لم أتمكن من العثور على هذا المستخدم!');
                    }
                }
            }
            
            // الحصول على معلومات العضو في السيرفر
            const member = message.guild.members.cache.get(targetUser.id);
            
            // إنشاء Embed مذهل
            const embed = new EmbedBuilder()
                .setTitle(`🖼️ صورة ${targetUser.username}`)
                .setDescription(`**معلومات المستخدم:**`)
                .addFields(
                    {
                        name: '👤 الاسم',
                        value: `${targetUser.username}#${targetUser.discriminator}`,
                        inline: true
                    },
                    {
                        name: '🆔 المعرف',
                        value: `\`${targetUser.id}\``,
                        inline: true
                    },
                    {
                        name: '📅 تاريخ الإنشاء',
                        value: `<t:${Math.floor(targetUser.createdTimestamp / 1000)}:F>`,
                        inline: false
                    }
                )
                .setColor('#00ff88')
                .setTimestamp()
                .setFooter({ 
                    text: `طلب بواسطة ${message.author.username}`, 
                    iconURL: message.author.displayAvatarURL() 
                });
            
            // إضافة معلومات العضو إذا كان في السيرفر
            if (member) {
                embed.addFields(
                    {
                        name: '📝 الاسم المستعار',
                        value: member.displayName || 'لا يوجد',
                        inline: true
                    },
                    {
                        name: '📅 تاريخ الانضمام',
                        value: `<t:${Math.floor(member.joinedTimestamp / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '🎭 الرتب',
                        value: member.roles.cache
                            .filter(role => role.id !== message.guild.id)
                            .map(role => role.toString())
                            .slice(0, 5)
                            .join(', ') || 'لا توجد رتب',
                        inline: false
                    }
                );
            }
            
            // إضافة الصورة
            const avatarURL = targetUser.displayAvatarURL({ 
                dynamic: true, 
                size: 4096,
                format: 'png'
            });
            
            embed.setImage(avatarURL);
            embed.setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 256 }));
            
            // إنشاء أزرار للتحميل
            const { ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
            
            const buttons = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('📥 تحميل PNG')
                        .setURL(targetUser.displayAvatarURL({ format: 'png', size: 4096 }))
                        .setStyle(ButtonStyle.Link),
                    new ButtonBuilder()
                        .setLabel('📥 تحميل JPG')
                        .setURL(targetUser.displayAvatarURL({ format: 'jpg', size: 4096 }))
                        .setStyle(ButtonStyle.Link),
                    new ButtonBuilder()
                        .setLabel('📥 تحميل WEBP')
                        .setURL(targetUser.displayAvatarURL({ format: 'webp', size: 4096 }))
                        .setStyle(ButtonStyle.Link)
                );
            
            // إضافة زر GIF إذا كانت الصورة متحركة
            if (targetUser.avatar && targetUser.avatar.startsWith('a_')) {
                buttons.addComponents(
                    new ButtonBuilder()
                        .setLabel('📥 تحميل GIF')
                        .setURL(targetUser.displayAvatarURL({ format: 'gif', size: 4096 }))
                        .setStyle(ButtonStyle.Link)
                );
            }
            
            await message.reply({ 
                embeds: [embed], 
                components: [buttons] 
            });
            
        } catch (error) {
            console.error('خطأ في أمر avatar:', error);
            message.reply('❌ حدث خطأ أثناء جلب صورة المستخدم!');
        }
    }
};
