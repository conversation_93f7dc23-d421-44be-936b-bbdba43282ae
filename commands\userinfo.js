/**
 * CS Bot - أمر User Info
 * 
 * يعرض معلومات شاملة عن المستخدم
 */

const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'userinfo',
    aliases: ['user', 'whois', 'profile'],
    description: 'عرض معلومات شاملة عن المستخدم',
    usage: '!userinfo [@user]',
    category: 'معلومات',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            // تحديد المستخدم المطلوب
            let targetUser = message.author;
            let targetMember = message.member;
            
            if (args[0]) {
                const mention = message.mentions.users.first();
                if (mention) {
                    targetUser = mention;
                    targetMember = message.guild.members.cache.get(mention.id);
                } else {
                    const searchTerm = args.join(' ');
                    const foundMember = message.guild.members.cache.find(member => 
                        member.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        member.user.id === searchTerm ||
                        member.displayName.toLowerCase().includes(searchTerm.toLowerCase())
                    );
                    
                    if (foundMember) {
                        targetUser = foundMember.user;
                        targetMember = foundMember;
                    } else {
                        return message.reply('❌ لم أتمكن من العثور على هذا المستخدم!');
                    }
                }
            }
            
            // حالة المستخدم
            const statusEmojis = {
                'online': '🟢 متصل',
                'idle': '🟡 خامل',
                'dnd': '🔴 مشغول',
                'offline': '⚫ غير متصل'
            };
            
            // نوع المستخدم
            const userFlags = targetUser.flags?.toArray() || [];
            const badges = userFlags.map(flag => {
                const flagEmojis = {
                    'Staff': '👨‍💼 موظف Discord',
                    'Partner': '🤝 شريك Discord',
                    'Hypesquad': '🎉 HypeSquad',
                    'BugHunterLevel1': '🐛 صياد الأخطاء المستوى 1',
                    'BugHunterLevel2': '🐛 صياد الأخطاء المستوى 2',
                    'HypesquadOnlineHouse1': '🏠 Bravery',
                    'HypesquadOnlineHouse2': '🏠 Brilliance',
                    'HypesquadOnlineHouse3': '🏠 Balance',
                    'PremiumEarlySupporter': '💎 مؤيد مبكر',
                    'VerifiedDeveloper': '🔧 مطور موثق',
                    'CertifiedModerator': '🛡️ مشرف معتمد',
                    'BotHTTPInteractions': '🤖 بوت HTTP'
                };
                return flagEmojis[flag] || flag;
            });
            
            // إنشاء Embed
            const embed = new EmbedBuilder()
                .setTitle(`👤 معلومات المستخدم: ${targetUser.username}`)
                .setThumbnail(targetUser.displayAvatarURL({ dynamic: true, size: 256 }))
                .setColor(targetMember?.displayHexColor || '#7289da')
                .addFields(
                    {
                        name: '🏷️ المعلومات الأساسية',
                        value: [
                            `**الاسم:** ${targetUser.username}#${targetUser.discriminator}`,
                            `**المعرف:** \`${targetUser.id}\``,
                            `**نوع الحساب:** ${targetUser.bot ? '🤖 بوت' : '👤 مستخدم'}`,
                            `**تاريخ الإنشاء:** <t:${Math.floor(targetUser.createdTimestamp / 1000)}:F>`
                        ].join('\n'),
                        inline: false
                    }
                );
            
            // معلومات العضو في السيرفر
            if (targetMember) {
                const status = targetMember.presence?.status || 'offline';
                const activity = targetMember.presence?.activities[0];
                
                embed.addFields(
                    {
                        name: '🏰 معلومات السيرفر',
                        value: [
                            `**الاسم المستعار:** ${targetMember.displayName}`,
                            `**الحالة:** ${statusEmojis[status]}`,
                            `**تاريخ الانضمام:** <t:${Math.floor(targetMember.joinedTimestamp / 1000)}:F>`,
                            `**ترتيب الانضمام:** #${message.guild.members.cache
                                .filter(m => m.joinedTimestamp < targetMember.joinedTimestamp).size + 1}`
                        ].join('\n'),
                        inline: false
                    }
                );
                
                // النشاط الحالي
                if (activity) {
                    const activityTypes = {
                        0: '🎮 يلعب',
                        1: '📺 يشاهد',
                        2: '🎵 يستمع إلى',
                        3: '📺 يبث',
                        5: '🏆 يتنافس في'
                    };
                    
                    embed.addFields({
                        name: '🎯 النشاط الحالي',
                        value: `${activityTypes[activity.type] || '🎯'} **${activity.name}**${
                            activity.details ? `\n${activity.details}` : ''
                        }${activity.state ? `\n${activity.state}` : ''}`,
                        inline: false
                    });
                }
                
                // الرتب
                const roles = targetMember.roles.cache
                    .filter(role => role.id !== message.guild.id)
                    .sort((a, b) => b.position - a.position)
                    .map(role => role.toString());
                
                if (roles.length > 0) {
                    embed.addFields({
                        name: `🎭 الرتب (${roles.length})`,
                        value: roles.slice(0, 20).join(', ') + (roles.length > 20 ? '...' : ''),
                        inline: false
                    });
                }
                
                // الصلاحيات المهمة
                const keyPermissions = [
                    'Administrator', 'ManageGuild', 'ManageRoles', 'ManageChannels',
                    'ManageMessages', 'BanMembers', 'KickMembers', 'MentionEveryone'
                ];
                
                const userPermissions = keyPermissions.filter(perm => 
                    targetMember.permissions.has(perm)
                ).map(perm => {
                    const permNames = {
                        'Administrator': '👑 مدير',
                        'ManageGuild': '🏰 إدارة السيرفر',
                        'ManageRoles': '🎭 إدارة الرتب',
                        'ManageChannels': '📺 إدارة القنوات',
                        'ManageMessages': '💬 إدارة الرسائل',
                        'BanMembers': '🔨 حظر الأعضاء',
                        'KickMembers': '👢 طرد الأعضاء',
                        'MentionEveryone': '📢 منشن الجميع'
                    };
                    return permNames[perm] || perm;
                });
                
                if (userPermissions.length > 0) {
                    embed.addFields({
                        name: '🔑 الصلاحيات المهمة',
                        value: userPermissions.join('\n'),
                        inline: false
                    });
                }
            }
            
            // الشارات
            if (badges.length > 0) {
                embed.addFields({
                    name: '🏅 الشارات',
                    value: badges.join('\n'),
                    inline: false
                });
            }
            
            // معلومات إضافية
            const additionalInfo = [];
            
            if (targetUser.bot) {
                additionalInfo.push('🤖 هذا حساب بوت');
            }
            
            if (targetUser.system) {
                additionalInfo.push('⚙️ حساب نظام Discord');
            }
            
            if (targetMember?.premiumSince) {
                additionalInfo.push(`🚀 يدعم السيرفر منذ <t:${Math.floor(targetMember.premiumSinceTimestamp / 1000)}:F>`);
            }
            
            if (additionalInfo.length > 0) {
                embed.addFields({
                    name: 'ℹ️ معلومات إضافية',
                    value: additionalInfo.join('\n'),
                    inline: false
                });
            }
            
            embed.setTimestamp()
                .setFooter({ 
                    text: `طلب بواسطة ${message.author.username}`, 
                    iconURL: message.author.displayAvatarURL() 
                });
            
            await message.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('خطأ في أمر userinfo:', error);
            message.reply('❌ حدث خطأ أثناء جلب معلومات المستخدم!');
        }
    }
};
