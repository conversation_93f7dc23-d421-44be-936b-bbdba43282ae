<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الرئيسية - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64"
                 alt="CS Bot"
                 class="rounded-circle me-2"
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-tachometer-alt"></i> CS Bot - لوحة التحكم</span>
        </a>
        <div class="d-flex">
            <span class="navbar-text me-3">
                مرحباً، <%= user.username %>
            </span>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> لوحة التحكم الرئيسية</h1>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-server fa-2x text-primary mb-2"></i>
                            <h4 class="card-title">2</h4>
                            <p class="card-text text-muted">السيرفرات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-success mb-2"></i>
                            <h4 class="card-title">11</h4>
                            <p class="card-text text-muted">المستخدمين</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-terminal fa-2x text-info mb-2"></i>
                            <h4 class="card-title">18</h4>
                            <p class="card-text text-muted">الأوامر</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4 class="card-title">متصل</h4>
                            <p class="card-text text-muted">حالة البوت</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الإدارة -->
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">إدارة المستخدمين</h5>
                            <p class="card-text">عرض وإدارة جميع مستخدمي البوت</p>
                            <a href="/dashboard/users" class="btn btn-primary">
                                <i class="fas fa-users"></i> إدارة المستخدمين
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-server fa-3x text-success mb-3"></i>
                            <h5 class="card-title">إدارة السيرفرات</h5>
                            <p class="card-text">عرض وإدارة جميع سيرفرات البوت</p>
                            <a href="/dashboard/servers" class="btn btn-success">
                                <i class="fas fa-server"></i> إدارة السيرفرات
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-crown fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">إدارة البريميوم</h5>
                            <p class="card-text">إدارة اشتراكات البريميوم</p>
                            <a href="/dashboard/premium" class="btn btn-warning">
                                <i class="fas fa-crown"></i> إدارة البريميوم
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                            <h5 class="card-title">التحليلات</h5>
                            <p class="card-text">إحصائيات وتحليلات مفصلة</p>
                            <a href="/dashboard/analytics" class="btn btn-info">
                                <i class="fas fa-chart-bar"></i> عرض التحليلات
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-3x text-secondary mb-3"></i>
                            <h5 class="card-title">الإعدادات</h5>
                            <p class="card-text">إعدادات البوت والنظام</p>
                            <a href="/dashboard/settings" class="btn btn-secondary">
                                <i class="fas fa-cogs"></i> الإعدادات
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user fa-3x text-dark mb-3"></i>
                            <h5 class="card-title">الملف الشخصي</h5>
                            <p class="card-text">عرض وتعديل ملفك الشخصي</p>
                            <a href="/dashboard/profile" class="btn btn-dark">
                                <i class="fas fa-user"></i> الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-user-shield fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">إدارة المالكين</h5>
                            <p class="card-text">إضافة وإدارة مالكين جدد للبوت</p>
                            <button class="btn btn-warning" onclick="showAddOwnerModal()">
                                <i class="fas fa-user-plus"></i> إضافة مالك
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm h-100 border-danger">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-3x text-danger mb-3"></i>
                            <h5 class="card-title">أدوات المطور</h5>
                            <p class="card-text">أدوات متقدمة للمطورين</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-danger btn-sm" onclick="restartBot()">
                                    <i class="fas fa-redo"></i> إعادة تشغيل
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="clearCache()">
                                    <i class="fas fa-broom"></i> مسح الذاكرة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Owner Modal -->
<div class="modal fade" id="addOwnerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-shield"></i> إضافة مالك جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addOwnerForm">
                    <div class="mb-3">
                        <label for="ownerUserId" class="form-label">معرف المستخدم (User ID)</label>
                        <input type="text" class="form-control" id="ownerUserId" required
                               placeholder="مثال: 123456789012345678">
                        <div class="form-text">
                            يمكنك الحصول على معرف المستخدم من خلال تفعيل وضع المطور في Discord
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="ownerNote" class="form-label">ملاحظة (اختياري)</label>
                        <textarea class="form-control" id="ownerNote" rows="3"
                                  placeholder="سبب إضافة هذا المالك..."></textarea>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> المالك الجديد سيحصل على صلاحيات كاملة للبوت ولوحة التحكم.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="addNewOwner()">
                    <i class="fas fa-user-plus"></i> إضافة المالك
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="bg-dark text-white py-4 mt-5">
    <div class="container text-center">
        <p>&copy; 2024 CS Bot. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// إظهار modal إضافة مالك
function showAddOwnerModal() {
    new bootstrap.Modal(document.getElementById('addOwnerModal')).show();
}

// إضافة مالك جديد
async function addNewOwner() {
    const userId = document.getElementById('ownerUserId').value.trim();
    const note = document.getElementById('ownerNote').value.trim();

    if (!userId) {
        alert('يرجى إدخال معرف المستخدم');
        return;
    }

    // التحقق من صحة معرف المستخدم
    if (!/^\d{17,19}$/.test(userId)) {
        alert('معرف المستخدم غير صحيح. يجب أن يكون رقماً من 17-19 خانة');
        return;
    }

    if (!confirm(`هل أنت متأكد من إضافة المستخدم ${userId} كمالك جديد؟`)) {
        return;
    }

    try {
        const response = await fetch('/api/owners/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId, note })
        });

        const result = await response.json();

        if (response.ok) {
            alert('تم إضافة المالك الجديد بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('addOwnerModal')).hide();

            // مسح النموذج
            document.getElementById('addOwnerForm').reset();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إضافة المالك');
        console.error('خطأ:', error);
    }
}

// إعادة تشغيل البوت
async function restartBot() {
    if (!confirm('هل أنت متأكد من إعادة تشغيل البوت؟ سيتم قطع الاتصال مؤقتاً.')) {
        return;
    }

    try {
        const response = await fetch('/api/restart', {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            alert('سيتم إعادة تشغيل البوت خلال 5 ثوان...');

            // إظهار مؤشر التحميل
            setTimeout(() => {
                location.reload();
            }, 10000); // إعادة تحميل الصفحة بعد 10 ثوان
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إعادة تشغيل البوت');
    }
}

// مسح الذاكرة المؤقتة
async function clearCache() {
    if (!confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) {
        return;
    }

    try {
        const response = await fetch('/api/system/clear-cache', {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            alert('تم مسح الذاكرة المؤقتة بنجاح!');
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في مسح الذاكرة المؤقتة');
    }
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(() => {
    // يمكن إضافة تحديث الإحصائيات هنا
}, 30000);
</script>
</body>
</html>
