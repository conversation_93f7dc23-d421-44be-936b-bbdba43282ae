/**
 * اختبار سريع للبوت - CS Bot
 */

console.log('🧪 اختبار البوت...');

// تحميل الإعدادات
const config = require('./config');
console.log('✅ تم تحميل الإعدادات');

// فحص التوكن
if (config.token && config.token !== 'YOUR_BOT_TOKEN_HERE') {
    console.log('✅ التوكن موجود');
} else {
    console.log('❌ التوكن غير موجود أو غير صحيح');
    process.exit(1);
}

// تحميل Discord.js
const { Client, GatewayIntentBits, Collection } = require('discord.js');
console.log('✅ تم تحميل Discord.js');

// إنشاء العميل
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

console.log('✅ تم إنشاء العميل');

// إعداد المجموعات
client.commands = new Collection();
client.slashCommands = new Collection();
client.stats = {
    commandsExecuted: 0,
    messagesProcessed: 0
};

console.log('✅ تم إعداد المجموعات');

// حدث الجاهزية
client.once('ready', () => {
    console.log('🎉 البوت متصل وجاهز!');
    console.log(`📊 الاسم: ${client.user.tag}`);
    console.log(`🏠 السيرفرات: ${client.guilds.cache.size}`);
    console.log(`👥 المستخدمين: ${client.users.cache.size}`);
    console.log(`📺 القنوات: ${client.channels.cache.size}`);
    console.log('');
    console.log('💡 البوت يعمل بشكل صحيح!');
    console.log('🔗 لوحة التحكم: http://localhost:3000');
    console.log('⚡ جرب الأوامر: !help2 أو /economy');
    console.log('');
    console.log('⏹️ لإيقاف البوت: اضغط Ctrl+C');
});

// حدث الرسائل
client.on('messageCreate', (message) => {
    if (message.author.bot) return;
    
    client.stats.messagesProcessed++;
    
    // اختبار أمر بسيط
    if (message.content === '!test') {
        message.reply('✅ البوت يعمل بشكل ممتاز! 🚀');
    }
    
    if (message.content === '!ping') {
        message.reply(`🏓 Pong! البينغ: ${client.ws.ping}ms`);
    }
});

// معالج الأخطاء
client.on('error', (error) => {
    console.error('❌ خطأ في البوت:', error);
});

process.on('unhandledRejection', (error) => {
    console.error('❌ رفض غير معالج:', error);
});

// تسجيل الدخول
console.log('🔐 تسجيل الدخول...');
client.login(config.token).catch(error => {
    console.error('❌ فشل تسجيل الدخول:', error);
    process.exit(1);
});
