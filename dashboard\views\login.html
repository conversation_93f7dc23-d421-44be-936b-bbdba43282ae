<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="bg-gradient">

<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <img src="https://cdn.discordapp.com/avatars/1289581696995561495/a_01234567890abcdef.gif?size=128"
                             alt="CS Bot"
                             class="rounded-circle mb-3"
                             width="80" height="80"
                             onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
                        <h2 class="fw-bold">CS Bot</h2>
                        <p class="text-muted">لوحة التحكم الأسطورية</p>
                    </div>

                    <!-- Error Messages -->
                    <div id="errorMessages"></div>

                    <!-- Login Form -->
                    <div class="text-center">
                        <h4 class="mb-4">تسجيل الدخول</h4>

                        <p class="text-muted mb-4">
                            سجل الدخول بحساب Discord للوصول إلى لوحة التحكم الأسطورية
                        </p>

                        <!-- Discord Login Button -->
                        <a href="/auth/discord" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fab fa-discord me-2"></i>
                            تسجيل الدخول بـ Discord
                        </a>

                        <!-- Info -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> المطورين يحصلون على لوحة التحكم الكاملة، المستخدمين العاديين يحصلون على لوحة إدارة سيرفراتهم
                        </div>

                        <!-- Back to Home -->
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>

            <!-- Features -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 bg-transparent">
                        <div class="card-body text-center">
                            <h6 class="text-white mb-3">مميزات لوحة التحكم:</h6>
                            <div class="row text-white">
                                <div class="col-4">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <p class="small">إدارة المستخدمين</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-crown fa-2x mb-2"></i>
                                    <p class="small">إدارة البريميوم</p>
                                </div>
                                <div class="col-4">
                                    <i class="fas fa-server fa-2x mb-2"></i>
                                    <p class="small">إدارة السيرفرات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// التحقق من وجود رسائل خطأ في URL
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    const errorDiv = document.getElementById('errorMessages');

    if (error) {
        let errorMessage = '';

        switch (error) {
            case 'no_code':
                errorMessage = 'لم يتم الحصول على رمز التفويض من Discord';
                break;
            case 'auth_failed':
                errorMessage = 'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى';
                break;
            case 'access_denied':
                errorMessage = 'تم رفض الوصول. يجب الموافقة على الصلاحيات للمتابعة';
                break;
            case 'invalid_code':
                errorMessage = 'رمز التفويض غير صحيح أو منتهي الصلاحية';
                break;
            case 'unauthorized':
                errorMessage = 'غير مصرح لك بالوصول';
                break;
            default:
                errorMessage = 'حدث خطأ غير معروف';
        }

        // إضافة رسالة تفصيلية إذا كانت متوفرة
        const message = urlParams.get('message');
        if (message) {
            errorMessage += '<br><small class="text-muted">التفاصيل: ' + decodeURIComponent(message) + '</small>';
        }

        errorDiv.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>خطأ:</strong> ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
});

// معالجة تسجيل دخول المطور
document.getElementById('devLoginForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const password = document.getElementById('devPassword').value;
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;

    // تأثير التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحقق...';
    button.disabled = true;

    try {
        const response = await fetch('/auth/dev-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password })
        });

        const result = await response.json();

        if (response.ok) {
            // نجح تسجيل الدخول
            button.innerHTML = '<i class="fas fa-check me-2"></i>تم بنجاح!';
            button.classList.remove('btn-success');
            button.classList.add('btn-success');

            // توجيه للداشبورد
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            // فشل تسجيل الدخول
            throw new Error(result.error || 'فشل في تسجيل الدخول');
        }
    } catch (error) {
        // عرض رسالة الخطأ
        const errorDiv = document.getElementById('errorMessages');
        errorDiv.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>خطأ:</strong> ${error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // استعادة الزر
        button.innerHTML = originalText;
        button.disabled = false;
    }
});

// تأثير تحميل عند الضغط على زر Discord
document.querySelector('a[href="/auth/discord"]').addEventListener('click', function(e) {
    const button = this;
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التوجيه إلى Discord...';
    button.classList.add('disabled');

    // في حالة عدم التوجيه خلال 5 ثوان، استعادة الزر
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('disabled');
    }, 5000);
});
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.btn-primary {
    background: linear-gradient(45deg, #5865F2, #7289DA);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
}

.fa-robot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.alert {
    border-radius: 15px;
}

.card {
    border-radius: 20px;
}

.btn {
    border-radius: 15px;
    font-weight: 600;
}
</style>

</body>
</html>
