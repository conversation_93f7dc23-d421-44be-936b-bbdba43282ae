/**
 * نظام الصلاحيات المتقدم - CS Bot
 * يدير صلاحيات المستخدمين والسيرفرات بدون قيود owner-only
 */

const { PermissionsBitField } = require('discord.js');
const User = require('../models/User');
const GuildSettings = require('../models/GuildSettings');

class PermissionManager {
    constructor() {
        // مستويات الصلاحيات
        this.levels = {
            EVERYONE: 0,
            MEMBER: 1,
            MODERATOR: 2,
            ADMIN: 3,
            SERVER_OWNER: 4,
            BOT_ADMIN: 5
        };

        // صلاحيات Discord المطلوبة لكل مستوى
        this.discordPermissions = {
            MODERATOR: [
                PermissionsBitField.Flags.KickMembers,
                PermissionsBitField.Flags.BanMembers,
                PermissionsBitField.Flags.ManageMessages,
                PermissionsBitField.Flags.MuteMembers
            ],
            ADMIN: [
                PermissionsBitField.Flags.Administrator,
                PermissionsBitField.Flags.ManageGuild,
                PermissionsBitField.Flags.ManageChannels,
                PermissionsBitField.Flags.ManageRoles
            ]
        };

        // قائمة مديري البوت (يمكن تعديلها من لوحة التحكم)
        this.botAdmins = new Set([
            '1289581696995561495' // المطور الأساسي
        ]);
    }

    // إضافة مدير بوت جديد
    addBotAdmin(userId) {
        this.botAdmins.add(userId);
        console.log(`✅ تم إضافة ${userId} كمدير بوت`);
        return true;
    }

    // إزالة مدير بوت
    removeBotAdmin(userId) {
        if (userId === '1289581696995561495') {
            return false; // لا يمكن إزالة المطور الأساسي
        }
        this.botAdmins.delete(userId);
        console.log(`❌ تم إزالة ${userId} من مديري البوت`);
        return true;
    }

    // الحصول على قائمة مديري البوت
    getBotAdmins() {
        return Array.from(this.botAdmins);
    }

    // فحص مستوى صلاحية المستخدم
    async getUserLevel(user, guild = null) {
        try {
            // فحص مديري البوت
            if (this.botAdmins.has(user.id)) {
                return this.levels.BOT_ADMIN;
            }

            // إذا لم يكن هناك سيرفر، المستخدم عادي
            if (!guild) {
                return this.levels.MEMBER;
            }

            // فحص مالك السيرفر
            if (guild.ownerId === user.id) {
                return this.levels.SERVER_OWNER;
            }

            // الحصول على عضو السيرفر
            const member = await guild.members.fetch(user.id).catch(() => null);
            if (!member) {
                return this.levels.EVERYONE;
            }

            // فحص صلاحيات الإدارة
            if (member.permissions.has(PermissionsBitField.Flags.Administrator)) {
                return this.levels.ADMIN;
            }

            // فحص صلاحيات الإشراف
            const hasModPerms = this.discordPermissions.MODERATOR.some(perm =>
                member.permissions.has(perm)
            );
            if (hasModPerms) {
                return this.levels.MODERATOR;
            }

            return this.levels.MEMBER;
        } catch (error) {
            console.error('❌ خطأ في فحص مستوى المستخدم:', error);
            return this.levels.EVERYONE;
        }
    }

    // فحص إذا كان المستخدم يملك صلاحية معينة
    async hasPermission(user, guild, requiredLevel) {
        const userLevel = await this.getUserLevel(user, guild);
        return userLevel >= requiredLevel;
    }

    // فحص صلاحيات الأوامر
    async checkCommandPermission(user, guild, command) {
        try {
            // الأوامر العامة متاحة للجميع
            if (!command.permission || command.permission === 'EVERYONE') {
                return { allowed: true };
            }

            // فحص إذا كان المستخدم محظور
            const userData = await User.findOne({ userId: user.id });
            if (userData?.moderation?.banned) {
                return {
                    allowed: false,
                    reason: 'المستخدم محظور من استخدام البوت'
                };
            }

            // فحص مستوى الصلاحية المطلوب
            const requiredLevel = this.levels[command.permission] || this.levels.MEMBER;
            const hasPermission = await this.hasPermission(user, guild, requiredLevel);

            if (!hasPermission) {
                return {
                    allowed: false,
                    reason: `هذا الأمر يتطلب مستوى صلاحية: ${command.permission}`
                };
            }

            // فحص البريميوم إذا كان مطلوب
            if (command.premium) {
                const isPremium = await this.checkPremium(user, guild);
                if (!isPremium) {
                    return {
                        allowed: false,
                        reason: 'هذا الأمر يتطلب اشتراك البريميوم'
                    };
                }
            }

            return { allowed: true };
        } catch (error) {
            console.error('❌ خطأ في فحص صلاحيات الأمر:', error);
            return {
                allowed: false,
                reason: 'حدث خطأ في فحص الصلاحيات'
            };
        }
    }

    // فحص البريميوم
    async checkPremium(user, guild = null) {
        try {
            // فحص بريميوم المستخدم
            const userData = await User.findOne({ userId: user.id });
            if (userData?.premium?.active && userData.premium.endDate > new Date()) {
                return true;
            }

            // فحص بريميوم السيرفر
            if (guild) {
                const guildData = await GuildSettings.findOne({ guildId: guild.id });
                if (guildData?.premium?.enabled && guildData.premium.expiresAt > new Date()) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('❌ خطأ في فحص البريميوم:', error);
            return false;
        }
    }

    // فحص صلاحيات البوت في السيرفر
    checkBotPermissions(guild, requiredPermissions = []) {
        try {
            const botMember = guild.members.me;
            if (!botMember) {
                return {
                    allowed: false,
                    missing: ['البوت غير موجود في السيرفر']
                };
            }

            const missing = [];
            for (const permission of requiredPermissions) {
                if (!botMember.permissions.has(permission)) {
                    missing.push(permission);
                }
            }

            return {
                allowed: missing.length === 0,
                missing
            };
        } catch (error) {
            console.error('❌ خطأ في فحص صلاحيات البوت:', error);
            return {
                allowed: false,
                missing: ['خطأ في فحص الصلاحيات']
            };
        }
    }

    // الحصول على اسم مستوى الصلاحية
    getLevelName(level) {
        const names = {
            [this.levels.EVERYONE]: 'الجميع',
            [this.levels.MEMBER]: 'عضو',
            [this.levels.MODERATOR]: 'مشرف',
            [this.levels.ADMIN]: 'مدير',
            [this.levels.SERVER_OWNER]: 'مالك السيرفر',
            [this.levels.BOT_ADMIN]: 'مدير البوت'
        };
        return names[level] || 'غير معروف';
    }

    // فحص إذا كان المستخدم يستطيع الوصول للوحة التحكم
    async canAccessDashboard(user) {
        try {
            // مديري البوت يمكنهم الوصول دائماً
            if (this.botAdmins.has(user.id)) {
                return { allowed: true, level: 'BOT_ADMIN' };
            }

            // فحص إذا كان المستخدم مدير في أي سيرفر يحتوي البوت
            // هذا يتطلب فحص جميع السيرفرات، لكن للبساطة سنسمح للجميع
            return { allowed: true, level: 'MEMBER' };
        } catch (error) {
            console.error('❌ خطأ في فحص الوصول للوحة التحكم:', error);
            return { allowed: false, reason: 'خطأ في فحص الصلاحيات' };
        }
    }

    // فحص إذا كان المستخدم يستطيع إدارة سيرفر معين
    async canManageGuild(user, guildId) {
        try {
            // مديري البوت يمكنهم إدارة أي سيرفر
            if (this.botAdmins.has(user.id)) {
                return true;
            }

            // فحص إذا كان المستخدم مدير في السيرفر
            const guildData = await GuildSettings.findOne({ guildId });
            if (!guildData) {
                return false;
            }

            // فحص مالك السيرفر
            if (guildData.ownerId === user.id) {
                return true;
            }

            // يمكن إضافة فحص إضافي للمديرين المخولين
            return false;
        } catch (error) {
            console.error('❌ خطأ في فحص إدارة السيرفر:', error);
            return false;
        }
    }
}

module.exports = new PermissionManager();
