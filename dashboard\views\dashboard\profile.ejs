<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64"
                 alt="CS Bot"
                 class="rounded-circle me-2"
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-user"></i> CS Bot - الملف الشخصي</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><i class="fas fa-user"></i> الملف الشخصي</h1>

            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white text-center">
                            <h5 class="mb-0"><i class="fas fa-id-card"></i> معلومات الحساب</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>" class="rounded-circle mb-3" width="120" height="120">
                            <h4><%= user.username %></h4>
                            <p class="text-muted">ID: <%= user.id %></p>

                            <% if (userData && userData.premium && userData.premium.enabled) { %>
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-crown"></i> Premium Member
                                </span>
                            <% } %>
                        </div>
                    </div>
                </div>

                <div class="col-md-8 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> الإحصائيات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><i class="fas fa-server"></i> السيرفرات المدارة:</span>
                                        <strong><%= user.guilds ? user.guilds.filter(g => (g.permissions & 0x20) === 0x20).length : 0 %></strong>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><i class="fas fa-calendar"></i> تاريخ الانضمام:</span>
                                        <strong><%= userData ? new Date(userData.createdAt).toLocaleDateString('ar-SA') : 'غير محدد' %></strong>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><i class="fas fa-clock"></i> آخر زيارة:</span>
                                        <strong><%= userData ? new Date(userData.lastSeen).toLocaleDateString('ar-SA') : 'الآن' %></strong>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span><i class="fas fa-globe"></i> اللغة المفضلة:</span>
                                        <strong><%= userData && userData.language === 'en' ? 'English' : 'العربية' %></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <% if (userData && userData.premium && userData.premium.enabled) { %>
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-crown"></i> معلومات البريميوم</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>نوع الاشتراك:</strong>
                            <span class="badge bg-success"><%= userData.premium.tier === 'pro' ? 'Pro' : 'Basic' %></span>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ الانتهاء:</strong>
                            <%= userData.premium.expiresAt ? new Date(userData.premium.expiresAt).toLocaleDateString('ar-SA') : 'غير محدد' %>
                        </div>
                        <div class="col-md-4">
                            <strong>الحالة:</strong>
                            <span class="badge bg-success">نشط</span>
                        </div>
                    </div>
                </div>
            </div>
            <% } %>

            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> إعدادات الحساب</h5>
                </div>
                <div class="card-body">
                    <form id="profileSettingsForm">
                        <div class="mb-3">
                            <label for="language" class="form-label">اللغة المفضلة</label>
                            <select class="form-select" id="language">
                                <option value="ar" <%= userData && userData.language === 'ar' ? 'selected' : '' %>>العربية</option>
                                <option value="en" <%= userData && userData.language === 'en' ? 'selected' : '' %>>English</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notifications" checked>
                                <label class="form-check-label" for="notifications">
                                    تلقي إشعارات البوت
                                </label>
                            </div>
                        </div>

                        <button type="button" id="saveProfileBtn" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('saveProfileBtn').addEventListener('click', function() {
        const language = document.getElementById('language').value;
        const notifications = document.getElementById('notifications').checked;

        // إرسال البيانات إلى الخادم
        fetch('/api/profile/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                language,
                notifications
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ الإعدادات بنجاح!');
            } else {
                alert('حدث خطأ أثناء حفظ الإعدادات: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حفظ الإعدادات');
        });
    });
});
</script>

<!-- Footer -->
<footer class="bg-dark text-white py-4 mt-5">
    <div class="container text-center">
        <p>&copy; 2024 CS Bot. جميع الحقوق محفوظة.</p>
    </div>
</footer>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
