/**
 * مدير الـ Webhooks - CS Bot
 * إرسال الإشعارات المهمة للمطور
 */

const { WebhookClient, EmbedBuilder } = require('discord.js');
const config = require('../config');

class WebhookManager {
    constructor() {
        if (config.webhook.enabled && config.webhook.url) {
            try {
                this.webhook = new WebhookClient({ url: config.webhook.url });
                console.log('✅ تم تشغيل نظام الـ Webhook');
            } catch (error) {
                console.error('❌ خطأ في إعداد الـ Webhook:', error);
                this.webhook = null;
            }
        } else {
            this.webhook = null;
            console.log('⚠️ الـ Webhook معطل أو غير مكون');
        }
    }

    // إرسال إشعار عام
    async sendNotification(title, description, color = '#0099ff', fields = []) {
        if (!this.webhook) return false;

        try {
            const embed = new EmbedBuilder()
                .setTitle(title)
                .setDescription(description)
                .setColor(color)
                .setTimestamp()
                .setFooter({ text: 'CS Bot Notification System' });

            if (fields.length > 0) {
                embed.addFields(fields);
            }

            await this.webhook.send({ embeds: [embed] });
            return true;
        } catch (error) {
            console.error('❌ خطأ في إرسال الـ Webhook:', error);
            return false;
        }
    }

    // إشعار بدء تشغيل البوت
    async sendStartupNotification(botUser, guilds, users) {
        const embed = new EmbedBuilder()
            .setTitle('🚀 تم تشغيل البوت')
            .setDescription(`تم تشغيل **${botUser.username}** بنجاح!`)
            .addFields([
                {
                    name: '🏠 السيرفرات',
                    value: `${guilds} سيرفر`,
                    inline: true
                },
                {
                    name: '👥 المستخدمين',
                    value: `${users} مستخدم`,
                    inline: true
                },
                {
                    name: '⏰ الوقت',
                    value: new Date().toLocaleString('ar-SA'),
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(botUser.displayAvatarURL())
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار انضمام لسيرفر جديد
    async sendGuildJoinNotification(guild) {
        const embed = new EmbedBuilder()
            .setTitle('🎉 انضمام لسيرفر جديد')
            .setDescription(`تم إضافة البوت لسيرفر جديد!`)
            .addFields([
                {
                    name: '📛 اسم السيرفر',
                    value: guild.name,
                    inline: true
                },
                {
                    name: '🆔 معرف السيرفر',
                    value: guild.id,
                    inline: true
                },
                {
                    name: '👥 عدد الأعضاء',
                    value: `${guild.memberCount} عضو`,
                    inline: true
                },
                {
                    name: '👑 المالك',
                    value: `<@${guild.ownerId}>`,
                    inline: true
                },
                {
                    name: '📅 تاريخ الإنشاء',
                    value: guild.createdAt.toLocaleDateString('ar-SA'),
                    inline: true
                },
                {
                    name: '🌍 المنطقة',
                    value: guild.preferredLocale || 'غير محدد',
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(guild.iconURL() || null)
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار مغادرة سيرفر
    async sendGuildLeaveNotification(guild) {
        const embed = new EmbedBuilder()
            .setTitle('😢 مغادرة سيرفر')
            .setDescription(`تم إزالة البوت من سيرفر`)
            .addFields([
                {
                    name: '📛 اسم السيرفر',
                    value: guild.name,
                    inline: true
                },
                {
                    name: '🆔 معرف السيرفر',
                    value: guild.id,
                    inline: true
                },
                {
                    name: '👥 عدد الأعضاء',
                    value: `${guild.memberCount} عضو`,
                    inline: true
                }
            ])
            .setColor('#ff0000')
            .setThumbnail(guild.iconURL() || null)
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار خطأ مهم
    async sendErrorNotification(error, context = '') {
        const embed = new EmbedBuilder()
            .setTitle('❌ خطأ مهم')
            .setDescription(`حدث خطأ في البوت`)
            .addFields([
                {
                    name: '📝 رسالة الخطأ',
                    value: `\`\`\`${error.message}\`\`\``,
                    inline: false
                },
                {
                    name: '📍 السياق',
                    value: context || 'غير محدد',
                    inline: true
                },
                {
                    name: '⏰ الوقت',
                    value: new Date().toLocaleString('ar-SA'),
                    inline: true
                }
            ])
            .setColor('#ff0000')
            .setTimestamp();

        if (error.stack) {
            embed.addFields([
                {
                    name: '🔍 تفاصيل الخطأ',
                    value: `\`\`\`${error.stack.substring(0, 1000)}\`\`\``,
                    inline: false
                }
            ]);
        }

        return this.sendEmbed(embed);
    }

    // إشعار استخدام أمر مهم
    async sendCommandUsageNotification(user, command, guild) {
        const embed = new EmbedBuilder()
            .setTitle('⚡ استخدام أمر مهم')
            .setDescription(`تم استخدام أمر مهم`)
            .addFields([
                {
                    name: '👤 المستخدم',
                    value: `${user.username} (${user.id})`,
                    inline: true
                },
                {
                    name: '📝 الأمر',
                    value: command,
                    inline: true
                },
                {
                    name: '🏠 السيرفر',
                    value: guild ? `${guild.name} (${guild.id})` : 'رسالة خاصة',
                    inline: true
                }
            ])
            .setColor('#ffff00')
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار تسجيل دخول لوحة التحكم
    async sendDashboardLoginNotification(user, ip) {
        const embed = new EmbedBuilder()
            .setTitle('🔐 تسجيل دخول لوحة التحكم')
            .setDescription(`تم تسجيل دخول جديد للوحة التحكم`)
            .addFields([
                {
                    name: '👤 المستخدم',
                    value: `${user.username} (${user.id})`,
                    inline: true
                },
                {
                    name: '🌐 عنوان IP',
                    value: ip,
                    inline: true
                },
                {
                    name: '⏰ الوقت',
                    value: new Date().toLocaleString('ar-SA'),
                    inline: true
                }
            ])
            .setColor('#0099ff')
            .setThumbnail(user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png` : null)
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار إحصائيات يومية
    async sendDailyStatsNotification(stats) {
        const embed = new EmbedBuilder()
            .setTitle('📊 إحصائيات يومية')
            .setDescription(`تقرير الإحصائيات اليومية`)
            .addFields([
                {
                    name: '⚡ الأوامر المستخدمة',
                    value: `${stats.commandsUsed || 0}`,
                    inline: true
                },
                {
                    name: '💬 الرسائل المعالجة',
                    value: `${stats.messagesProcessed || 0}`,
                    inline: true
                },
                {
                    name: '👥 المستخدمين النشطين',
                    value: `${stats.activeUsers || 0}`,
                    inline: true
                },
                {
                    name: '🏠 السيرفرات النشطة',
                    value: `${stats.activeGuilds || 0}`,
                    inline: true
                },
                {
                    name: '🆕 مستخدمين جدد',
                    value: `${stats.newUsers || 0}`,
                    inline: true
                },
                {
                    name: '⏱️ وقت التشغيل',
                    value: `${stats.uptime || '0س'}`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار تحديث البوت
    async sendUpdateNotification(version, changes) {
        const embed = new EmbedBuilder()
            .setTitle('🔄 تحديث البوت')
            .setDescription(`تم تحديث البوت للإصدار **${version}**`)
            .addFields([
                {
                    name: '📝 التغييرات',
                    value: changes.substring(0, 1000),
                    inline: false
                },
                {
                    name: '⏰ وقت التحديث',
                    value: new Date().toLocaleString('ar-SA'),
                    inline: true
                }
            ])
            .setColor('#9b59b6')
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إشعار نشاط مشبوه
    async sendSuspiciousActivityNotification(user, activity, details) {
        const embed = new EmbedBuilder()
            .setTitle('⚠️ نشاط مشبوه')
            .setDescription(`تم رصد نشاط مشبوه`)
            .addFields([
                {
                    name: '👤 المستخدم',
                    value: `${user.username} (${user.id})`,
                    inline: true
                },
                {
                    name: '🚨 نوع النشاط',
                    value: activity,
                    inline: true
                },
                {
                    name: '📋 التفاصيل',
                    value: details,
                    inline: false
                }
            ])
            .setColor('#ff6b6b')
            .setThumbnail(user.displayAvatarURL())
            .setTimestamp();

        return this.sendEmbed(embed);
    }

    // إرسال embed مخصص
    async sendEmbed(embed) {
        if (!this.webhook) return false;

        try {
            await this.webhook.send({ embeds: [embed] });
            return true;
        } catch (error) {
            console.error('❌ خطأ في إرسال الـ Webhook:', error);
            return false;
        }
    }

    // إرسال رسالة نصية بسيطة
    async sendMessage(content) {
        if (!this.webhook) return false;

        try {
            await this.webhook.send({ content });
            return true;
        } catch (error) {
            console.error('❌ خطأ في إرسال الـ Webhook:', error);
            return false;
        }
    }

    // فحص حالة الـ Webhook
    async testWebhook() {
        return this.sendNotification(
            '🧪 اختبار الـ Webhook',
            'هذه رسالة اختبار للتأكد من عمل الـ Webhook بشكل صحيح',
            '#00ff00',
            [
                {
                    name: '✅ الحالة',
                    value: 'يعمل بشكل صحيح',
                    inline: true
                },
                {
                    name: '⏰ الوقت',
                    value: new Date().toLocaleString('ar-SA'),
                    inline: true
                }
            ]
        );
    }
}

module.exports = new WebhookManager();
