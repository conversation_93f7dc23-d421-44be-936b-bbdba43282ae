# دليل التثبيت والإعداد - بوت CS

## 📋 المتطلبات

- Node.js (الإصدار 16.9.0 أو أحدث)
- MongoDB (قاعدة بيانات)
- حساب Discord Developer للحصول على توكن البوت

## 🔧 خطوات التثبيت

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd cs-bot
```

### 2. تثبيت الحزم المطلوبة
```bash
npm install
```

### 3. إعداد ملف البيئة
انسخ ملف `.env.example` إلى `.env` وقم بتعبئة البيانات:

```bash
cp .env.example .env
```

ثم قم بتعديل ملف `.env`:
```env
# Bot Configuration
BOT_TOKEN=YOUR_BOT_TOKEN_HERE
CLIENT_ID=YOUR_CLIENT_ID_HERE
CLIENT_SECRET=YOUR_CLIENT_SECRET_HERE

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/csbot

# Dashboard Configuration
PORT=3000
DOMAIN=http://localhost:3000
SESSION_SECRET=your-session-secret-change-this
```

### 4. إنشاء تطبيق Discord

1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. انقر على "New Application"
3. اختر اسماً للتطبيق
4. اذهب إلى تبويب "Bot"
5. انقر على "Add Bot"
6. انسخ التوكن وضعه في `BOT_TOKEN`
7. اذهب إلى تبويب "OAuth2"
8. انسخ `CLIENT_ID` و `CLIENT_SECRET`
9. أضف Redirect URL: `http://localhost:3000/auth/callback`

## 🚀 تشغيل البوت

### تشغيل البوت كاملاً
```bash
npm start
```

### تشغيل لوحة التحكم فقط (للاختبار)
```bash
npm run dashboard
```

### تشغيل في وضع التطوير
```bash
npm run dev
```

### اختبار الإعدادات
```bash
npm test
```

## 🌐 الوصول للوحة التحكم

بعد تشغيل البوت، يمكنك الوصول للوحة التحكم من خلال:
- http://localhost:3000

## 📁 هيكل المشروع

```
cs-bot/
├── config.js              # إعدادات البوت
├── index.js               # الملف الرئيسي الأصلي
├── start.js               # ملف التشغيل المحسن
├── dashboard-only.js      # تشغيل لوحة التحكم فقط
├── test.js               # اختبار الإعدادات
├── models/               # نماذج قاعدة البيانات
│   ├── guild.js         # نموذج السيرفر
│   └── user.js          # نموذج المستخدم
├── dashboard/           # لوحة التحكم
│   ├── app.js          # تطبيق Express
│   ├── routes/         # مسارات لوحة التحكم
│   ├── views/          # قوالب EJS
│   └── public/         # الملفات الثابتة
└── .env                # متغيرات البيئة
```

## 🔧 استكشاف الأخطاء

### البوت لا يعمل
1. تأكد من صحة التوكن في ملف `.env`
2. تأكد من تشغيل MongoDB
3. تحقق من الأخطاء في وحدة التحكم

### لوحة التحكم لا تعمل
1. تأكد من أن المنفذ 3000 غير مستخدم
2. تحقق من إعدادات OAuth2 في Discord
3. تأكد من صحة `CLIENT_ID` و `CLIENT_SECRET`

### مشاكل قاعدة البيانات
1. تأكد من تشغيل MongoDB
2. تحقق من صحة `MONGODB_URI`
3. تأكد من وجود صلاحيات الكتابة

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف `SETUP.md` هذا
2. راجع الأخطاء في وحدة التحكم
3. تواصل مع المطور

## 🔒 الأمان

- لا تشارك ملف `.env` مع أحد
- غير `SESSION_SECRET` إلى قيمة عشوائية قوية
- استخدم HTTPS في الإنتاج
- قم بتحديث الحزم بانتظام
