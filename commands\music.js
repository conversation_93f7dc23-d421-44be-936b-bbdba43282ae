const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, Button<PERSON>uilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'music',
    description: 'نظام الموسيقى المتقدم',
    usage: '!music <play/pause/stop/skip/queue/volume> [رابط/اسم الأغنية]',
    category: 'ترفيه',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            const subCommand = args[0]?.toLowerCase();
            
            if (!subCommand) {
                return this.showMusicPanel(message);
            }
            
            switch (subCommand) {
                case 'play':
                case 'p':
                    return this.playMusic(message, args.slice(1));
                case 'pause':
                    return this.pauseMusic(message);
                case 'resume':
                    return this.resumeMusic(message);
                case 'stop':
                    return this.stopMusic(message);
                case 'skip':
                case 's':
                    return this.skipMusic(message);
                case 'queue':
                case 'q':
                    return this.showQueue(message);
                case 'volume':
                case 'v':
                    return this.setVolume(message, args[1]);
                case 'loop':
                    return this.toggleLoop(message);
                case 'shuffle':
                    return this.shuffleQueue(message);
                case 'nowplaying':
                case 'np':
                    return this.nowPlaying(message);
                default:
                    return this.showMusicPanel(message);
            }
            
        } catch (error) {
            console.error('خطأ في أمر الموسيقى:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ في الموسيقى')
                .setDescription('حدث خطأ أثناء تشغيل الموسيقى')
                .setTimestamp();
                
            return message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async showMusicPanel(message) {
        const embed = new EmbedBuilder()
            .setColor('#9932cc')
            .setTitle('🎵 نظام الموسيقى المتقدم')
            .setDescription('اختر ما تريد فعله:')
            .addFields([
                {
                    name: '🎶 التشغيل',
                    value: '`!music play <رابط/اسم>` - تشغيل أغنية\n`!music pause` - إيقاف مؤقت\n`!music resume` - استكمال التشغيل',
                    inline: true
                },
                {
                    name: '⏭️ التحكم',
                    value: '`!music skip` - تخطي الأغنية\n`!music stop` - إيقاف التشغيل\n`!music volume <1-100>` - مستوى الصوت',
                    inline: true
                },
                {
                    name: '📋 القائمة',
                    value: '`!music queue` - عرض القائمة\n`!music loop` - تكرار الأغنية\n`!music shuffle` - خلط القائمة',
                    inline: true
                }
            ])
            .setFooter({ text: 'CS Bot Music System' })
            .setTimestamp();
            
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('music_play')
                    .setLabel('تشغيل')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('▶️'),
                new ButtonBuilder()
                    .setCustomId('music_pause')
                    .setLabel('إيقاف مؤقت')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⏸️'),
                new ButtonBuilder()
                    .setCustomId('music_skip')
                    .setLabel('تخطي')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⏭️'),
                new ButtonBuilder()
                    .setCustomId('music_stop')
                    .setLabel('إيقاف')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('⏹️'),
                new ButtonBuilder()
                    .setCustomId('music_queue')
                    .setLabel('القائمة')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📋')
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    },
    
    async playMusic(message, args) {
        const voiceChannel = message.member.voice.channel;
        
        if (!voiceChannel) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ')
                .setDescription('يجب أن تكون في قناة صوتية لتشغيل الموسيقى!')
                .setTimestamp();
                
            return message.reply({ embeds: [embed] });
        }
        
        if (!args.length) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ')
                .setDescription('يرجى كتابة اسم الأغنية أو رابط!')
                .setTimestamp();
                
            return message.reply({ embeds: [embed] });
        }
        
        const query = args.join(' ');
        
        // محاكاة تشغيل الموسيقى (في التطبيق الحقيقي ستحتاج مكتبة مثل discord-player)
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🎵 جاري البحث...')
            .setDescription(`البحث عن: **${query}**`)
            .setTimestamp();
            
        const searchMsg = await message.reply({ embeds: [embed] });
        
        // محاكاة وقت البحث
        setTimeout(async () => {
            const playEmbed = new EmbedBuilder()
                .setColor('#9932cc')
                .setTitle('🎶 تم بدء التشغيل')
                .setDescription(`**${query}**`)
                .addFields([
                    { name: '👤 طلب بواسطة', value: message.author.toString(), inline: true },
                    { name: '📍 القناة الصوتية', value: voiceChannel.name, inline: true },
                    { name: '⏱️ المدة', value: '3:45', inline: true }
                ])
                .setThumbnail('https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg')
                .setFooter({ text: 'CS Bot Music' })
                .setTimestamp();
                
            await searchMsg.edit({ embeds: [playEmbed] });
        }, 2000);
    },
    
    async pauseMusic(message) {
        const embed = new EmbedBuilder()
            .setColor('#ffff00')
            .setTitle('⏸️ تم إيقاف التشغيل مؤقتاً')
            .setDescription('تم إيقاف الموسيقى مؤقتاً')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async resumeMusic(message) {
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('▶️ تم استكمال التشغيل')
            .setDescription('تم استكمال تشغيل الموسيقى')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async stopMusic(message) {
        const embed = new EmbedBuilder()
            .setColor('#ff0000')
            .setTitle('⏹️ تم إيقاف التشغيل')
            .setDescription('تم إيقاف الموسيقى وإفراغ القائمة')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async skipMusic(message) {
        const embed = new EmbedBuilder()
            .setColor('#00ffff')
            .setTitle('⏭️ تم تخطي الأغنية')
            .setDescription('تم الانتقال للأغنية التالية')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async showQueue(message) {
        // قائمة وهمية للعرض
        const queue = [
            'Never Gonna Give You Up - Rick Astley',
            'Bohemian Rhapsody - Queen',
            'Imagine - John Lennon',
            'Hotel California - Eagles',
            'Stairway to Heaven - Led Zeppelin'
        ];
        
        const embed = new EmbedBuilder()
            .setColor('#9932cc')
            .setTitle('📋 قائمة التشغيل')
            .setDescription(queue.map((song, index) => 
                `${index === 0 ? '🎵' : `${index}.`} ${song}`
            ).join('\n'))
            .addFields([
                { name: '📊 إجمالي الأغاني', value: queue.length.toString(), inline: true },
                { name: '⏱️ إجمالي المدة', value: '18:32', inline: true },
                { name: '🔄 وضع التكرار', value: 'مُعطل', inline: true }
            ])
            .setFooter({ text: 'استخدم !music play <اسم> لإضافة أغنية' })
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async setVolume(message, volume) {
        if (!volume || isNaN(volume) || volume < 1 || volume > 100) {
            const embed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ')
                .setDescription('يرجى كتابة رقم بين 1 و 100')
                .setTimestamp();
                
            return message.reply({ embeds: [embed] });
        }
        
        const embed = new EmbedBuilder()
            .setColor('#00ff00')
            .setTitle('🔊 تم تغيير مستوى الصوت')
            .setDescription(`مستوى الصوت الآن: **${volume}%**`)
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async toggleLoop(message) {
        const embed = new EmbedBuilder()
            .setColor('#9932cc')
            .setTitle('🔄 وضع التكرار')
            .setDescription('تم تفعيل وضع التكرار للأغنية الحالية')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async shuffleQueue(message) {
        const embed = new EmbedBuilder()
            .setColor('#ff69b4')
            .setTitle('🔀 تم خلط القائمة')
            .setDescription('تم خلط ترتيب الأغاني في القائمة')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    },
    
    async nowPlaying(message) {
        const embed = new EmbedBuilder()
            .setColor('#9932cc')
            .setTitle('🎵 الأغنية الحالية')
            .setDescription('**Never Gonna Give You Up - Rick Astley**')
            .addFields([
                { name: '⏱️ التقدم', value: '1:23 / 3:45', inline: true },
                { name: '🔊 مستوى الصوت', value: '75%', inline: true },
                { name: '👤 طلب بواسطة', value: message.author.toString(), inline: true }
            ])
            .setThumbnail('https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg')
            .setTimestamp();
            
        return message.reply({ embeds: [embed] });
    }
};
