/**
 * أمر الإحصائيات الأسطوري
 */

module.exports = {
    name: 'stats',
    description: 'عرض إحصائيات البوت الأسطورية',
    usage: '!stats',
    category: 'general',
    
    async execute(message, args, client) {
        try {
            // حساب وقت التشغيل
            function formatUptime(ms) {
                const seconds = Math.floor(ms / 1000);
                const minutes = Math.floor(seconds / 60);
                const hours = Math.floor(minutes / 60);
                const days = Math.floor(hours / 24);
                
                if (days > 0) return `${days} يوم، ${hours % 24} ساعة`;
                if (hours > 0) return `${hours} ساعة، ${minutes % 60} دقيقة`;
                if (minutes > 0) return `${minutes} دقيقة، ${seconds % 60} ثانية`;
                return `${seconds} ثانية`;
            }

            // حساب استخدام الذاكرة
            const memoryUsage = process.memoryUsage();
            const memoryUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024);

            const embed = {
                color: 0x00FF00,
                title: '📊 إحصائيات CS Bot الأسطوري',
                thumbnail: {
                    url: client.user.displayAvatarURL()
                },
                fields: [
                    {
                        name: '🎮 السيرفرات',
                        value: `\`${client.guilds.cache.size}\``,
                        inline: true
                    },
                    {
                        name: '👥 المستخدمين',
                        value: `\`${client.users.cache.size}\``,
                        inline: true
                    },
                    {
                        name: '📝 القنوات',
                        value: `\`${client.channels.cache.size}\``,
                        inline: true
                    },
                    {
                        name: '⚡ الأوامر المنفذة',
                        value: `\`${client.stats.commandsExecuted}\``,
                        inline: true
                    },
                    {
                        name: '💬 الرسائل المعالجة',
                        value: `\`${client.stats.messagesProcessed}\``,
                        inline: true
                    },
                    {
                        name: '👑 أعضاء البريميوم',
                        value: `\`${client.stats.premiumUsers}\``,
                        inline: true
                    },
                    {
                        name: '⏰ وقت التشغيل',
                        value: `\`${formatUptime(Date.now() - client.stats.startTime)}\``,
                        inline: false
                    },
                    {
                        name: '🧠 استخدام الذاكرة',
                        value: `\`${memoryUsed} MB\``,
                        inline: true
                    },
                    {
                        name: '🔧 إصدار Node.js',
                        value: `\`${process.version}\``,
                        inline: true
                    },
                    {
                        name: '📡 Ping',
                        value: `\`${Math.round(client.ws.ping)}ms\``,
                        inline: true
                    }
                ],
                footer: {
                    text: `CS Bot الأسطوري | تم التشغيل في ${new Date(client.stats.startTime).toLocaleString('ar-SA')}`,
                    icon_url: client.user.displayAvatarURL()
                },
                timestamp: new Date()
            };

            // إضافة معلومات إضافية للمطور
            if (message.author.id === client.config.owner.id) {
                embed.fields.push({
                    name: '🔐 معلومات المطور',
                    value: `**الأوامر المحملة:** \`${client.commands.size}\`\n**Slash Commands:** \`${client.slashCommands.size}\`\n**المعرف:** \`${client.user.id}\``,
                    inline: false
                });
            }

            await message.reply({ embeds: [embed] });

        } catch (error) {
            console.error('خطأ في أمر stats:', error);
            message.reply('❌ حدث خطأ أثناء جلب الإحصائيات!');
        }
    }
};
