/**
 * مدير القنوات - CS Bot
 * إدارة وتنظيم قنوات السيرفرات
 */

const { ChannelType } = require('discord.js');

class ChannelManager {
    constructor() {
        this.channelCache = new Map();
        this.lastUpdate = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
        
        console.log('📺 تم تشغيل مدير القنوات');
    }

    // الحصول على قنوات السيرفر مع التخزين المؤقت
    async getGuildChannels(guild, forceRefresh = false) {
        const guildId = guild.id;
        const now = Date.now();
        
        // فحص التخزين المؤقت
        if (!forceRefresh && this.channelCache.has(guildId)) {
            const lastUpdate = this.lastUpdate.get(guildId);
            if (lastUpdate && (now - lastUpdate) < this.cacheTimeout) {
                return this.channelCache.get(guildId);
            }
        }

        try {
            // جلب القنوات من Discord
            const channels = await guild.channels.fetch();
            
            // تصنيف القنوات
            const categorizedChannels = {
                text: [],
                voice: [],
                category: [],
                forum: [],
                announcement: [],
                stage: [],
                thread: []
            };

            channels.forEach(channel => {
                if (!channel) return;

                const channelData = {
                    id: channel.id,
                    name: channel.name,
                    type: channel.type,
                    position: channel.position || 0,
                    parentId: channel.parentId,
                    topic: channel.topic || null,
                    nsfw: channel.nsfw || false,
                    permissions: this.getChannelPermissions(channel),
                    memberCount: channel.members?.size || 0,
                    createdAt: channel.createdAt
                };

                switch (channel.type) {
                    case ChannelType.GuildText:
                        categorizedChannels.text.push(channelData);
                        break;
                    case ChannelType.GuildVoice:
                        channelData.userLimit = channel.userLimit;
                        channelData.bitrate = channel.bitrate;
                        categorizedChannels.voice.push(channelData);
                        break;
                    case ChannelType.GuildCategory:
                        categorizedChannels.category.push(channelData);
                        break;
                    case ChannelType.GuildForum:
                        categorizedChannels.forum.push(channelData);
                        break;
                    case ChannelType.GuildAnnouncement:
                        categorizedChannels.announcement.push(channelData);
                        break;
                    case ChannelType.GuildStageVoice:
                        categorizedChannels.stage.push(channelData);
                        break;
                    case ChannelType.PublicThread:
                    case ChannelType.PrivateThread:
                        categorizedChannels.thread.push(channelData);
                        break;
                }
            });

            // ترتيب القنوات حسب الموقع
            Object.keys(categorizedChannels).forEach(type => {
                categorizedChannels[type].sort((a, b) => a.position - b.position);
            });

            // تحديث التخزين المؤقت
            this.channelCache.set(guildId, categorizedChannels);
            this.lastUpdate.set(guildId, now);

            return categorizedChannels;
        } catch (error) {
            console.error(`❌ خطأ في جلب قنوات السيرفر ${guildId}:`, error);
            return this.getEmptyChannelStructure();
        }
    }

    // الحصول على قنوات نصية فقط
    async getTextChannels(guild, forceRefresh = false) {
        const channels = await this.getGuildChannels(guild, forceRefresh);
        return channels.text || [];
    }

    // الحصول على قنوات صوتية فقط
    async getVoiceChannels(guild, forceRefresh = false) {
        const channels = await this.getGuildChannels(guild, forceRefresh);
        return channels.voice || [];
    }

    // الحصول على الفئات
    async getCategories(guild, forceRefresh = false) {
        const channels = await this.getGuildChannels(guild, forceRefresh);
        return channels.category || [];
    }

    // البحث عن قناة بالاسم
    async findChannelByName(guild, channelName, type = 'text') {
        const channels = await this.getGuildChannels(guild);
        const channelList = channels[type] || [];
        
        return channelList.find(channel => 
            channel.name.toLowerCase().includes(channelName.toLowerCase())
        );
    }

    // البحث عن قناة بالمعرف
    async findChannelById(guild, channelId) {
        const channels = await this.getGuildChannels(guild);
        
        for (const type of Object.keys(channels)) {
            const channel = channels[type].find(ch => ch.id === channelId);
            if (channel) return channel;
        }
        
        return null;
    }

    // الحصول على صلاحيات القناة
    getChannelPermissions(channel) {
        try {
            const permissions = {
                viewChannel: false,
                sendMessages: false,
                readMessageHistory: false,
                manageMessages: false,
                manageChannel: false,
                connect: false,
                speak: false
            };

            // فحص الصلاحيات للبوت
            const botMember = channel.guild.members.me;
            if (!botMember) return permissions;

            const botPermissions = channel.permissionsFor(botMember);
            if (!botPermissions) return permissions;

            permissions.viewChannel = botPermissions.has('ViewChannel');
            permissions.sendMessages = botPermissions.has('SendMessages');
            permissions.readMessageHistory = botPermissions.has('ReadMessageHistory');
            permissions.manageMessages = botPermissions.has('ManageMessages');
            permissions.manageChannel = botPermissions.has('ManageChannels');
            permissions.connect = botPermissions.has('Connect');
            permissions.speak = botPermissions.has('Speak');

            return permissions;
        } catch (error) {
            console.error('خطأ في فحص صلاحيات القناة:', error);
            return this.getEmptyPermissions();
        }
    }

    // الحصول على إحصائيات القنوات
    async getChannelStats(guild) {
        const channels = await this.getGuildChannels(guild);
        
        return {
            total: Object.values(channels).reduce((sum, arr) => sum + arr.length, 0),
            text: channels.text.length,
            voice: channels.voice.length,
            category: channels.category.length,
            forum: channels.forum.length,
            announcement: channels.announcement.length,
            stage: channels.stage.length,
            thread: channels.thread.length,
            accessible: this.countAccessibleChannels(channels),
            restricted: this.countRestrictedChannels(channels)
        };
    }

    // عد القنوات القابلة للوصول
    countAccessibleChannels(channels) {
        let count = 0;
        Object.values(channels).forEach(channelList => {
            channelList.forEach(channel => {
                if (channel.permissions.viewChannel) count++;
            });
        });
        return count;
    }

    // عد القنوات المقيدة
    countRestrictedChannels(channels) {
        let count = 0;
        Object.values(channels).forEach(channelList => {
            channelList.forEach(channel => {
                if (!channel.permissions.viewChannel) count++;
            });
        });
        return count;
    }

    // تنظيف التخزين المؤقت
    clearCache(guildId = null) {
        if (guildId) {
            this.channelCache.delete(guildId);
            this.lastUpdate.delete(guildId);
        } else {
            this.channelCache.clear();
            this.lastUpdate.clear();
        }
    }

    // تحديث قناة واحدة في التخزين المؤقت
    updateChannelInCache(guildId, channelData) {
        if (!this.channelCache.has(guildId)) return;

        const channels = this.channelCache.get(guildId);
        const channelType = this.getChannelTypeString(channelData.type);
        
        if (channels[channelType]) {
            const index = channels[channelType].findIndex(ch => ch.id === channelData.id);
            if (index !== -1) {
                channels[channelType][index] = channelData;
            } else {
                channels[channelType].push(channelData);
                channels[channelType].sort((a, b) => a.position - b.position);
            }
        }
    }

    // حذف قناة من التخزين المؤقت
    removeChannelFromCache(guildId, channelId) {
        if (!this.channelCache.has(guildId)) return;

        const channels = this.channelCache.get(guildId);
        
        Object.keys(channels).forEach(type => {
            channels[type] = channels[type].filter(ch => ch.id !== channelId);
        });
    }

    // تحويل نوع القناة لنص
    getChannelTypeString(type) {
        switch (type) {
            case ChannelType.GuildText:
                return 'text';
            case ChannelType.GuildVoice:
                return 'voice';
            case ChannelType.GuildCategory:
                return 'category';
            case ChannelType.GuildForum:
                return 'forum';
            case ChannelType.GuildAnnouncement:
                return 'announcement';
            case ChannelType.GuildStageVoice:
                return 'stage';
            case ChannelType.PublicThread:
            case ChannelType.PrivateThread:
                return 'thread';
            default:
                return 'unknown';
        }
    }

    // هيكل قنوات فارغ
    getEmptyChannelStructure() {
        return {
            text: [],
            voice: [],
            category: [],
            forum: [],
            announcement: [],
            stage: [],
            thread: []
        };
    }

    // صلاحيات فارغة
    getEmptyPermissions() {
        return {
            viewChannel: false,
            sendMessages: false,
            readMessageHistory: false,
            manageMessages: false,
            manageChannel: false,
            connect: false,
            speak: false
        };
    }

    // تصدير القنوات لـ JSON
    async exportChannels(guild) {
        const channels = await this.getGuildChannels(guild, true);
        
        return {
            guildId: guild.id,
            guildName: guild.name,
            exportedAt: new Date().toISOString(),
            channels: channels,
            stats: await this.getChannelStats(guild)
        };
    }

    // فحص صحة القنوات
    async validateChannels(guild) {
        const issues = [];
        const channels = await this.getGuildChannels(guild, true);

        // فحص القنوات النصية
        channels.text.forEach(channel => {
            if (!channel.permissions.viewChannel) {
                issues.push({
                    type: 'permission',
                    channel: channel.name,
                    issue: 'لا يمكن للبوت رؤية القناة'
                });
            }
            if (!channel.permissions.sendMessages) {
                issues.push({
                    type: 'permission',
                    channel: channel.name,
                    issue: 'لا يمكن للبوت إرسال رسائل'
                });
            }
        });

        // فحص القنوات الصوتية
        channels.voice.forEach(channel => {
            if (!channel.permissions.connect) {
                issues.push({
                    type: 'permission',
                    channel: channel.name,
                    issue: 'لا يمكن للبوت الاتصال بالقناة الصوتية'
                });
            }
        });

        return issues;
    }

    // إحصائيات الاستخدام
    getUsageStats() {
        return {
            cachedGuilds: this.channelCache.size,
            cacheHits: this.cacheHits || 0,
            cacheMisses: this.cacheMisses || 0,
            lastCleanup: this.lastCleanup || null
        };
    }

    // تنظيف دوري للتخزين المؤقت
    startPeriodicCleanup() {
        setInterval(() => {
            const now = Date.now();
            const expiredGuilds = [];

            this.lastUpdate.forEach((timestamp, guildId) => {
                if (now - timestamp > this.cacheTimeout) {
                    expiredGuilds.push(guildId);
                }
            });

            expiredGuilds.forEach(guildId => {
                this.channelCache.delete(guildId);
                this.lastUpdate.delete(guildId);
            });

            if (expiredGuilds.length > 0) {
                console.log(`🧹 تم تنظيف ${expiredGuilds.length} سيرفر من تخزين القنوات المؤقت`);
            }

            this.lastCleanup = now;
        }, 10 * 60 * 1000); // كل 10 دقائق
    }
}

module.exports = new ChannelManager();
