/**
 * CS Bot - الملف الرئيسي
 *
 * هذا الملف مسؤول عن تشغيل البوت ولوحة التحكم
 */

// استيراد المكتبات اللازمة
const { Client, GatewayIntentBits, Partials, Collection } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// استيراد الإعدادات
const config = require('./config');

// إنشاء عميل Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildPresences,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessageReactions
    ],
    partials: [
        Partials.Message,
        Partials.Channel,
        Partials.Reaction,
        Partials.GuildMember,
        Partials.User
    ]
});

// تهيئة المجموعات
client.commands = new Collection();
client.slashCommands = new Collection();
client.events = new Collection();
client.config = config;

// الاتصال بقاعدة البيانات
mongoose.connect(config.mongoURI)
    .then(() => console.log('✅ تم الاتصال بقاعدة البيانات MongoDB'))
    .catch(err => console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err));

// تحميل لوحة التحكم
const dashboard = require('./dashboard/app');
dashboard.locals.config = config;
dashboard.locals.client = client;

// تشغيل لوحة التحكم
dashboard.listen(config.dashboard.port, () => {
    console.log(`✅ تم تشغيل لوحة التحكم على المنفذ ${config.dashboard.port}`);
    console.log(`🌐 يمكنك الوصول إلى لوحة التحكم من خلال: ${config.dashboard.domain}`);
});

// عند جاهزية البوت
client.on('ready', () => {
    console.log(`✅ البوت ${client.user.tag} متصل وجاهز!`);

    // تعيين حالة البوت
    client.user.setActivity(`${config.prefix}help | ${client.guilds.cache.size} سيرفر`, { type: 3 }); // 3 = WATCHING
    client.user.setStatus('online');

    // تحديث إحصائيات البوت
    setInterval(() => {
        client.user.setActivity(`${config.prefix}help | ${client.guilds.cache.size} سيرفر`, { type: 3 });
    }, 60000); // تحديث كل دقيقة
});

// تسجيل الدخول إلى Discord
client.login(config.token)
    .then(() => console.log('✅ تم تسجيل دخول البوت بنجاح'))
    .catch(err => console.error('❌ فشل تسجيل دخول البوت:', err));

// التعامل مع أخطاء العملية
process.on('unhandledRejection', error => {
    console.error('خطأ غير معالج:', error);
});

// تصدير العميل للاستخدام في ملفات أخرى
module.exports = client;