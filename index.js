/**
 * CS Bot - البوت الأسطوري 🚀
 *
 * بوت ديسكورد شامل مع لوحة تحكم ويب ومميزات أسطورية
 */

console.log('🚀 بدء تشغيل CS Bot الأسطوري...');

// تحميل المتغيرات البيئية
require('dotenv').config();

// استيراد المكتبات اللازمة
const { Client, GatewayIntentBits, Partials, Collection, REST, Routes } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// استيراد الإعدادات
const config = require('./config');

// إنشاء عميل Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildPresences,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessageReactions
    ],
    partials: [
        Partials.Message,
        Partials.Channel,
        Partials.Reaction,
        Partials.GuildMember,
        Partials.User
    ]
});

// تهيئة المجموعات
client.commands = new Collection();
client.slashCommands = new Collection();
client.events = new Collection();
client.config = config;

// إحصائيات البوت الأسطورية
client.stats = {
    commandsExecuted: 0,
    startTime: Date.now(),
    messagesProcessed: 0,
    premiumUsers: 0
};

// تحميل الأوامر العادية
function loadCommands() {
    console.log('📂 تحميل الأوامر العادية...');

    if (!fs.existsSync('./commands')) {
        console.log('⚠️ مجلد commands غير موجود، سيتم إنشاؤه...');
        fs.mkdirSync('./commands', { recursive: true });
        return;
    }

    const commandItems = fs.readdirSync('./commands');

    for (const item of commandItems) {
        const itemPath = `./commands/${item}`;
        const stat = fs.statSync(itemPath);

        if (stat.isFile() && item.endsWith('.js')) {
            // ملف أمر مباشر
            try {
                const command = require(itemPath);
                client.commands.set(command.name, command);
                console.log(`✅ تم تحميل الأمر: ${command.name.padEnd(8)}`);
            } catch (error) {
                console.error(`❌ خطأ في تحميل الأمر ${item}:`, error);
            }
        } else if (stat.isDirectory()) {
            // مجلد يحتوي على أوامر
            const commandFiles = fs.readdirSync(itemPath).filter(file => file.endsWith('.js'));

            for (const file of commandFiles) {
                try {
                    const command = require(`${itemPath}/${file}`);
                    client.commands.set(command.name, command);
                    console.log(`✅ تم تحميل الأمر: ${command.name.padEnd(8)}`);
                } catch (error) {
                    console.error(`❌ خطأ في تحميل الأمر ${file}:`, error);
                }
            }
        }
    }

    console.log(`🎉 تم تحميل ${client.commands.size} أمر بنجاح!`);
}

// تحميل Slash Commands
async function loadSlashCommands() {
    console.log('⚡ تحميل Slash Commands...');

    if (!fs.existsSync('./slashCommands')) {
        console.log('⚠️ مجلد slashCommands غير موجود، سيتم إنشاؤه...');
        fs.mkdirSync('./slashCommands', { recursive: true });
        return;
    }

    const slashCommandFolders = fs.readdirSync('./slashCommands');
    const commands = [];

    for (const folder of slashCommandFolders) {
        const commandFiles = fs.readdirSync(`./slashCommands/${folder}`).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            try {
                const command = require(`./slashCommands/${folder}/${file}`);
                client.slashCommands.set(command.data.name, command);
                commands.push(command.data.toJSON());
                console.log(`✅ تم تحميل Slash Command: ${command.data.name}`);
            } catch (error) {
                console.error(`❌ خطأ في تحميل Slash Command ${file}:`, error);
            }
        }
    }

    // تسجيل Slash Commands
    if (commands.length > 0) {
        try {
            const rest = new REST({ version: '10' }).setToken(config.token);

            console.log('🔄 بدء تسجيل Slash Commands...');

            await rest.put(
                Routes.applicationCommands(config.clientId),
                { body: commands }
            );

            console.log(`🎉 تم تسجيل ${commands.length} Slash Command بنجاح!`);
        } catch (error) {
            console.error('❌ خطأ في تسجيل Slash Commands:', error);
        }
    }
}

// تحميل الأوامر
loadCommands();

// تحميل Slash Commands عند جاهزية البوت
client.once('ready', async () => {
    await loadSlashCommands();
});

// الاتصال بقاعدة البيانات
mongoose.connect(config.mongoURI)
    .then(() => console.log('✅ تم الاتصال بقاعدة البيانات MongoDB'))
    .catch(err => console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err));

// تحميل لوحة التحكم
const dashboard = require('./dashboard/app');
dashboard.locals.config = config;
dashboard.locals.client = client;

// تشغيل لوحة التحكم
dashboard.listen(config.dashboard.port, () => {
    console.log(`✅ تم تشغيل لوحة التحكم على المنفذ ${config.dashboard.port}`);
    console.log(`🌐 يمكنك الوصول إلى لوحة التحكم من خلال: ${config.dashboard.domain}`);
});

// معالج الرسائل للأوامر العادية
client.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    if (!message.content.startsWith(config.prefix)) return;

    client.stats.messagesProcessed++;

    const args = message.content.slice(config.prefix.length).trim().split(/ +/);
    const commandName = args.shift().toLowerCase();

    const command = client.commands.get(commandName);
    if (!command) return;

    try {
        // التحقق من صلاحيات المالك
        if (command.ownerOnly && message.author.id !== config.owner.id) {
            return message.reply('❌ هذا الأمر مخصص للمطور فقط!');
        }

        await command.execute(message, args, client);
        client.stats.commandsExecuted++;

        // تحديث إحصائيات قاعدة البيانات المؤقتة
        const memoryDB = require('./utils/memoryDB');
        memoryDB.incrementCommand();

        console.log(`🎯 تم تنفيذ الأمر ${commandName} بواسطة ${message.author.tag}`);
    } catch (error) {
        console.error(`❌ خطأ في تنفيذ الأمر ${commandName}:`, error);
        message.reply('❌ حدث خطأ أثناء تنفيذ الأمر!');
    }
});

// معالج Slash Commands
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isChatInputCommand()) return;

    const command = client.slashCommands.get(interaction.commandName);
    if (!command) return;

    try {
        await command.execute(interaction);
        client.stats.commandsExecuted++;

        console.log(`⚡ تم تنفيذ Slash Command ${interaction.commandName} بواسطة ${interaction.user.tag}`);
    } catch (error) {
        console.error(`❌ خطأ في تنفيذ Slash Command ${interaction.commandName}:`, error);

        const errorMessage = '❌ حدث خطأ أثناء تنفيذ الأمر!';

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: errorMessage, ephemeral: true });
        } else {
            await interaction.reply({ content: errorMessage, ephemeral: true });
        }
    }
});

// عند جاهزية البوت
client.on('ready', async () => {
    console.log(`✅ البوت ${client.user.tag} متصل وجاهز!`);
    console.log(`🎮 متصل بـ ${client.guilds.cache.size} سيرفر`);
    console.log(`👥 يخدم ${client.users.cache.size} مستخدم`);

    // تحميل Slash Commands
    await loadSlashCommands();

    // تعيين حالة البوت الأسطورية
    const activities = [
        `${config.prefix}help | ${client.guilds.cache.size} سيرفر`,
        `🚀 CS Bot الأسطوري`,
        `👑 نظام البريميوم متاح`,
        `⚡ Slash Commands جاهزة`,
        `🌟 لوحة تحكم متقدمة`
    ];

    let activityIndex = 0;

    const updateActivity = () => {
        client.user.setActivity(activities[activityIndex], { type: 3 }); // 3 = WATCHING
        activityIndex = (activityIndex + 1) % activities.length;
    };

    updateActivity();
    client.user.setStatus('online');

    // تحديث إحصائيات البوت كل دقيقة
    setInterval(updateActivity, 30000); // تحديث كل 30 ثانية
});

// تسجيل الدخول إلى Discord
client.login(config.token)
    .then(() => console.log('✅ تم تسجيل دخول البوت بنجاح'))
    .catch(err => console.error('❌ فشل تسجيل دخول البوت:', err));

// التعامل مع أخطاء العملية
process.on('unhandledRejection', error => {
    console.error('خطأ غير معالج:', error);
});

// تصدير العميل للاستخدام في ملفات أخرى
module.exports = client;