<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السيرفرات - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64" 
                 alt="CS Bot" 
                 class="rounded-circle me-2" 
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-server"></i> CS Bot - إدارة السيرفرات</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-server"></i> إدارة السيرفرات
                    </h2>
                    <p class="card-text">إدارة وعرض جميع سيرفرات البوت</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Server Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-server fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="totalServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي السيرفرات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="totalMembers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي الأعضاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="activeServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفرات نشطة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="premiumServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفرات بريميوم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث عن سيرفر...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="sizeFilter">
                                <option value="">جميع الأحجام</option>
                                <option value="small">صغير (أقل من 100)</option>
                                <option value="medium">متوسط (100-1000)</option>
                                <option value="large">كبير (أكثر من 1000)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="sortBy">
                                <option value="memberCount">عدد الأعضاء</option>
                                <option value="name">الاسم</option>
                                <option value="joinedAt">تاريخ الانضمام</option>
                                <option value="lastActivity">آخر نشاط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadServers()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Servers Grid -->
    <div class="row" id="serversContainer">
        <div class="col-12 text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">جاري تحميل السيرفرات...</p>
        </div>
    </div>

    <!-- Pagination -->
    <nav aria-label="صفحات السيرفرات">
        <ul class="pagination justify-content-center" id="pagination">
            <!-- سيتم إنشاؤها بواسطة JavaScript -->
        </ul>
    </nav>
</div>

<!-- Server Details Modal -->
<div class="modal fade" id="serverModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-server"></i> تفاصيل السيرفر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-danger" onclick="leaveServer()">
                    <i class="fas fa-sign-out-alt"></i> مغادرة السيرفر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let currentPage = 1;
let totalPages = 1;
let currentServerId = null;

// تحميل بيانات السيرفرات
async function loadServers(page = 1) {
    try {
        const searchTerm = document.getElementById('searchInput').value;
        const sizeFilter = document.getElementById('sizeFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        
        const params = new URLSearchParams({
            page,
            search: searchTerm,
            size: sizeFilter,
            sortBy
        });
        
        const response = await fetch(`/api/servers?${params}`);
        const data = await response.json();
        
        if (response.ok) {
            // تحديث الإحصائيات
            document.getElementById('totalServers').textContent = data.stats.total || 0;
            document.getElementById('totalMembers').textContent = data.stats.totalMembers || 0;
            document.getElementById('activeServers').textContent = data.stats.active || 0;
            document.getElementById('premiumServers').textContent = data.stats.premium || 0;
            
            // تحديث السيرفرات
            displayServers(data.servers || []);
            
            // تحديث الصفحات
            currentPage = page;
            totalPages = data.totalPages || 1;
            updatePagination();
            
        } else {
            console.error('خطأ في تحميل السيرفرات:', data.error);
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        // عرض بيانات وهمية للاختبار
        displayDummyServers();
    }
}

// عرض السيرفرات
function displayServers(servers) {
    const container = document.getElementById('serversContainer');
    
    if (servers.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                <h5>لا توجد سيرفرات</h5>
                <p class="text-muted">البوت غير موجود في أي سيرفر</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = servers.map(server => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${server.icon || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                             class="rounded-circle me-3" width="50" height="50">
                        <div>
                            <h6 class="card-title mb-1">${server.name}</h6>
                            <small class="text-muted">${server.id}</small>
                        </div>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="text-primary mb-0">${server.memberCount || 0}</h6>
                                <small class="text-muted">أعضاء</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h6 class="text-success mb-0">${server.channelCount || 0}</h6>
                                <small class="text-muted">قنوات</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h6 class="text-info mb-0">${server.roleCount || 0}</h6>
                            <small class="text-muted">رتب</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        ${server.premium ? '<span class="badge bg-warning me-1"><i class="fas fa-crown"></i> بريميوم</span>' : ''}
                        ${server.verified ? '<span class="badge bg-success me-1"><i class="fas fa-check"></i> موثق</span>' : ''}
                        ${server.partnered ? '<span class="badge bg-primary me-1"><i class="fas fa-handshake"></i> شريك</span>' : ''}
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="viewServer('${server.id}')">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="confirmLeaveServer('${server.id}', '${server.name}')">
                            <i class="fas fa-sign-out-alt"></i> مغادرة السيرفر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// عرض بيانات وهمية للاختبار
function displayDummyServers() {
    const dummyServers = [
        {
            id: '123456789',
            name: 'سيرفر الاختبار الأول',
            icon: null,
            memberCount: 150,
            channelCount: 25,
            roleCount: 12,
            premium: true,
            verified: false,
            partnered: false
        },
        {
            id: '987654321',
            name: 'مجتمع المطورين',
            icon: null,
            memberCount: 500,
            channelCount: 40,
            roleCount: 20,
            premium: false,
            verified: true,
            partnered: false
        }
    ];
    
    // تحديث الإحصائيات
    document.getElementById('totalServers').textContent = '2';
    document.getElementById('totalMembers').textContent = '650';
    document.getElementById('activeServers').textContent = '2';
    document.getElementById('premiumServers').textContent = '1';
    
    displayServers(dummyServers);
}

// تحديث الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    let paginationHTML = '';
    
    // زر السابق
    if (currentPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadServers(${currentPage - 1})">السابق</a>
            </li>
        `;
    }
    
    // أرقام الصفحات
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadServers(${i})">${i}</a>
            </li>
        `;
    }
    
    // زر التالي
    if (currentPage < totalPages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadServers(${currentPage + 1})">التالي</a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHTML;
}

// عرض تفاصيل السيرفر
function viewServer(serverId) {
    currentServerId = serverId;
    
    document.getElementById('serverModalBody').innerHTML = `
        <div class="text-center py-3">
            <i class="fas fa-spinner fa-spin"></i> جاري تحميل التفاصيل...
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('serverModal')).show();
    
    // محاكاة تحميل البيانات
    setTimeout(() => {
        document.getElementById('serverModalBody').innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <img src="https://cdn.discordapp.com/embed/avatars/0.png" 
                         class="rounded-circle mb-3" width="120" height="120">
                    <h5>سيرفر الاختبار</h5>
                    <p class="text-muted">${serverId}</p>
                </div>
                <div class="col-md-8">
                    <h6>معلومات السيرفر</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>عدد الأعضاء:</strong></td>
                            <td>150 عضو</td>
                        </tr>
                        <tr>
                            <td><strong>عدد القنوات:</strong></td>
                            <td>25 قناة</td>
                        </tr>
                        <tr>
                            <td><strong>عدد الرتب:</strong></td>
                            <td>12 رتبة</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الانضمام:</strong></td>
                            <td>${new Date().toLocaleDateString('ar-SA')}</td>
                        </tr>
                        <tr>
                            <td><strong>آخر نشاط:</strong></td>
                            <td>منذ دقائق</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
    }, 1000);
}

// تأكيد مغادرة السيرفر
function confirmLeaveServer(serverId, serverName) {
    if (confirm(`هل أنت متأكد من مغادرة سيرفر "${serverName}"؟`)) {
        leaveServerById(serverId);
    }
}

// مغادرة السيرفر
async function leaveServerById(serverId) {
    try {
        const response = await fetch(`/api/servers/${serverId}/leave`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم مغادرة السيرفر بنجاح!');
            loadServers(currentPage);
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في مغادرة السيرفر');
    }
}

// البحث في الوقت الفعلي
document.getElementById('searchInput').addEventListener('input', () => {
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        loadServers(1);
    }, 500);
});

// تغيير الفلاتر
document.getElementById('sizeFilter').addEventListener('change', () => loadServers(1));
document.getElementById('sortBy').addEventListener('change', () => loadServers(1));

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => loadServers());

// تحديث البيانات كل دقيقة
setInterval(() => loadServers(currentPage), 60000);
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

</body>
</html>
