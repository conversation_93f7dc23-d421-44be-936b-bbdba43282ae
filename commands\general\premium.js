/**
 * أمر فحص البريميوم
 */

const { getUserData } = require('../../models/user');

module.exports = {
    name: 'premium',
    description: 'فحص حالة البريميوم الخاصة بك',
    usage: '!premium [@user]',
    category: 'general',
    
    async execute(message, args, client) {
        try {
            // تحديد المستخدم المراد فحصه
            const targetUser = message.mentions.users.first() || message.author;
            
            // التحقق من الصلاحيات لفحص مستخدم آخر
            if (targetUser.id !== message.author.id && message.author.id !== client.config.owner.id) {
                return message.reply('❌ يمكنك فحص البريميوم الخاص بك فقط!');
            }

            // جلب بيانات المستخدم
            const userData = await getUserData(targetUser.id, targetUser.username, targetUser.discriminator, targetUser.avatar);

            const embed = {
                color: userData.premium?.enabled ? 0xFFD700 : 0x808080,
                title: `👑 حالة البريميوم - ${targetUser.username}`,
                thumbnail: {
                    url: targetUser.displayAvatarURL()
                },
                fields: [
                    {
                        name: '📊 الحالة',
                        value: userData.premium?.enabled ? '✅ **نشط**' : '❌ **غير نشط**',
                        inline: true
                    }
                ],
                footer: {
                    text: 'CS Bot - نظام البريميوم',
                    icon_url: client.user.displayAvatarURL()
                },
                timestamp: new Date()
            };

            if (userData.premium?.enabled) {
                // إضافة معلومات البريميوم
                const expiresAt = new Date(userData.premium.expiresAt);
                const now = new Date();
                const timeLeft = expiresAt - now;
                
                // حساب الوقت المتبقي
                const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

                let timeLeftText = '';
                if (days > 0) timeLeftText += `${days} يوم `;
                if (hours > 0) timeLeftText += `${hours} ساعة `;
                if (minutes > 0) timeLeftText += `${minutes} دقيقة`;
                
                if (timeLeft <= 0) {
                    timeLeftText = 'منتهي الصلاحية';
                    embed.color = 0xFF0000;
                }

                embed.fields.push(
                    {
                        name: '📅 تاريخ الانتهاء',
                        value: `<t:${Math.floor(expiresAt.getTime() / 1000)}:F>`,
                        inline: true
                    },
                    {
                        name: '⏰ الوقت المتبقي',
                        value: `\`${timeLeftText || 'منتهي'}\``,
                        inline: true
                    },
                    {
                        name: '🏆 نوع الاشتراك',
                        value: `\`${userData.premium.tier || 'Premium'}\``,
                        inline: true
                    }
                );

                if (userData.premium.addedBy) {
                    embed.fields.push({
                        name: '👤 تم الإضافة بواسطة',
                        value: `<@${userData.premium.addedBy}>`,
                        inline: true
                    });
                }

                if (userData.premium.addedAt) {
                    embed.fields.push({
                        name: '📅 تاريخ الإضافة',
                        value: `<t:${Math.floor(new Date(userData.premium.addedAt).getTime() / 1000)}:R>`,
                        inline: true
                    });
                }

                // إضافة المميزات
                embed.fields.push({
                    name: '🌟 المميزات المتاحة',
                    value: '• الوصول للوحة التحكم المتقدمة\n• إعدادات حصرية\n• دعم أولوية\n• مميزات خاصة في البوت\n• إمكانية إضافة توكن بوت خاص',
                    inline: false
                });

                // تحذير إذا كان البريميوم سينتهي قريباً
                if (timeLeft > 0 && timeLeft < 7 * 24 * 60 * 60 * 1000) { // أقل من أسبوع
                    embed.fields.push({
                        name: '⚠️ تحذير',
                        value: 'البريميوم الخاص بك سينتهي قريباً! تواصل مع المطور لتجديده.',
                        inline: false
                    });
                }
            } else {
                // معلومات للمستخدمين غير المميزين
                embed.fields.push({
                    name: '💡 كيفية الحصول على البريميوم',
                    value: 'تواصل مع المطور أو زر صفحة البريميوم في لوحة التحكم للحصول على البريميوم!',
                    inline: false
                });

                embed.fields.push({
                    name: '🎁 مميزات البريميوم',
                    value: '• الوصول للوحة التحكم المتقدمة\n• إعدادات حصرية\n• دعم أولوية\n• مميزات خاصة في البوت',
                    inline: false
                });
            }

            await message.reply({ embeds: [embed] });

        } catch (error) {
            console.error('خطأ في أمر premium:', error);
            message.reply('❌ حدث خطأ أثناء فحص البريميوم!');
        }
    }
};
