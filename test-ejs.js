/**
 * اختبار EJS للتأكد من عمل include
 */

const express = require('express');
const path = require('path');
const app = express();

// إعداد EJS
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'dashboard/views'));

// مسار اختبار
app.get('/', (req, res) => {
    console.log('🔄 محاولة عرض الصفحة الرئيسية...');
    
    try {
        res.render('index', {
            user: null,
            config: {
                bot: { name: '<PERSON> Bot' },
                prefix: '!'
            },
            bot: {
                name: '<PERSON> Bot',
                avatar: '/img/logo.png'
            }
        });
    } catch (error) {
        console.error('❌ خطأ في عرض الصفحة:', error);
        res.status(500).send('خطأ في عرض الصفحة: ' + error.message);
    }
});

// تشغيل الخادم
const port = 3001;
app.listen(port, () => {
    console.log(`🧪 خادم اختبار EJS يعمل على المنفذ ${port}`);
    console.log(`🌐 اذهب إلى: http://localhost:${port}`);
    console.log('📝 هذا اختبار لمعرفة سبب مشكلة EJS include');
});
