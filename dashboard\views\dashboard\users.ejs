<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64" 
                 alt="CS Bot" 
                 class="rounded-circle me-2" 
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-users"></i> CS Bot - إدارة المستخدمين</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-users"></i> إدارة المستخدمين
                    </h2>
                    <p class="card-text">إدارة وعرض جميع مستخدمي البوت</p>
                </div>
            </div>
        </div>
    </div>

    <!-- User Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="totalUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي المستخدمين</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="activeUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">نشط اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="premiumUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">مستخدمين بريميوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                    <h4 class="card-title" id="bannedUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">محظورين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث عن مستخدم...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="premium">بريميوم</option>
                                <option value="banned">محظور</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="sortBy">
                                <option value="joinedAt">تاريخ الانضمام</option>
                                <option value="lastActive">آخر نشاط</option>
                                <option value="commandsUsed">الأوامر المستخدمة</option>
                                <option value="username">الاسم</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="loadUsers()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> قائمة المستخدمين
                        <span class="badge bg-light text-dark ms-2" id="userCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>تاريخ الانضمام</th>
                                    <th>آخر نشاط</th>
                                    <th>الأوامر المستخدمة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable">
                                <tr>
                                    <td colspan="6" class="text-center py-3">
                                        <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <nav aria-label="صفحات المستخدمين">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- سيتم إنشاؤها بواسطة JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i> تفاصيل المستخدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-warning" onclick="togglePremium()">
                    <i class="fas fa-crown"></i> تبديل البريميوم
                </button>
                <button type="button" class="btn btn-danger" onclick="toggleBan()">
                    <i class="fas fa-ban"></i> تبديل الحظر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let currentPage = 1;
let totalPages = 1;
let currentUserId = null;

// تحميل بيانات المستخدمين
async function loadUsers(page = 1) {
    try {
        const searchTerm = document.getElementById('searchInput').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const sortBy = document.getElementById('sortBy').value;
        
        const params = new URLSearchParams({
            page,
            search: searchTerm,
            status: statusFilter,
            sortBy
        });
        
        const response = await fetch(`/api/users?${params}`);
        const data = await response.json();
        
        if (response.ok) {
            // تحديث الإحصائيات
            document.getElementById('totalUsers').textContent = data.stats.total || 0;
            document.getElementById('activeUsers').textContent = data.stats.active || 0;
            document.getElementById('premiumUsers').textContent = data.stats.premium || 0;
            document.getElementById('bannedUsers').textContent = data.stats.banned || 0;
            
            // تحديث الجدول
            updateUsersTable(data.users || []);
            
            // تحديث الصفحات
            currentPage = page;
            totalPages = data.totalPages || 1;
            updatePagination();
            
            document.getElementById('userCount').textContent = data.users?.length || 0;
            
        } else {
            console.error('خطأ في تحميل المستخدمين:', data.error);
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

// تحديث جدول المستخدمين
function updateUsersTable(users) {
    const tbody = document.getElementById('usersTable');
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-3">
                    <i class="fas fa-users text-muted"></i>
                    <p class="mb-0 mt-2">لا يوجد مستخدمين</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${user.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                         class="rounded-circle me-2" width="32" height="32">
                    <div>
                        <strong>${user.username}</strong>
                        <br><small class="text-muted">${user.id}</small>
                    </div>
                </div>
            </td>
            <td>${new Date(user.joinedAt).toLocaleDateString('ar-SA')}</td>
            <td>${user.lastActive ? new Date(user.lastActive).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
            <td>
                <span class="badge bg-info">${user.commandsUsed || 0}</span>
            </td>
            <td>
                <div>
                    ${user.premium?.active ? '<span class="badge bg-warning me-1"><i class="fas fa-crown"></i> بريميوم</span>' : ''}
                    ${user.banned ? '<span class="badge bg-danger me-1"><i class="fas fa-ban"></i> محظور</span>' : '<span class="badge bg-success me-1"><i class="fas fa-check"></i> نشط</span>'}
                </div>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewUser('${user.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-outline-warning" onclick="toggleUserPremium('${user.id}')">
                        <i class="fas fa-crown"></i> بريميوم
                    </button>
                    <button class="btn btn-outline-danger" onclick="toggleUserBan('${user.id}')">
                        <i class="fas fa-ban"></i> حظر
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تحديث الصفحات
function updatePagination() {
    const pagination = document.getElementById('pagination');
    let paginationHTML = '';
    
    // زر السابق
    if (currentPage > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${currentPage - 1})">السابق</a>
            </li>
        `;
    }
    
    // أرقام الصفحات
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            </li>
        `;
    }
    
    // زر التالي
    if (currentPage < totalPages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${currentPage + 1})">التالي</a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHTML;
}

// عرض تفاصيل المستخدم
async function viewUser(userId) {
    currentUserId = userId;
    
    try {
        const response = await fetch(`/api/users/${userId}`);
        const user = await response.json();
        
        if (response.ok) {
            document.getElementById('userModalBody').innerHTML = `
                <div class="row">
                    <div class="col-md-4 text-center">
                        <img src="${user.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                             class="rounded-circle mb-3" width="120" height="120">
                        <h5>${user.username}</h5>
                        <p class="text-muted">${user.id}</p>
                    </div>
                    <div class="col-md-8">
                        <h6>معلومات المستخدم</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>تاريخ الانضمام:</strong></td>
                                <td>${new Date(user.joinedAt).toLocaleDateString('ar-SA')}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر نشاط:</strong></td>
                                <td>${user.lastActive ? new Date(user.lastActive).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
                            </tr>
                            <tr>
                                <td><strong>الأوامر المستخدمة:</strong></td>
                                <td>${user.commandsUsed || 0}</td>
                            </tr>
                            <tr>
                                <td><strong>السيرفرات المشتركة:</strong></td>
                                <td>${user.mutualGuilds || 0}</td>
                            </tr>
                            <tr>
                                <td><strong>حالة البريميوم:</strong></td>
                                <td>
                                    ${user.premium?.active ? 
                                        `<span class="badge bg-warning"><i class="fas fa-crown"></i> نشط حتى ${new Date(user.premium.expiresAt).toLocaleDateString('ar-SA')}</span>` :
                                        '<span class="badge bg-secondary">غير نشط</span>'
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    ${user.banned ? 
                                        '<span class="badge bg-danger"><i class="fas fa-ban"></i> محظور</span>' :
                                        '<span class="badge bg-success"><i class="fas fa-check"></i> نشط</span>'
                                    }
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('userModal')).show();
            
        } else {
            alert('خطأ في تحميل بيانات المستخدم');
        }
        
    } catch (error) {
        alert('حدث خطأ في تحميل البيانات');
    }
}

// تبديل البريميوم للمستخدم
async function toggleUserPremium(userId) {
    if (!confirm('هل أنت متأكد من تبديل حالة البريميوم؟')) return;
    
    try {
        const response = await fetch(`/api/users/${userId}/premium`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم تحديث حالة البريميوم بنجاح!');
            loadUsers(currentPage);
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في تحديث البريميوم');
    }
}

// تبديل حظر المستخدم
async function toggleUserBan(userId) {
    if (!confirm('هل أنت متأكد من تبديل حالة الحظر؟')) return;
    
    try {
        const response = await fetch(`/api/users/${userId}/ban`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم تحديث حالة الحظر بنجاح!');
            loadUsers(currentPage);
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في تحديث الحظر');
    }
}

// البحث في الوقت الفعلي
document.getElementById('searchInput').addEventListener('input', () => {
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
        loadUsers(1);
    }, 500);
});

// تغيير الفلاتر
document.getElementById('statusFilter').addEventListener('change', () => loadUsers(1));
document.getElementById('sortBy').addEventListener('change', () => loadUsers(1));

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => loadUsers());

// تحديث البيانات كل دقيقة
setInterval(() => loadUsers(currentPage), 60000);
</script>

</body>
</html>
