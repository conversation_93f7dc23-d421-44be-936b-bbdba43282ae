<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .btn-discord {
            background-color: #5865F2;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .btn-discord:hover {
            background-color: #4752c4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(88, 101, 242, 0.3);
        }
        .bot-logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #fff;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card login-card border-0">
                <div class="card-body text-center py-5">
                    <!-- شعار البوت -->
                    <% if (config && config.clientId) { %>
                        <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=256"
                             alt="CS Bot"
                             class="bot-logo mb-4"
                             onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
                    <% } else { %>
                        <img src="https://cdn.discordapp.com/embed/avatars/0.png"
                             alt="CS Bot"
                             class="bot-logo mb-4">
                    <% } %>

                    <h2 class="text-primary mb-2">CS Bot</h2>
                    <h4 class="mb-4"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</h4>

                    <p class="text-muted mb-4">
                        قم بتسجيل الدخول باستخدام حساب Discord الخاص بك للوصول إلى لوحة التحكم
                    </p>

                    <div class="d-grid gap-3">
                        <a href="/auth/discord" class="btn btn-discord btn-lg">
                            <i class="fab fa-discord me-2"></i> تسجيل الدخول باستخدام Discord
                        </a>

                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i> العودة للصفحة الرئيسية
                        </a>
                    </div>

                    <div class="mt-4">
                        <small class="text-muted">
                            بتسجيل الدخول، أنت توافق على
                            <a href="/terms" class="text-decoration-none">شروط الاستخدام</a>
                            و
                            <a href="/privacy" class="text-decoration-none">سياسة الخصوصية</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
