<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحليلات المتقدمة - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-chart-line"></i> CS Bot - التحليلات المتقدمة
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-chart-line"></i> التحليلات المتقدمة
                    </h2>
                    <p class="card-text">تحليل شامل لأداء البوت والمستخدمين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-primary mb-3"></i>
                    <h3 class="card-title" id="totalUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <p class="card-text text-muted">إجمالي المستخدمين</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +12% هذا الشهر
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-terminal fa-3x text-success mb-3"></i>
                    <h3 class="card-title" id="totalCommands">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <p class="card-text text-muted">الأوامر المنفذة</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +25% هذا الأسبوع
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-3x text-warning mb-3"></i>
                    <h3 class="card-title" id="premiumUsers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <p class="card-text text-muted">مستخدمين البريميوم</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +8% هذا الشهر
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-server fa-3x text-info mb-3"></i>
                    <h3 class="card-title" id="activeServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h3>
                    <p class="card-text text-muted">السيرفرات النشطة</p>
                    <small class="text-success">
                        <i class="fas fa-arrow-up"></i> +5% هذا الشهر
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Commands Usage Chart -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> استخدام الأوامر (آخر 7 أيام)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="commandsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> نمو المستخدمين (آخر 30 يوم)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="usersChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- More Charts -->
    <div class="row mb-4">
        <!-- Most Used Commands -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy"></i> الأوامر الأكثر استخداماً
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="topCommandsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Server Activity -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-activity"></i> نشاط السيرفرات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="serverActivityChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Tables -->
    <div class="row">
        <!-- Top Users -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-medal"></i> أكثر المستخدمين نشاطاً
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>المرتبة</th>
                                    <th>المستخدم</th>
                                    <th>الأوامر</th>
                                    <th>النشاط</th>
                                </tr>
                            </thead>
                            <tbody id="topUsersTable">
                                <tr>
                                    <td colspan="4" class="text-center py-3">
                                        <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i> النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentActivity">
                        <div class="text-center py-3">
                            <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تحميل البيانات
async function loadAnalytics() {
    try {
        const response = await fetch('/api/analytics');
        const data = await response.json();
        
        // تحديث الإحصائيات السريعة
        document.getElementById('totalUsers').textContent = data.totalUsers?.toLocaleString() || '0';
        document.getElementById('totalCommands').textContent = data.totalCommands?.toLocaleString() || '0';
        document.getElementById('premiumUsers').textContent = data.premiumUsers?.toLocaleString() || '0';
        document.getElementById('activeServers').textContent = data.activeServers?.toLocaleString() || '0';
        
        // إنشاء الرسوم البيانية
        createCommandsChart(data.commandsData || []);
        createUsersChart(data.usersData || []);
        createTopCommandsChart(data.topCommands || []);
        createServerActivityChart(data.serverActivity || []);
        
        // تحديث الجداول
        updateTopUsersTable(data.topUsers || []);
        updateRecentActivity(data.recentActivity || []);
        
    } catch (error) {
        console.error('خطأ في تحميل التحليلات:', error);
    }
}

// رسم بياني لاستخدام الأوامر
function createCommandsChart(data) {
    const ctx = document.getElementById('commandsChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['6 أيام', '5 أيام', '4 أيام', '3 أيام', 'أمس', 'اليوم'],
            datasets: [{
                label: 'الأوامر المنفذة',
                data: data.length ? data : [120, 150, 180, 200, 250, 300],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم بياني لنمو المستخدمين
function createUsersChart(data) {
    const ctx = document.getElementById('usersChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
            datasets: [{
                label: 'مستخدمين جدد',
                data: data.length ? data : [45, 67, 89, 123],
                backgroundColor: '#28a745',
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم بياني للأوامر الأكثر استخداماً
function createTopCommandsChart(data) {
    const ctx = document.getElementById('topCommandsChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.length ? data.map(d => d.name) : ['help', 'premium', 'stats', 'ping', 'avatar'],
            datasets: [{
                data: data.length ? data.map(d => d.count) : [45, 30, 15, 8, 2],
                backgroundColor: [
                    '#ff6384',
                    '#36a2eb',
                    '#ffce56',
                    '#4bc0c0',
                    '#9966ff'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

// رسم بياني لنشاط السيرفرات
function createServerActivityChart(data) {
    const ctx = document.getElementById('serverActivityChart').getContext('2d');
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['الصباح', 'الظهر', 'العصر', 'المساء', 'الليل', 'منتصف الليل'],
            datasets: [{
                label: 'النشاط',
                data: data.length ? data : [65, 85, 90, 95, 70, 45],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.2)',
                pointBackgroundColor: '#17a2b8'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// تحديث جدول أكثر المستخدمين نشاطاً
function updateTopUsersTable(users) {
    const tbody = document.getElementById('topUsersTable');
    
    if (users.length === 0) {
        // بيانات وهمية للعرض
        users = [
            { rank: 1, username: 'المستخدم1', commands: 245, activity: 'عالي' },
            { rank: 2, username: 'المستخدم2', commands: 189, activity: 'متوسط' },
            { rank: 3, username: 'المستخدم3', commands: 156, activity: 'متوسط' },
            { rank: 4, username: 'المستخدم4', commands: 134, activity: 'منخفض' },
            { rank: 5, username: 'المستخدم5', commands: 98, activity: 'منخفض' }
        ];
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <span class="badge bg-primary">#${user.rank}</span>
            </td>
            <td>${user.username}</td>
            <td>
                <span class="badge bg-success">${user.commands}</span>
            </td>
            <td>
                <span class="badge ${getActivityBadgeClass(user.activity)}">${user.activity}</span>
            </td>
        </tr>
    `).join('');
}

// تحديث النشاط الأخير
function updateRecentActivity(activities) {
    const container = document.getElementById('recentActivity');
    
    if (activities.length === 0) {
        // بيانات وهمية للعرض
        activities = [
            { time: 'منذ دقيقتين', action: 'تم تنفيذ أمر !help', user: 'المستخدم1' },
            { time: 'منذ 5 دقائق', action: 'انضمام مستخدم جديد', user: 'المستخدم2' },
            { time: 'منذ 10 دقائق', action: 'تم تنفيذ أمر !premium', user: 'المستخدم3' },
            { time: 'منذ 15 دقيقة', action: 'تم تنفيذ أمر !stats', user: 'المستخدم4' }
        ];
    }
    
    container.innerHTML = activities.map(activity => `
        <div class="d-flex align-items-center mb-3">
            <div class="flex-shrink-0">
                <i class="fas fa-circle text-success" style="font-size: 8px;"></i>
            </div>
            <div class="flex-grow-1 ms-3">
                <div class="fw-bold">${activity.action}</div>
                <small class="text-muted">${activity.user} • ${activity.time}</small>
            </div>
        </div>
    `).join('');
}

function getActivityBadgeClass(activity) {
    switch (activity) {
        case 'عالي': return 'bg-success';
        case 'متوسط': return 'bg-warning';
        case 'منخفض': return 'bg-secondary';
        default: return 'bg-secondary';
    }
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadAnalytics);

// تحديث البيانات كل دقيقة
setInterval(loadAnalytics, 60000);
</script>

</body>
</html>
