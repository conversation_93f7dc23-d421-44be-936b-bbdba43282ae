/**
 * أوامر الإدارة المتقدمة - CS Bot
 * أوامر قوية لإدارة السيرفر والبوت
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const config = require('../config');
const webhookManager = require('../utils/webhookManager');
const channelManager = require('../utils/channelManager');

module.exports = {
    name: 'admin',
    description: 'أوامر الإدارة المتقدمة',
    aliases: ['إدارة', 'ادارة'],
    category: 'إدارة',
    usage: 'admin <subcommand>',
    cooldown: 5,
    ownerOnly: false,
    permissions: [PermissionFlagsBits.Administrator],
    
    async execute(message, args, client) {
        const subcommand = args[0]?.toLowerCase();

        // فحص الصلاحيات
        if (!message.member.permissions.has(PermissionFlagsBits.Administrator) && 
            !config.owners.includes(message.author.id)) {
            const embed = new EmbedBuilder()
                .setTitle('❌ ليس لديك صلاحية')
                .setDescription('هذا الأمر مخصص للمديرين فقط')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        if (!subcommand) {
            return this.showAdminMenu(message, client);
        }

        switch (subcommand) {
            case 'channels':
            case 'قنوات':
                return this.manageChannels(message, args, client);
            
            case 'users':
            case 'مستخدمين':
                return this.manageUsers(message, args, client);
            
            case 'stats':
            case 'احصائيات':
                return this.showStats(message, client);
            
            case 'cleanup':
            case 'تنظيف':
                return this.cleanup(message, args, client);
            
            case 'backup':
            case 'نسخ':
                return this.backup(message, client);
            
            case 'settings':
            case 'اعدادات':
                return this.manageSettings(message, args, client);
            
            case 'webhook':
                return this.testWebhook(message, client);
            
            case 'announce':
            case 'اعلان':
                return this.announce(message, args, client);
            
            default:
                return this.showAdminMenu(message, client);
        }
    },

    async showAdminMenu(message, client) {
        const embed = new EmbedBuilder()
            .setTitle('🛡️ لوحة الإدارة المتقدمة')
            .setDescription('اختر من الخيارات التالية:')
            .addFields([
                {
                    name: '📺 إدارة القنوات',
                    value: '`admin channels` - إدارة قنوات السيرفر',
                    inline: true
                },
                {
                    name: '👥 إدارة المستخدمين',
                    value: '`admin users` - إدارة المستخدمين',
                    inline: true
                },
                {
                    name: '📊 الإحصائيات',
                    value: '`admin stats` - عرض إحصائيات مفصلة',
                    inline: true
                },
                {
                    name: '🧹 التنظيف',
                    value: '`admin cleanup` - تنظيف الرسائل',
                    inline: true
                },
                {
                    name: '💾 النسخ الاحتياطي',
                    value: '`admin backup` - إنشاء نسخة احتياطية',
                    inline: true
                },
                {
                    name: '⚙️ الإعدادات',
                    value: '`admin settings` - إدارة إعدادات السيرفر',
                    inline: true
                },
                {
                    name: '📢 الإعلانات',
                    value: '`admin announce` - إرسال إعلان',
                    inline: true
                },
                {
                    name: '🔗 اختبار Webhook',
                    value: '`admin webhook` - اختبار الـ webhook',
                    inline: true
                }
            ])
            .setColor('#0099ff')
            .setFooter({ text: 'CS Bot Admin Panel' });

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('admin_channels')
                    .setLabel('📺 القنوات')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('admin_users')
                    .setLabel('👥 المستخدمين')
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('admin_stats')
                    .setLabel('📊 إحصائيات')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('admin_cleanup')
                    .setLabel('🧹 تنظيف')
                    .setStyle(ButtonStyle.Danger)
            );

        return message.reply({ embeds: [embed], components: [row] });
    },

    async manageChannels(message, args, client) {
        const channels = await channelManager.getGuildChannels(message.guild, true);
        const stats = await channelManager.getChannelStats(message.guild);

        const embed = new EmbedBuilder()
            .setTitle('📺 إدارة القنوات')
            .setDescription(`إحصائيات قنوات **${message.guild.name}**`)
            .addFields([
                {
                    name: '📊 إجمالي القنوات',
                    value: `${stats.total}`,
                    inline: true
                },
                {
                    name: '💬 قنوات نصية',
                    value: `${stats.text}`,
                    inline: true
                },
                {
                    name: '🔊 قنوات صوتية',
                    value: `${stats.voice}`,
                    inline: true
                },
                {
                    name: '📁 الفئات',
                    value: `${stats.category}`,
                    inline: true
                },
                {
                    name: '✅ قابلة للوصول',
                    value: `${stats.accessible}`,
                    inline: true
                },
                {
                    name: '🔒 مقيدة',
                    value: `${stats.restricted}`,
                    inline: true
                }
            ])
            .setColor('#00ff00');

        // عرض القنوات النصية
        if (channels.text.length > 0) {
            const textChannels = channels.text.slice(0, 10).map(ch => 
                `${ch.permissions.viewChannel ? '✅' : '❌'} <#${ch.id}>`
            ).join('\n');
            
            embed.addFields([
                {
                    name: '💬 القنوات النصية (أول 10)',
                    value: textChannels,
                    inline: false
                }
            ]);
        }

        // فحص المشاكل
        const issues = await channelManager.validateChannels(message.guild);
        if (issues.length > 0) {
            const issueText = issues.slice(0, 5).map(issue => 
                `⚠️ ${issue.channel}: ${issue.issue}`
            ).join('\n');
            
            embed.addFields([
                {
                    name: '⚠️ مشاكل الصلاحيات',
                    value: issueText,
                    inline: false
                }
            ]);
        }

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('channels_refresh')
                    .setLabel('🔄 تحديث')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('channels_export')
                    .setLabel('📤 تصدير')
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('channels_validate')
                    .setLabel('🔍 فحص')
                    .setStyle(ButtonStyle.Success)
            );

        return message.reply({ embeds: [embed], components: [row] });
    },

    async showStats(message, client) {
        const uptime = process.uptime();
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        const seconds = Math.floor(uptime % 60);

        const embed = new EmbedBuilder()
            .setTitle('📊 إحصائيات البوت المتقدمة')
            .addFields([
                {
                    name: '🤖 معلومات البوت',
                    value: `**الاسم:** ${client.user.username}\n**المعرف:** ${client.user.id}\n**وقت التشغيل:** ${hours}س ${minutes}د ${seconds}ث`,
                    inline: true
                },
                {
                    name: '🏠 السيرفرات',
                    value: `**العدد:** ${client.guilds.cache.size}\n**الأعضاء:** ${client.users.cache.size}\n**القنوات:** ${client.channels.cache.size}`,
                    inline: true
                },
                {
                    name: '⚡ الأداء',
                    value: `**الأوامر المنفذة:** ${client.stats.commandsExecuted}\n**الرسائل المعالجة:** ${client.stats.messagesProcessed}\n**الذاكرة:** ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
                    inline: true
                },
                {
                    name: '🌐 الشبكة',
                    value: `**البينغ:** ${client.ws.ping}ms\n**الحالة:** ${client.ws.status === 0 ? '🟢 متصل' : '🔴 منقطع'}\n**الإصدار:** ${require('discord.js').version}`,
                    inline: true
                },
                {
                    name: '💾 قاعدة البيانات',
                    value: `**النوع:** ${global.usingTempDB ? 'مؤقتة' : 'MongoDB'}\n**الحالة:** ${global.usingTempDB ? '🟡 مؤقت' : '🟢 متصل'}`,
                    inline: true
                },
                {
                    name: '🔧 النظام',
                    value: `**المنصة:** ${process.platform}\n**Node.js:** ${process.version}\n**CPU:** ${process.arch}`,
                    inline: true
                }
            ])
            .setColor('#9b59b6')
            .setThumbnail(client.user.displayAvatarURL())
            .setTimestamp();

        return message.reply({ embeds: [embed] });
    },

    async cleanup(message, args, client) {
        const amount = parseInt(args[1]) || 10;
        
        if (amount < 1 || amount > 100) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('يجب أن يكون العدد بين 1 و 100')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        try {
            const messages = await message.channel.bulkDelete(amount + 1, true);
            
            const embed = new EmbedBuilder()
                .setTitle('🧹 تم التنظيف')
                .setDescription(`تم حذف **${messages.size - 1}** رسالة`)
                .setColor('#00ff00')
                .setTimestamp();

            const reply = await message.channel.send({ embeds: [embed] });
            
            // حذف رسالة التأكيد بعد 5 ثوان
            setTimeout(() => {
                reply.delete().catch(() => {});
            }, 5000);

            // إرسال إشعار للـ webhook
            await webhookManager.sendNotification(
                '🧹 تنظيف القناة',
                `تم حذف ${messages.size - 1} رسالة من قناة ${message.channel.name}`,
                '#00ff00',
                [
                    {
                        name: '👤 المدير',
                        value: message.author.tag,
                        inline: true
                    },
                    {
                        name: '📺 القناة',
                        value: message.channel.name,
                        inline: true
                    }
                ]
            );

        } catch (error) {
            console.error('خطأ في تنظيف الرسائل:', error);
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ في التنظيف')
                .setDescription('حدث خطأ أثناء حذف الرسائل')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }
    },

    async backup(message, client) {
        const embed = new EmbedBuilder()
            .setTitle('💾 إنشاء نسخة احتياطية')
            .setDescription('جاري إنشاء نسخة احتياطية من بيانات السيرفر...')
            .setColor('#ffff00');

        const msg = await message.reply({ embeds: [embed] });

        try {
            // تصدير القنوات
            const channelsData = await channelManager.exportChannels(message.guild);
            
            // تصدير معلومات السيرفر
            const guildData = {
                id: message.guild.id,
                name: message.guild.name,
                description: message.guild.description,
                memberCount: message.guild.memberCount,
                createdAt: message.guild.createdAt,
                ownerId: message.guild.ownerId,
                features: message.guild.features,
                roles: message.guild.roles.cache.map(role => ({
                    id: role.id,
                    name: role.name,
                    color: role.hexColor,
                    permissions: role.permissions.toArray()
                }))
            };

            const backupData = {
                guild: guildData,
                channels: channelsData,
                createdAt: new Date().toISOString(),
                createdBy: message.author.id
            };

            // حفظ النسخة الاحتياطية (يمكن تطويرها لحفظ في ملف أو قاعدة بيانات)
            const backupId = Date.now().toString();
            
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ تم إنشاء النسخة الاحتياطية')
                .setDescription(`تم إنشاء نسخة احتياطية بنجاح`)
                .addFields([
                    {
                        name: '🆔 معرف النسخة',
                        value: backupId,
                        inline: true
                    },
                    {
                        name: '📊 البيانات المحفوظة',
                        value: `${channelsData.channels.text.length} قناة نصية\n${channelsData.channels.voice.length} قناة صوتية\n${guildData.roles.length} رتبة`,
                        inline: true
                    }
                ])
                .setColor('#00ff00')
                .setTimestamp();

            await msg.edit({ embeds: [successEmbed] });

            // إرسال إشعار للـ webhook
            await webhookManager.sendNotification(
                '💾 نسخة احتياطية جديدة',
                `تم إنشاء نسخة احتياطية للسيرفر ${message.guild.name}`,
                '#00ff00',
                [
                    {
                        name: '👤 المدير',
                        value: message.author.tag,
                        inline: true
                    },
                    {
                        name: '🆔 معرف النسخة',
                        value: backupId,
                        inline: true
                    }
                ]
            );

        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ فشل في إنشاء النسخة الاحتياطية')
                .setDescription('حدث خطأ أثناء إنشاء النسخة الاحتياطية')
                .setColor('#ff0000');

            await msg.edit({ embeds: [errorEmbed] });
        }
    },

    async testWebhook(message, client) {
        if (!config.owners.includes(message.author.id)) {
            const embed = new EmbedBuilder()
                .setTitle('❌ ليس لديك صلاحية')
                .setDescription('هذا الأمر مخصص لمالكي البوت فقط')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle('🔗 اختبار الـ Webhook')
            .setDescription('جاري اختبار الـ webhook...')
            .setColor('#ffff00');

        const msg = await message.reply({ embeds: [embed] });

        try {
            const success = await webhookManager.testWebhook();
            
            const resultEmbed = new EmbedBuilder()
                .setTitle(success ? '✅ نجح الاختبار' : '❌ فشل الاختبار')
                .setDescription(success ? 'الـ webhook يعمل بشكل صحيح' : 'فشل في إرسال الـ webhook')
                .setColor(success ? '#00ff00' : '#ff0000');

            await msg.edit({ embeds: [resultEmbed] });

        } catch (error) {
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ خطأ في الاختبار')
                .setDescription('حدث خطأ أثناء اختبار الـ webhook')
                .setColor('#ff0000');

            await msg.edit({ embeds: [errorEmbed] });
        }
    },

    async announce(message, args, client) {
        if (!args[1]) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('يرجى كتابة نص الإعلان')
                .setColor('#ff0000');
            return message.reply({ embeds: [embed] });
        }

        const announcement = args.slice(1).join(' ');
        
        const embed = new EmbedBuilder()
            .setTitle('📢 إعلان مهم')
            .setDescription(announcement)
            .setColor('#ff6b6b')
            .setAuthor({ 
                name: message.author.username, 
                iconURL: message.author.displayAvatarURL() 
            })
            .setTimestamp()
            .setFooter({ text: `إعلان من إدارة ${message.guild.name}` });

        // إرسال الإعلان في القناة الحالية
        await message.channel.send({ embeds: [embed] });

        // إرسال إشعار للـ webhook
        await webhookManager.sendNotification(
            '📢 إعلان جديد',
            `تم إرسال إعلان في السيرفر ${message.guild.name}`,
            '#ff6b6b',
            [
                {
                    name: '👤 المرسل',
                    value: message.author.tag,
                    inline: true
                },
                {
                    name: '📺 القناة',
                    value: message.channel.name,
                    inline: true
                },
                {
                    name: '📝 المحتوى',
                    value: announcement.substring(0, 100) + (announcement.length > 100 ? '...' : ''),
                    inline: false
                }
            ]
        );

        // حذف رسالة الأمر
        await message.delete().catch(() => {});
    }
};
