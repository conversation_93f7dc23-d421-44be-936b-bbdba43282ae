const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true,
        unique: true
    },
    username: {
        type: String,
        required: true
    },
    discriminator: {
        type: String,
        default: '0000'
    },
    avatar: {
        type: String,
        default: null
    },
    email: {
        type: String,
        default: null
    },

    // إحصائيات المستخدم
    stats: {
        commandsUsed: {
            type: Number,
            default: 0
        },
        messagesCount: {
            type: Number,
            default: 0
        },
        voiceTime: {
            type: Number,
            default: 0
        },
        joinedServers: {
            type: Number,
            default: 0
        },
        invitesCount: {
            type: Number,
            default: 0
        }
    },

    // البريميوم
    premium: {
        active: {
            type: Boolean,
            default: false
        },
        startDate: {
            type: Date,
            default: null
        },
        endDate: {
            type: Date,
            default: null
        },
        type: {
            type: String,
            enum: ['monthly', 'yearly', 'lifetime'],
            default: 'monthly'
        }
    },

    // الاقتصاد
    economy: {
        balance: {
            type: Number,
            default: 1000
        },
        bank: {
            type: Number,
            default: 0
        },
        lastDaily: {
            type: Date,
            default: null
        },
        lastWeekly: {
            type: Date,
            default: null
        },
        lastWork: {
            type: Date,
            default: null
        },
        job: {
            type: String,
            default: null
        },
        level: {
            type: Number,
            default: 1
        },
        xp: {
            type: Number,
            default: 0
        }
    },

    // الإعدادات
    settings: {
        language: {
            type: String,
            enum: ['ar', 'en'],
            default: 'ar'
        },
        timezone: {
            type: String,
            default: 'Asia/Riyadh'
        },
        notifications: {
            type: Boolean,
            default: true
        },
        privacy: {
            showStats: {
                type: Boolean,
                default: true
            },
            showInventory: {
                type: Boolean,
                default: true
            }
        }
    },

    // الحظر والعقوبات
    moderation: {
        banned: {
            type: Boolean,
            default: false
        },
        banReason: {
            type: String,
            default: null
        },
        banDate: {
            type: Date,
            default: null
        },
        warnings: [{
            reason: String,
            moderator: String,
            date: {
                type: Date,
                default: Date.now
            }
        }]
    },

    // تواريخ مهمة
    firstJoin: {
        type: Date,
        default: Date.now
    },
    lastSeen: {
        type: Date,
        default: Date.now
    },
    lastCommand: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});

// إنشاء فهارس للبحث السريع
userSchema.index({ userId: 1 });
userSchema.index({ username: 1 });
userSchema.index({ 'premium.active': 1 });
userSchema.index({ 'moderation.banned': 1 });

// دوال مساعدة
userSchema.methods.addXP = function(amount) {
    this.economy.xp += amount;
    const newLevel = Math.floor(this.economy.xp / 1000) + 1;
    if (newLevel > this.economy.level) {
        this.economy.level = newLevel;
        return true; // level up
    }
    return false;
};

userSchema.methods.isPremium = function() {
    return this.premium.active && this.premium.endDate > new Date();
};

userSchema.methods.addCommand = function() {
    this.stats.commandsUsed += 1;
    this.lastCommand = new Date();
    this.lastSeen = new Date();
};

const User = mongoose.model('User', userSchema);

// دالة للحصول على بيانات المستخدم
async function getUserData(userId, username, discriminator, avatar) {
    try {
        let user = await User.findOne({ userId });

        if (!user) {
            user = new User({
                userId,
                username,
                discriminator: discriminator || '0000',
                avatar
            });
            await user.save();
        } else {
            // تحديث البيانات الأساسية
            user.username = username;
            user.discriminator = discriminator || '0000';
            user.avatar = avatar;
            user.lastSeen = new Date();
            await user.save();
        }

        return user;
    } catch (error) {
        console.error('خطأ في getUserData:', error);
        throw error;
    }
}

module.exports = User;
module.exports.getUserData = getUserData;
