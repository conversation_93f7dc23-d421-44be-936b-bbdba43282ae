/**
 * CS Bot - الإعدادات الأساسية
 *
 * هذا الملف يحتوي فقط على الإعدادات الضرورية للبوت
 * باقي الإعدادات يمكن التحكم بها من خلال لوحة التحكم
 */

// تحميل المتغيرات البيئية
require('dotenv').config();

module.exports = {
    // إعدادات البوت الأساسية
    token: process.env.BOT_TOKEN || "MTM2MjMzOTkyNjUzNTgzMTU1Mw.GTs-5E.6tH8E-_zl9H7JrvGths4erKuza5DHyHxqrrGlM",
    clientId: process.env.CLIENT_ID || "1362339926535831553",
    clientSecret: process.env.CLIENT_SECRET || "Mcqtey2kQ9VYStRTJX3494OLpsO_dChB",
    prefix: "!",

    // إعدادات قاعدة البيانات
    mongoURI: process.env.MONGODB_URI || "mongodb://localhost:27017/csbot",

    // إعدادات لوحة التحكم
    dashboard: {
        port: process.env.PORT || 3000,
        domain: process.env.DOMAIN || "http://localhost:3000",
        sessionSecret: process.env.SESSION_SECRET || "cs-bot-secret-key",
        callbackURL: "/auth/callback"
    },

    // معلومات المالك
    owner: {
        id: "1289581696995561495",
        username: ".h_4s"
    },

    // اللغات المدعومة
    languages: ["ar", "en"],
    defaultLanguage: "ar",

    // مسارات الملفات المهمة
    paths: {
        commands: "./commands",
        events: "./events",
        models: "./models",
        views: "./dashboard/views",
        public: "./dashboard/public"
    }
};
