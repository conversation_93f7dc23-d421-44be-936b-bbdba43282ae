/**
 * CS Bot - الإعدادات الأساسية
 *
 * هذا الملف يحتوي فقط على الإعدادات الضرورية للبوت
 * باقي الإعدادات يمكن التحكم بها من خلال لوحة التحكم
 */

// تحميل المتغيرات البيئية
require('dotenv').config();

module.exports = {
    // إعدادات البوت الأساسية
    token: process.env.BOT_TOKEN || "MTM2MjMzOTkyNjUzNTgzMTU1Mw.G8Gpjz.u0uCi1TOTHKSTa8IyGCM5RWxPS2xufrlveBrt4",
    clientId: process.env.CLIENT_ID || "1372927532655054848",
    clientSecret: process.env.CLIENT_SECRET || "VdHFP8ATC8fe0XtgAgzAWTfGx_LgGpy_",
    prefix: "!",

    // إعدادات قاعدة البيانات
    mongoURI: process.env.MONGODB_URI || "mongodb://Abdullah:<EMAIL>:27017/db_Abdullah?authSource=admin",

    // إعدادات لوحة التحكم
    dashboard: {
        port: process.env.PORT || 3000,
        domain: process.env.DOMAIN || "http://localhost:3000",
        sessionSecret: process.env.SESSION_SECRET || "cs-bot-secret-key",
        callbackURL: "/auth/callback"
    },

    // معلومات المالك
    owner: {
        id: "1289581696995561495",
        username: ".h_4s"
    },

    // اللغات المدعومة
    languages: ["ar", "en"],
    defaultLanguage: "ar",

    // مسارات الملفات المهمة
    paths: {
        commands: "./commands",
        events: "./events",
        models: "./models",
        views: "./dashboard/views",
        public: "./dashboard/public"
    }
};
