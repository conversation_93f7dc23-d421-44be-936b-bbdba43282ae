/**
 * CS Bot - الإعدادات الأساسية
 *
 * هذا الملف يحتوي فقط على الإعدادات الضرورية للبوت
 * باقي الإعدادات يمكن التحكم بها من خلال لوحة التحكم
 */

// تحميل المتغيرات البيئية
require('dotenv').config();

module.exports = {
    // إعدادات البوت الأساسية
    token: process.env.BOT_TOKEN || "YOUR_BOT_TOKEN_HERE",
    clientId: process.env.CLIENT_ID || "YOUR_CLIENT_ID_HERE",
    clientSecret: process.env.CLIENT_SECRET || "YOUR_CLIENT_SECRET_HERE",
    prefix: "!",

    // إعدادات قاعدة البيانات - استخدام قاعدة بيانات محلية مؤقتة
    mongoURI: process.env.MONGODB_URI || "mongodb+srv://r81682195:<EMAIL>/",

    // إعدادات لوحة التحكم
    dashboard: {
        port: process.env.PORT || 3000,
        domain: process.env.DOMAIN || "http://localhost:3000",
        sessionSecret: process.env.SESSION_SECRET || "cs-bot-secret-key",
        callbackURL: process.env.CALLBACK_URL || "http://localhost:3000/auth/callback"
    },

    // معلومات المالك
    owner: {
        id: "1289581696995561495",
        username: ".h_4s"
    },

    // اللغات المدعومة
    languages: ["ar", "en"],
    defaultLanguage: "ar",

    // مسارات الملفات المهمة
    paths: {
        commands: "./commands",
        events: "./events",
        models: "./models",
        views: "./dashboard/views",
        public: "./dashboard/public"
    }
};
