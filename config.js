/**
 * CS Bot - الإعدادات الأساسية
 *
 * هذا الملف يحتوي فقط على الإعدادات الضرورية للبوت
 * باقي الإعدادات يمكن التحكم بها من خلال لوحة التحكم
 */

// تحميل المتغيرات البيئية
require('dotenv').config();

module.exports = {
    // إعدادات البوت الأساسية
    token: process.env.BOT_TOKEN || "YOUR_BOT_TOKEN_HERE",
    clientId: process.env.CLIENT_ID || "YOUR_CLIENT_ID_HERE",
    clientSecret: process.env.CLIENT_SECRET || "YOUR_CLIENT_SECRET_HERE",
    prefix: "!",

    // إعدادات قاعدة البيانات - استخدام قاعدة بيانات محلية مؤقتة
    mongoURI: process.env.MONGODB_URI || "mongodb+srv://r81682195:<EMAIL>/",

    // Webhook للإشعارات المهمة
    webhook: {
        url: process.env.WEBHOOK_URL || "https://discord.com/api/webhooks/1377228284856893450/PLuaHJQ9un0T3bXCQ0cqVDfL0e1nbzlzPpdgRXOkyzHh8vrhu6dt2lyJOgDqXUrliNfl",
        enabled: process.env.WEBHOOK_ENABLED === 'true' || true
    },

    // إعدادات الأمان والأداء
    security: {
        rateLimitPerMinute: parseInt(process.env.RATE_LIMIT) || 60,
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10, // MB
        encryptionEnabled: process.env.ENCRYPTION_ENABLED === 'true' || false,
        maintenanceMode: process.env.MAINTENANCE_MODE === 'true' || false
    },

    // إعدادات التطوير
    development: {
        debugMode: process.env.DEBUG_MODE === 'true' || false,
        verboseLogging: process.env.VERBOSE_LOGGING === 'true' || true,
        autoReload: process.env.AUTO_RELOAD === 'true' || false
    },

    // إعدادات البريميوم
    premium: {
        enabled: process.env.PREMIUM_ENABLED === 'true' || true,
        defaultDuration: parseInt(process.env.DEFAULT_PREMIUM_DURATION) || 30 // أيام
    },

    // إعدادات التحليلات
    analytics: {
        enabled: process.env.ANALYTICS_ENABLED === 'true' || true,
        errorTracking: process.env.ERROR_TRACKING === 'true' || true
    },

    // إعدادات النسخ الاحتياطي
    backup: {
        autoBackup: process.env.AUTO_BACKUP === 'true' || true,
        interval: parseInt(process.env.BACKUP_INTERVAL) || 24, // ساعات
        directory: process.env.BACKUP_DIR || './backups'
    },

    // إعدادات اللغة
    language: {
        default: process.env.DEFAULT_LANGUAGE || 'ar',
        autoTranslate: process.env.AUTO_TRANSLATE === 'true' || false
    },

    // إعدادات لوحة التحكم
    dashboard: {
        port: process.env.PORT || 3000,
        domain: process.env.DOMAIN || "http://localhost:3000",
        sessionSecret: process.env.SESSION_SECRET || "cs-bot-secret-key",
        callbackURL: process.env.CALLBACK_URL || "http://localhost:3000/auth/callback"
    },

    // معلومات المالك
    owner: {
        id: "1289581696995561495",
        username: ".h_4s"
    },

    // اللغات المدعومة
    languages: ["ar", "en"],
    defaultLanguage: "ar",

    // مسارات الملفات المهمة
    paths: {
        commands: "./commands",
        events: "./events",
        models: "./models",
        views: "./dashboard/views",
        public: "./dashboard/public"
    }
};
