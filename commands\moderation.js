/**
 * CS Bot - نظام الإدارة الأسطوري
 * 
 * أدوات إدارة متقدمة للمشرفين
 */

const { EmbedBuilder, PermissionFlagsBits } = require('discord.js');

module.exports = {
    name: 'mod',
    aliases: ['moderation', 'إدارة', 'admin'],
    description: 'نظام الإدارة الأسطوري - أدوات متقدمة للمشرفين',
    usage: '!mod [ban|kick|mute|warn|clear|lock|unlock]',
    category: 'إدارة',
    cooldown: 3,
    permissions: ['ManageMessages'],
    
    async execute(message, args) {
        try {
            const subCommand = args[0]?.toLowerCase();
            
            if (!message.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
                return message.reply('❌ ليس لديك صلاحية لاستخدام أوامر الإدارة!');
            }
            
            switch (subCommand) {
                case 'ban':
                case 'حظر':
                    await banUser(message, args.slice(1));
                    break;
                    
                case 'kick':
                case 'طرد':
                    await kickUser(message, args.slice(1));
                    break;
                    
                case 'mute':
                case 'كتم':
                    await muteUser(message, args.slice(1));
                    break;
                    
                case 'unmute':
                case 'إلغاء_كتم':
                    await unmuteUser(message, args.slice(1));
                    break;
                    
                case 'warn':
                case 'تحذير':
                    await warnUser(message, args.slice(1));
                    break;
                    
                case 'clear':
                case 'مسح':
                    await clearMessages(message, args.slice(1));
                    break;
                    
                case 'lock':
                case 'قفل':
                    await lockChannel(message);
                    break;
                    
                case 'unlock':
                case 'فتح':
                    await unlockChannel(message);
                    break;
                    
                case 'slowmode':
                case 'بطيء':
                    await setSlowmode(message, args.slice(1));
                    break;
                    
                default:
                    await showModerationHelp(message);
            }
            
        } catch (error) {
            console.error('خطأ في نظام الإدارة:', error);
            message.reply('❌ حدث خطأ في نظام الإدارة!');
        }
    }
};

// حظر مستخدم
async function banUser(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.BanMembers)) {
        return message.reply('❌ ليس لديك صلاحية حظر الأعضاء!');
    }
    
    const target = message.mentions.members.first();
    if (!target) {
        return message.reply('❌ يرجى منشن المستخدم المراد حظره!');
    }
    
    if (target.id === message.author.id) {
        return message.reply('❌ لا يمكنك حظر نفسك!');
    }
    
    if (target.roles.highest.position >= message.member.roles.highest.position) {
        return message.reply('❌ لا يمكنك حظر شخص له رتبة أعلى أو مساوية لك!');
    }
    
    const reason = args.slice(1).join(' ') || 'لم يتم تحديد سبب';
    
    try {
        await target.send(`🔨 تم حظرك من سيرفر **${message.guild.name}**\n**السبب:** ${reason}`);
    } catch (error) {
        // المستخدم لا يقبل رسائل خاصة
    }
    
    await target.ban({ reason: `${reason} - بواسطة ${message.author.tag}` });
    
    const embed = new EmbedBuilder()
        .setTitle('🔨 تم حظر المستخدم')
        .setColor('#ff0000')
        .addFields(
            {
                name: '👤 المستخدم',
                value: `${target.user.tag} (${target.id})`,
                inline: true
            },
            {
                name: '👮 المشرف',
                value: message.author.tag,
                inline: true
            },
            {
                name: '📝 السبب',
                value: reason,
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    
    // تسجيل في قناة اللوجات
    await logAction(message.guild, 'BAN', target.user, message.author, reason);
}

// طرد مستخدم
async function kickUser(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.KickMembers)) {
        return message.reply('❌ ليس لديك صلاحية طرد الأعضاء!');
    }
    
    const target = message.mentions.members.first();
    if (!target) {
        return message.reply('❌ يرجى منشن المستخدم المراد طرده!');
    }
    
    if (target.id === message.author.id) {
        return message.reply('❌ لا يمكنك طرد نفسك!');
    }
    
    if (target.roles.highest.position >= message.member.roles.highest.position) {
        return message.reply('❌ لا يمكنك طرد شخص له رتبة أعلى أو مساوية لك!');
    }
    
    const reason = args.slice(1).join(' ') || 'لم يتم تحديد سبب';
    
    try {
        await target.send(`👢 تم طردك من سيرفر **${message.guild.name}**\n**السبب:** ${reason}`);
    } catch (error) {
        // المستخدم لا يقبل رسائل خاصة
    }
    
    await target.kick(`${reason} - بواسطة ${message.author.tag}`);
    
    const embed = new EmbedBuilder()
        .setTitle('👢 تم طرد المستخدم')
        .setColor('#ff9900')
        .addFields(
            {
                name: '👤 المستخدم',
                value: `${target.user.tag} (${target.id})`,
                inline: true
            },
            {
                name: '👮 المشرف',
                value: message.author.tag,
                inline: true
            },
            {
                name: '📝 السبب',
                value: reason,
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    
    await logAction(message.guild, 'KICK', target.user, message.author, reason);
}

// مسح الرسائل
async function clearMessages(message, args) {
    if (!message.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
        return message.reply('❌ ليس لديك صلاحية إدارة الرسائل!');
    }
    
    const amount = parseInt(args[0]);
    
    if (!amount || amount < 1 || amount > 100) {
        return message.reply('❌ يرجى تحديد عدد صحيح من 1 إلى 100!');
    }
    
    try {
        const deleted = await message.channel.bulkDelete(amount + 1, true);
        
        const embed = new EmbedBuilder()
            .setTitle('🧹 تم مسح الرسائل')
            .setColor('#00ff00')
            .setDescription(`تم مسح **${deleted.size - 1}** رسالة بواسطة ${message.author}`)
            .setTimestamp();
        
        const reply = await message.channel.send({ embeds: [embed] });
        
        // حذف رسالة التأكيد بعد 5 ثوان
        setTimeout(() => {
            reply.delete().catch(() => {});
        }, 5000);
        
        await logAction(message.guild, 'CLEAR', null, message.author, `${deleted.size - 1} رسالة في ${message.channel}`);
        
    } catch (error) {
        message.reply('❌ حدث خطأ أثناء مسح الرسائل! (قد تكون الرسائل قديمة جداً)');
    }
}

// قفل القناة
async function lockChannel(message) {
    if (!message.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return message.reply('❌ ليس لديك صلاحية إدارة القنوات!');
    }
    
    await message.channel.permissionOverwrites.edit(message.guild.roles.everyone, {
        SendMessages: false
    });
    
    const embed = new EmbedBuilder()
        .setTitle('🔒 تم قفل القناة')
        .setColor('#ff0000')
        .setDescription(`تم قفل القناة بواسطة ${message.author}`)
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    await logAction(message.guild, 'LOCK', null, message.author, `قناة ${message.channel}`);
}

// فتح القناة
async function unlockChannel(message) {
    if (!message.member.permissions.has(PermissionFlagsBits.ManageChannels)) {
        return message.reply('❌ ليس لديك صلاحية إدارة القنوات!');
    }
    
    await message.channel.permissionOverwrites.edit(message.guild.roles.everyone, {
        SendMessages: null
    });
    
    const embed = new EmbedBuilder()
        .setTitle('🔓 تم فتح القناة')
        .setColor('#00ff00')
        .setDescription(`تم فتح القناة بواسطة ${message.author}`)
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
    await logAction(message.guild, 'UNLOCK', null, message.author, `قناة ${message.channel}`);
}

// مساعدة الإدارة
async function showModerationHelp(message) {
    const embed = new EmbedBuilder()
        .setTitle('🛡️ نظام الإدارة الأسطوري')
        .setColor('#7289da')
        .setDescription('أدوات إدارة متقدمة للمشرفين')
        .addFields(
            {
                name: '🔨 أوامر العقوبات',
                value: [
                    '`!mod ban @user [reason]` - حظر مستخدم',
                    '`!mod kick @user [reason]` - طرد مستخدم',
                    '`!mod mute @user [time] [reason]` - كتم مستخدم',
                    '`!mod warn @user [reason]` - تحذير مستخدم'
                ].join('\n'),
                inline: false
            },
            {
                name: '🧹 أوامر التنظيف',
                value: [
                    '`!mod clear <number>` - مسح رسائل',
                    '`!mod lock` - قفل القناة',
                    '`!mod unlock` - فتح القناة',
                    '`!mod slowmode <seconds>` - وضع بطيء'
                ].join('\n'),
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// تسجيل الإجراءات
async function logAction(guild, action, target, moderator, reason) {
    // البحث عن قناة اللوجات
    const logChannel = guild.channels.cache.find(channel => 
        channel.name.includes('log') || channel.name.includes('سجل')
    );
    
    if (!logChannel) return;
    
    const embed = new EmbedBuilder()
        .setTitle(`📋 ${action}`)
        .setColor(getActionColor(action))
        .addFields(
            {
                name: '👮 المشرف',
                value: moderator.tag,
                inline: true
            },
            {
                name: '📝 السبب',
                value: reason,
                inline: true
            }
        )
        .setTimestamp();
    
    if (target) {
        embed.addFields({
            name: '👤 المستخدم',
            value: `${target.tag} (${target.id})`,
            inline: true
        });
    }
    
    try {
        await logChannel.send({ embeds: [embed] });
    } catch (error) {
        // تجاهل أخطاء اللوجات
    }
}

function getActionColor(action) {
    const colors = {
        'BAN': '#ff0000',
        'KICK': '#ff9900',
        'MUTE': '#ffff00',
        'WARN': '#ff6600',
        'CLEAR': '#00ff00',
        'LOCK': '#ff0000',
        'UNLOCK': '#00ff00'
    };
    return colors[action] || '#7289da';
}
