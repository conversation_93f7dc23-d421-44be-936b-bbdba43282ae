/**
 * CS Bot - Main Stylesheet
 */

/* Global Styles */
:root {
    --primary-color: #5865F2;
    --secondary-color: #2F3136;
    --accent-color: #EB459E;
    --light-color: #FFFFFF;
    --dark-color: #23272A;
    --success-color: #57F287;
    --warning-color: #FEE75C;
    --danger-color: #ED4245;
    --info-color: #5865F2;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* RTL Support */
body[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

body[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

body[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Navbar */
.navbar-brand img {
    margin-right: 0.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--light-color);
    padding: 5rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 1rem 1rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Server Icon Placeholder */
.server-icon-placeholder {
    width: 80px;
    height: 80px;
    background-color: var(--secondary-color);
    color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto;
}

/* Dashboard Sidebar */
.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.list-group-item-action:hover {
    background-color: rgba(88, 101, 242, 0.1);
}

/* Forms */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(88, 101, 242, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #4752c4;
    border-color: #4752c4;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Footer */
footer {
    margin-top: 3rem;
}

footer a {
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }

    .display-4 {
        font-size: 2.5rem;
    }
}

/* Custom Switch Styling */
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
}

/* Code Blocks */
code {
    background-color: #f1f1f1;
    color: var(--accent-color);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* إضافات أسطورية جديدة */

/* بطاقات الإحصائيات المحسنة */
.stat-card {
    transition: all 0.3s ease;
    border: none;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تأثيرات الأزرار المحسنة */
.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn-primary:hover::before {
    width: 300px;
    height: 300px;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* بطاقة البريميوم الأسطورية */
.premium-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 20px;
    padding: 5px 15px;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
    animation: pulse 2s infinite;
}

/* تحسينات للجداول */
.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: scale(1.01);
}

/* تأثيرات خاصة */
.glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.sparkle::after {
    content: '✨';
    position: absolute;
    top: -10px;
    right: -10px;
    animation: pulse 1.5s infinite;
}

/* خلفية متدرجة محسنة */
.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
}

.bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* رسوم متحركة إضافية */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* تحسينات شعار البوت */
.bot-logo {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.bot-logo:hover {
    transform: scale(1.1) rotate(360deg);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* تحسينات الشارات */
.badge {
    border-radius: 12px;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* تحسينات الحالة */
.status-online {
    color: #28a745;
    animation: pulse 2s infinite;
}

.status-offline {
    color: #dc3545;
}

.status-idle {
    color: #ffc107;
}

/* تحسينات الإشعارات المنبثقة */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 15px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-left: 4px solid var(--primary-color);
    animation: slideInRight 0.5s ease;
    z-index: 9999;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسينات الأزرار التفاعلية */
.interactive-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.interactive-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    z-index: -1;
}

.interactive-btn:hover::after {
    width: 300px;
    height: 300px;
}

/* تحسينات خاصة للسيرفرات */
.server-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.server-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.server-icon {
    transition: all 0.3s ease;
}

.server-icon:hover {
    transform: scale(1.1);
}

/* تحسينات الصلاحيات */
.permissions-list {
    font-size: 0.8rem;
    line-height: 1.2;
}

.permission-badge {
    display: inline-block;
    margin: 2px;
    padding: 2px 6px;
    background: rgba(var(--primary-color), 0.1);
    border-radius: 8px;
    font-size: 0.7rem;
}

/* تحسينات الإحصائيات */
.stats-number {
    font-size: 2rem;
    font-weight: bold;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسينات الخلفية المتحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}
