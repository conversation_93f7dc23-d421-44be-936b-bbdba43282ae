/**
 * CS Bot - Main Stylesheet
 */

/* Global Styles */
:root {
    --primary-color: #5865F2;
    --secondary-color: #2F3136;
    --accent-color: #EB459E;
    --light-color: #FFFFFF;
    --dark-color: #23272A;
    --success-color: #57F287;
    --warning-color: #FEE75C;
    --danger-color: #ED4245;
    --info-color: #5865F2;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* RTL Support */
body[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

body[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

body[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Navbar */
.navbar-brand img {
    margin-right: 0.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--light-color);
    padding: 5rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 1rem 1rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Server Icon Placeholder */
.server-icon-placeholder {
    width: 80px;
    height: 80px;
    background-color: var(--secondary-color);
    color: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    margin: 0 auto;
}

/* Dashboard Sidebar */
.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.list-group-item-action:hover {
    background-color: rgba(88, 101, 242, 0.1);
}

/* Forms */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(88, 101, 242, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #4752c4;
    border-color: #4752c4;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Footer */
footer {
    margin-top: 3rem;
}

footer a {
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
}

/* Custom Switch Styling */
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
}

/* Code Blocks */
code {
    background-color: #f1f1f1;
    color: var(--accent-color);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
