/**
 * CS Bot - نموذج إعدادات السيرفر
 * لحفظ إعدادات كل سيرفر في قاعدة البيانات
 */

const mongoose = require('mongoose');

const guildSettingsSchema = new mongoose.Schema({
    // معرف السيرفر
    guildId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },

    // إعدادات الترحيب
    welcome: {
        enabled: {
            type: Boolean,
            default: false
        },
        channel: {
            type: String,
            default: null
        },
        message: {
            type: String,
            default: 'مرحباً {user} في {server}!'
        },
        embed: {
            type: Boolean,
            default: false
        },
        color: {
            type: String,
            default: '#667eea'
        }
    },

    // إعدادات المغادرة
    leave: {
        enabled: {
            type: Boolean,
            default: false
        },
        channel: {
            type: String,
            default: null
        },
        message: {
            type: String,
            default: 'وداعاً {user}!'
        },
        embed: {
            type: <PERSON>olean,
            default: false
        },
        color: {
            type: String,
            default: '#dc3545'
        }
    },

    // إعدادات اللوجات
    logs: {
        enabled: {
            type: Boolean,
            default: false
        },
        channel: {
            type: String,
            default: null
        },
        events: {
            memberJoin: { type: Boolean, default: true },
            memberLeave: { type: Boolean, default: true },
            messageDelete: { type: Boolean, default: true },
            messageEdit: { type: Boolean, default: true },
            channelCreate: { type: Boolean, default: true },
            channelDelete: { type: Boolean, default: true },
            roleCreate: { type: Boolean, default: true },
            roleDelete: { type: Boolean, default: true },
            memberBan: { type: Boolean, default: true },
            memberUnban: { type: Boolean, default: true }
        }
    },

    // إعدادات مانع السبام
    antispam: {
        enabled: {
            type: Boolean,
            default: false
        },
        maxMessages: {
            type: Number,
            default: 5
        },
        timeWindow: {
            type: Number,
            default: 5000
        },
        punishment: {
            type: String,
            enum: ['mute', 'kick', 'ban', 'warn'],
            default: 'mute'
        },
        duration: {
            type: Number,
            default: 300000 // 5 دقائق
        }
    },

    // إعدادات الرتب التلقائية
    autorole: {
        enabled: {
            type: Boolean,
            default: false
        },
        roles: [{
            type: String
        }],
        bots: {
            enabled: { type: Boolean, default: false },
            roles: [{ type: String }]
        }
    },

    // إعدادات الردود التلقائية
    autoresponse: {
        enabled: {
            type: Boolean,
            default: false
        },
        responses: [{
            trigger: String,
            response: String,
            exact: { type: Boolean, default: false },
            deleteOriginal: { type: Boolean, default: false }
        }]
    },

    // إعدادات الاقتصاد
    economy: {
        enabled: {
            type: Boolean,
            default: false
        },
        currency: {
            type: String,
            default: 'نقاط'
        },
        dailyAmount: {
            type: Number,
            default: 100
        },
        workAmount: {
            type: Number,
            default: 50
        }
    },

    // إعدادات الإدارة
    moderation: {
        enabled: {
            type: Boolean,
            default: true
        },
        muteRole: {
            type: String,
            default: null
        },
        autoMod: {
            enabled: { type: Boolean, default: false },
            badWords: [{ type: String }],
            inviteLinks: { type: Boolean, default: false },
            excessiveCaps: { type: Boolean, default: false },
            excessiveEmojis: { type: Boolean, default: false }
        }
    },

    // إعدادات التذاكر
    tickets: {
        enabled: {
            type: Boolean,
            default: false
        },
        category: {
            type: String,
            default: null
        },
        supportRole: {
            type: String,
            default: null
        },
        transcripts: {
            enabled: { type: Boolean, default: true },
            channel: { type: String, default: null }
        }
    },

    // إعدادات البريميوم
    premium: {
        enabled: {
            type: Boolean,
            default: false
        },
        features: [{
            type: String
        }],
        expiresAt: {
            type: Date,
            default: null
        }
    },

    // إعدادات عامة
    general: {
        prefix: {
            type: String,
            default: '!'
        },
        language: {
            type: String,
            enum: ['ar', 'en'],
            default: 'ar'
        },
        timezone: {
            type: String,
            default: 'Asia/Riyadh'
        }
    },

    // معلومات التحديث
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    updatedBy: {
        type: String,
        default: null
    }
}, {
    timestamps: true,
    collection: 'guild_settings'
});

// فهارس للبحث السريع
guildSettingsSchema.index({ guildId: 1 });
guildSettingsSchema.index({ 'premium.enabled': 1 });
guildSettingsSchema.index({ lastUpdated: -1 });

// دوال مساعدة
guildSettingsSchema.methods.updateSetting = function(path, value, userId) {
    this.set(path, value);
    this.lastUpdated = new Date();
    this.updatedBy = userId;
    return this.save();
};

guildSettingsSchema.methods.toggleFeature = function(feature, userId) {
    const currentValue = this.get(feature + '.enabled');
    this.set(feature + '.enabled', !currentValue);
    this.lastUpdated = new Date();
    this.updatedBy = userId;
    return this.save();
};

// دوال ثابتة
guildSettingsSchema.statics.getOrCreate = async function(guildId) {
    let settings = await this.findOne({ guildId });
    
    if (!settings) {
        settings = new this({ guildId });
        await settings.save();
    }
    
    return settings;
};

guildSettingsSchema.statics.updateGuildSetting = async function(guildId, path, value, userId) {
    const settings = await this.getOrCreate(guildId);
    return settings.updateSetting(path, value, userId);
};

// تجنب إعادة تعريف النموذج إذا كان موجوداً بالفعل
const GuildSettings = mongoose.models.GuildSettings || mongoose.model('GuildSettings', guildSettingsSchema);

module.exports = GuildSettings;
