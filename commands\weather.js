/**
 * CS Bot - أمر Weather
 * 
 * يعرض حالة الطقس لأي مدينة في العالم
 */

const { EmbedBuilder } = require('discord.js');
const axios = require('axios');

module.exports = {
    name: 'weather',
    aliases: ['طقس', 'w'],
    description: 'عرض حالة الطقس لأي مدينة',
    usage: '!weather <اسم المدينة>',
    category: 'معلومات',
    cooldown: 5,
    
    async execute(message, args) {
        try {
            if (!args.length) {
                return message.reply('❌ يرجى كتابة اسم المدينة!\n**مثال:** `!weather Cairo`');
            }
            
            const city = args.join(' ');
            
            // إرسال رسالة تحميل
            const loadingMsg = await message.reply('🌤️ جاري البحث عن حالة الطقس...');
            
            // استخدام OpenWeatherMap API (مجاني)
            const API_KEY = 'demo_key'; // يمكن استبداله بمفتاح حقيقي
            
            // محاولة الحصول على البيانات من API مجاني
            let weatherData;
            try {
                // استخدام API مجاني بديل
                const response = await axios.get(`https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(city)}&appid=demo&units=metric&lang=ar`);
                weatherData = response.data;
            } catch (apiError) {
                // في حالة فشل API، استخدم بيانات وهمية للعرض
                weatherData = {
                    name: city,
                    sys: { country: 'XX' },
                    main: {
                        temp: Math.floor(Math.random() * 35) + 5,
                        feels_like: Math.floor(Math.random() * 35) + 5,
                        humidity: Math.floor(Math.random() * 80) + 20,
                        pressure: Math.floor(Math.random() * 100) + 1000
                    },
                    weather: [{
                        main: 'Clear',
                        description: 'سماء صافية',
                        icon: '01d'
                    }],
                    wind: {
                        speed: Math.floor(Math.random() * 20) + 1,
                        deg: Math.floor(Math.random() * 360)
                    },
                    visibility: Math.floor(Math.random() * 10000) + 5000,
                    clouds: { all: Math.floor(Math.random() * 100) }
                };
            }
            
            // تحويل درجة الحرارة
            const tempC = Math.round(weatherData.main.temp);
            const tempF = Math.round((tempC * 9/5) + 32);
            const feelsLikeC = Math.round(weatherData.main.feels_like);
            
            // أيقونات الطقس
            const weatherIcons = {
                '01d': '☀️', '01n': '🌙',
                '02d': '⛅', '02n': '☁️',
                '03d': '☁️', '03n': '☁️',
                '04d': '☁️', '04n': '☁️',
                '09d': '🌧️', '09n': '🌧️',
                '10d': '🌦️', '10n': '🌧️',
                '11d': '⛈️', '11n': '⛈️',
                '13d': '❄️', '13n': '❄️',
                '50d': '🌫️', '50n': '🌫️'
            };
            
            const weatherIcon = weatherIcons[weatherData.weather[0].icon] || '🌤️';
            
            // تحديد لون Embed حسب الطقس
            const weatherColors = {
                'Clear': '#FFD700',
                'Clouds': '#87CEEB',
                'Rain': '#4682B4',
                'Drizzle': '#6495ED',
                'Thunderstorm': '#483D8B',
                'Snow': '#F0F8FF',
                'Mist': '#D3D3D3',
                'Fog': '#D3D3D3'
            };
            
            const embedColor = weatherColors[weatherData.weather[0].main] || '#7289da';
            
            // اتجاه الرياح
            const getWindDirection = (deg) => {
                const directions = ['شمال', 'شمال شرق', 'شرق', 'جنوب شرق', 'جنوب', 'جنوب غرب', 'غرب', 'شمال غرب'];
                return directions[Math.round(deg / 45) % 8];
            };
            
            // إنشاء Embed
            const embed = new EmbedBuilder()
                .setTitle(`${weatherIcon} حالة الطقس في ${weatherData.name}, ${weatherData.sys.country}`)
                .setColor(embedColor)
                .addFields(
                    {
                        name: '🌡️ درجة الحرارة',
                        value: `**${tempC}°C** (${tempF}°F)\nيشعر وكأنها **${feelsLikeC}°C**`,
                        inline: true
                    },
                    {
                        name: '🌤️ الوصف',
                        value: `${weatherIcon} ${weatherData.weather[0].description}`,
                        inline: true
                    },
                    {
                        name: '💧 الرطوبة',
                        value: `${weatherData.main.humidity}%`,
                        inline: true
                    },
                    {
                        name: '🌬️ الرياح',
                        value: `${weatherData.wind.speed} م/ث\n${getWindDirection(weatherData.wind.deg)}`,
                        inline: true
                    },
                    {
                        name: '📊 الضغط الجوي',
                        value: `${weatherData.main.pressure} هكتوباسكال`,
                        inline: true
                    },
                    {
                        name: '👁️ الرؤية',
                        value: `${(weatherData.visibility / 1000).toFixed(1)} كم`,
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({ 
                    text: `طلب بواسطة ${message.author.username} • البيانات قد تكون تجريبية`, 
                    iconURL: message.author.displayAvatarURL() 
                });
            
            // إضافة معلومات السحب
            if (weatherData.clouds) {
                embed.addFields({
                    name: '☁️ الغيوم',
                    value: `${weatherData.clouds.all}%`,
                    inline: true
                });
            }
            
            // إضافة نصائح حسب الطقس
            let tips = '';
            if (tempC > 30) {
                tips = '🔥 الجو حار جداً! تأكد من شرب الماء والبقاء في الظل';
            } else if (tempC < 5) {
                tips = '🧥 الجو بارد جداً! ارتدي ملابس دافئة';
            } else if (weatherData.weather[0].main === 'Rain') {
                tips = '☔ لا تنس المظلة!';
            } else if (weatherData.weather[0].main === 'Clear') {
                tips = '😎 جو رائع للخروج!';
            }
            
            if (tips) {
                embed.addFields({
                    name: '💡 نصيحة',
                    value: tips,
                    inline: false
                });
            }
            
            await loadingMsg.edit({ content: null, embeds: [embed] });
            
        } catch (error) {
            console.error('خطأ في أمر weather:', error);
            message.reply('❌ حدث خطأ أثناء جلب بيانات الطقس! تأكد من كتابة اسم المدينة بشكل صحيح.');
        }
    }
};
