<%- include(__dirname + '/../partials/header') %>

<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-server"></i> <%= guild.name %></h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="/dashboard/server/<%= guild.id %>" class="list-group-item list-group-item-action">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/welcome" class="list-group-item list-group-item-action active">
                        <i class="fas fa-door-open"></i> الترحيب
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/logs" class="list-group-item list-group-item-action">
                        <i class="fas fa-history"></i> اللوجات
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/tickets" class="list-group-item list-group-item-action">
                        <i class="fas fa-ticket-alt"></i> التذاكر
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/autorole" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-tag"></i> الرتب التلقائية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/autoresponse" class="list-group-item list-group-item-action">
                        <i class="fas fa-reply-all"></i> الردود التلقائية
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/economy" class="list-group-item list-group-item-action">
                        <i class="fas fa-coins"></i> الاقتصاد
                    </a>
                    <a href="/dashboard/server/<%= guild.id %>/antispam" class="list-group-item list-group-item-action">
                        <i class="fas fa-shield-alt"></i> مانع السبام
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-door-open"></i> إعدادات الترحيب</h5>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="welcomeToggle" <%= settings.welcome && settings.welcome.enabled ? 'checked' : '' %>>
                        <label class="form-check-label text-white" for="welcomeToggle">تفعيل</label>
                    </div>
                </div>
                <div class="card-body">
                    <form id="welcomeSettingsForm">
                        <div class="mb-3">
                            <label for="welcomeChannel" class="form-label">قناة الترحيب</label>
                            <select class="form-select" id="welcomeChannel">
                                <option value="">-- اختر قناة --</option>
                                <!-- سيتم ملء هذه القائمة بالقنوات من خلال JavaScript -->
                            </select>
                            <div class="form-text">القناة التي سيتم إرسال رسائل الترحيب فيها</div>
                        </div>

                        <div class="mb-3">
                            <label for="welcomeMessage" class="form-label">رسالة الترحيب النصية</label>
                            <textarea class="form-control" id="welcomeMessage" rows="3"><%= settings.welcome ? settings.welcome.message : 'مرحباً {user} في سيرفر {server}! أنت العضو رقم {count}.' %></textarea>
                            <div class="form-text">
                                يمكنك استخدام المتغيرات التالية:
                                <ul>
                                    <li><code>{user}</code> - اسم المستخدم</li>
                                    <li><code>{server}</code> - اسم السيرفر</li>
                                    <li><code>{count}</code> - رقم العضو</li>
                                    <li><code>{mention}</code> - منشن للمستخدم</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="embedEnabled" <%= settings.welcome && settings.welcome.embedEnabled ? 'checked' : '' %>>
                                <label class="form-check-label" for="embedEnabled">
                                    استخدام Embed للترحيب
                                </label>
                            </div>
                        </div>

                        <div id="embedSettings" class="<%= settings.welcome && settings.welcome.embedEnabled ? '' : 'd-none' %>">
                            <div class="mb-3">
                                <label for="embedColor" class="form-label">لون الـ Embed</label>
                                <input type="color" class="form-control form-control-color" id="embedColor" value="<%= settings.welcome ? settings.welcome.embedColor : '#2F3136' %>">
                            </div>

                            <div class="mb-3">
                                <label for="embedTitle" class="form-label">عنوان الـ Embed</label>
                                <input type="text" class="form-control" id="embedTitle" value="<%= settings.welcome ? settings.welcome.embedTitle : 'عضو جديد!' %>">
                            </div>

                            <div class="mb-3">
                                <label for="embedDescription" class="form-label">وصف الـ Embed</label>
                                <textarea class="form-control" id="embedDescription" rows="3"><%= settings.welcome ? settings.welcome.embedDescription : 'مرحباً {user} في سيرفر {server}! أنت العضو رقم {count}.' %></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="embedThumbnail" <%= settings.welcome && settings.welcome.embedThumbnail ? 'checked' : '' %>>
                                    <label class="form-check-label" for="embedThumbnail">
                                        إظهار صورة المستخدم كـ Thumbnail
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="embedFooter" class="form-label">تذييل الـ Embed</label>
                                <input type="text" class="form-control" id="embedFooter" value="<%= settings.welcome ? settings.welcome.embedFooter : 'CS Bot' %>">
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" id="testWelcomeBtn" class="btn btn-secondary">
                                <i class="fas fa-vial"></i> اختبار الترحيب
                            </button>
                            <button type="button" id="saveWelcomeBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> معلومات مفيدة</h5>
                </div>
                <div class="card-body">
                    <p>نظام الترحيب يسمح لك بإرسال رسالة ترحيبية تلقائية للأعضاء الجدد في سيرفرك. يمكنك تخصيص الرسالة بالكامل، واستخدام Embed للحصول على مظهر أكثر احترافية.</p>

                    <h6>نصائح:</h6>
                    <ul>
                        <li>استخدم المتغيرات مثل <code>{user}</code> و <code>{server}</code> لجعل الرسالة شخصية.</li>
                        <li>يمكنك استخدام Embed للحصول على مظهر أكثر احترافية.</li>
                        <li>تأكد من أن البوت لديه صلاحيات الكتابة في قناة الترحيب.</li>
                        <li>يمكنك اختبار الترحيب قبل حفظ الإعدادات للتأكد من أنه يظهر كما تريد.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom JS for this page -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تبديل إعدادات الـ Embed
        document.getElementById('embedEnabled').addEventListener('change', function() {
            const embedSettings = document.getElementById('embedSettings');
            if (this.checked) {
                embedSettings.classList.remove('d-none');
            } else {
                embedSettings.classList.add('d-none');
            }
        });

        // جلب قنوات السيرفر
        fetch('/api/guilds/<%= guild.id %>/channels')
            .then(response => response.json())
            .then(channels => {
                const welcomeChannel = document.getElementById('welcomeChannel');
                const currentChannel = '<%= settings.welcome ? settings.welcome.channel : "" %>';

                channels.forEach(channel => {
                    if (channel.type === 0) { // قناة نصية فقط
                        const option = document.createElement('option');
                        option.value = channel.id;
                        option.textContent = '#' + channel.name;
                        if (channel.id === currentChannel) {
                            option.selected = true;
                        }
                        welcomeChannel.appendChild(option);
                    }
                });
            })
            .catch(error => {
                console.error('Error fetching channels:', error);
            });

        // حفظ إعدادات الترحيب
        document.getElementById('saveWelcomeBtn').addEventListener('click', function() {
            const enabled = document.getElementById('welcomeToggle').checked;
            const channel = document.getElementById('welcomeChannel').value;
            const message = document.getElementById('welcomeMessage').value;
            const embedEnabled = document.getElementById('embedEnabled').checked;
            const embedColor = document.getElementById('embedColor').value;
            const embedTitle = document.getElementById('embedTitle').value;
            const embedDescription = document.getElementById('embedDescription').value;
            const embedThumbnail = document.getElementById('embedThumbnail').checked;
            const embedFooter = document.getElementById('embedFooter').value;

            // إرسال البيانات إلى الخادم
            fetch('/api/guilds/<%= guild.id %>/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    welcome: {
                        enabled,
                        channel,
                        message,
                        embedEnabled,
                        embedColor,
                        embedTitle,
                        embedDescription,
                        embedThumbnail,
                        embedFooter
                    }
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم حفظ إعدادات الترحيب بنجاح!');
                } else {
                    alert('حدث خطأ أثناء حفظ إعدادات الترحيب: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ إعدادات الترحيب');
            });
        });

        // اختبار الترحيب
        document.getElementById('testWelcomeBtn').addEventListener('click', function() {
            fetch('/api/guilds/<%= guild.id %>/test-welcome', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    channel: document.getElementById('welcomeChannel').value,
                    message: document.getElementById('welcomeMessage').value,
                    embedEnabled: document.getElementById('embedEnabled').checked,
                    embedColor: document.getElementById('embedColor').value,
                    embedTitle: document.getElementById('embedTitle').value,
                    embedDescription: document.getElementById('embedDescription').value,
                    embedThumbnail: document.getElementById('embedThumbnail').checked,
                    embedFooter: document.getElementById('embedFooter').value
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إرسال رسالة الترحيب التجريبية!');
                } else {
                    alert('حدث خطأ أثناء اختبار الترحيب: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء اختبار الترحيب');
            });
        });
    });
</script>

<%- include(__dirname + '/../partials/footer') %>
