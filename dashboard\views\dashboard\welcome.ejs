<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الترحيب | CS Bot Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/css/dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .welcome-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .settings-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .channel-selector {
            position: relative;
        }

        .channel-dropdown {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .channel-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: background 0.3s ease;
        }

        .channel-item:hover {
            background: #f8f9fa;
        }

        .channel-item:last-child {
            border-bottom: none;
        }

        .preview-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
        }

        .message-preview {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            border-left: 4px solid #667eea;
            margin-top: 1rem;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <%- include('../partials/sidebar') %>

    <div class="main-content">
        <%- include('../partials/navbar') %>

        <div class="container-fluid px-4 py-4">
            <!-- رأس الصفحة -->
            <div class="welcome-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="text-white mb-2">
                            <i class="fas fa-hand-wave me-2"></i>
                            إعدادات الترحيب
                        </h1>
                        <p class="text-white-50 mb-0">
                            اضبط رسائل الترحيب للأعضاء الجدد في سيرفر <%= guild ? guild.name : 'السيرفر' %>
                        </p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-light" onclick="saveSettings()">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- إعدادات الترحيب -->
                <div class="col-lg-8">
                    <div class="settings-card">
                        <h3 class="mb-4">
                            <i class="fas fa-cog text-primary me-2"></i>
                            إعدادات الترحيب
                        </h3>

                        <!-- تفعيل/إلغاء الترحيب -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">حالة الترحيب</label>
                                <div class="d-flex align-items-center gap-3">
                                    <label class="toggle-switch">
                                        <input type="checkbox" id="welcomeEnabled" 
                                               <%= settings && settings.welcome && settings.welcome.enabled ? 'checked' : '' %>
                                               onchange="toggleWelcome(this.checked)">
                                        <span class="slider"></span>
                                    </label>
                                    <span id="welcomeStatus">
                                        <%= settings && settings.welcome && settings.welcome.enabled ? 'مفعل' : 'معطل' %>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار القناة -->
                        <div class="mb-4">
                            <label for="welcomeChannel" class="form-label">قناة الترحيب</label>
                            <div class="channel-selector">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-hashtag"></i>
                                    </span>
                                    <input type="text" class="form-control" id="welcomeChannelInput" 
                                           placeholder="اختر قناة الترحيب..." readonly onclick="toggleChannelDropdown()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="toggleChannelDropdown()">
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                                <div class="channel-dropdown" id="channelDropdown">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- رسالة الترحيب -->
                        <div class="mb-4">
                            <label for="welcomeMessage" class="form-label">رسالة الترحيب</label>
                            <textarea class="form-control" id="welcomeMessage" rows="4" 
                                      placeholder="مرحباً {user} في {server}! نتمنى لك إقامة ممتعة معنا."><%= settings && settings.welcome ? settings.welcome.message : 'مرحباً {user} في {server}!' %></textarea>
                            <div class="form-text">
                                يمكنك استخدام المتغيرات التالية:
                                <code>{user}</code> - اسم المستخدم،
                                <code>{server}</code> - اسم السيرفر،
                                <code>{memberCount}</code> - عدد الأعضاء
                            </div>
                        </div>

                        <!-- نوع الرسالة -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">نوع الرسالة</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="messageType" id="textMessage" value="text" checked>
                                    <label class="form-check-label" for="textMessage">
                                        رسالة نصية
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="messageType" id="embedMessage" value="embed">
                                    <label class="form-check-label" for="embedMessage">
                                        رسالة مدمجة (Embed)
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="embedColor" class="form-label">لون الرسالة المدمجة</label>
                                <input type="color" class="form-control form-control-color" id="embedColor" 
                                       value="<%= settings && settings.welcome ? settings.welcome.color : '#667eea' %>">
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                            <button class="btn btn-outline-secondary" onclick="testWelcome()">
                                <i class="fas fa-paper-plane me-2"></i>اختبار الرسالة
                            </button>
                            <button class="btn btn-outline-danger" onclick="resetSettings()">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <!-- معاينة الرسالة -->
                <div class="col-lg-4">
                    <div class="preview-card">
                        <h4 class="mb-3">
                            <i class="fas fa-eye text-info me-2"></i>
                            معاينة الرسالة
                        </h4>
                        <div class="message-preview" id="messagePreview">
                            <div class="d-flex align-items-center mb-2">
                                <img src="https://cdn.discordapp.com/embed/avatars/0.png" 
                                     alt="Bot" class="rounded-circle me-2" width="24" height="24">
                                <strong>CS Bot</strong>
                                <small class="text-muted ms-auto">الآن</small>
                            </div>
                            <div id="previewContent">
                                مرحباً <strong>@المستخدم</strong> في <strong><%= guild ? guild.name : 'السيرفر' %></strong>!
                            </div>
                        </div>

                        <!-- نصائح -->
                        <div class="mt-4">
                            <h6><i class="fas fa-lightbulb text-warning me-2"></i>نصائح</h6>
                            <ul class="list-unstyled small text-muted">
                                <li><i class="fas fa-check text-success me-1"></i> استخدم رسائل ودودة ومرحبة</li>
                                <li><i class="fas fa-check text-success me-1"></i> اذكر قوانين السيرفر المهمة</li>
                                <li><i class="fas fa-check text-success me-1"></i> أضف روابط مفيدة للأعضاء الجدد</li>
                                <li><i class="fas fa-check text-success me-1"></i> اجعل الرسالة قصيرة ومفيدة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedChannel = '<%= settings && settings.welcome ? settings.welcome.channel : "" %>';
        let channels = [];

        // تحميل القنوات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadChannels();
            updatePreview();
            
            // تحديث المعاينة عند تغيير النص
            document.getElementById('welcomeMessage').addEventListener('input', updatePreview);
            document.getElementById('embedColor').addEventListener('change', updatePreview);
            document.querySelectorAll('input[name="messageType"]').forEach(radio => {
                radio.addEventListener('change', updatePreview);
            });
        });

        // تحميل قنوات السيرفر
        async function loadChannels() {
            try {
                // قنوات تجريبية - في التطبيق الحقيقي ستأتي من API
                channels = [
                    { id: '123456789', name: 'general', type: 0 },
                    { id: '123456790', name: 'welcome', type: 0 },
                    { id: '123456791', name: 'announcements', type: 0 },
                    { id: '123456792', name: 'chat', type: 0 },
                    { id: '123456793', name: 'off-topic', type: 0 }
                ];

                updateChannelDropdown();
                
                // تحديد القناة المحفوظة
                if (selectedChannel) {
                    const channel = channels.find(c => c.id === selectedChannel);
                    if (channel) {
                        document.getElementById('welcomeChannelInput').value = `# ${channel.name}`;
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل القنوات:', error);
                showToast('خطأ في تحميل قنوات السيرفر', 'error');
            }
        }

        // تحديث قائمة القنوات
        function updateChannelDropdown() {
            const dropdown = document.getElementById('channelDropdown');
            dropdown.innerHTML = channels
                .filter(channel => channel.type === 0) // Text channels only
                .map(channel => `
                    <div class="channel-item" onclick="selectChannel('${channel.id}', '${channel.name}')">
                        <i class="fas fa-hashtag text-muted"></i>
                        <span>${channel.name}</span>
                    </div>
                `).join('');
        }

        // تبديل عرض قائمة القنوات
        function toggleChannelDropdown() {
            const dropdown = document.getElementById('channelDropdown');
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }

        // اختيار قناة
        function selectChannel(channelId, channelName) {
            selectedChannel = channelId;
            document.getElementById('welcomeChannelInput').value = `# ${channelName}`;
            toggleChannelDropdown();
        }

        // تبديل حالة الترحيب
        function toggleWelcome(enabled) {
            document.getElementById('welcomeStatus').textContent = enabled ? 'مفعل' : 'معطل';
        }

        // تحديث معاينة الرسالة
        function updatePreview() {
            const message = document.getElementById('welcomeMessage').value;
            const messageType = document.querySelector('input[name="messageType"]:checked').value;
            const color = document.getElementById('embedColor').value;
            const preview = document.getElementById('previewContent');

            let processedMessage = message
                .replace(/{user}/g, '<strong>@المستخدم</strong>')
                .replace(/{server}/g, '<strong><%= guild ? guild.name : "السيرفر" %></strong>')
                .replace(/{memberCount}/g, '<strong>150</strong>');

            if (messageType === 'embed') {
                preview.innerHTML = `
                    <div class="border-start border-3 ps-3" style="border-color: ${color} !important;">
                        <div style="color: ${color}; font-weight: 600; margin-bottom: 0.5rem;">
                            مرحباً بك!
                        </div>
                        <div>${processedMessage}</div>
                    </div>
                `;
            } else {
                preview.innerHTML = processedMessage;
            }
        }

        // حفظ الإعدادات
        async function saveSettings() {
            try {
                if (!selectedChannel && document.getElementById('welcomeEnabled').checked) {
                    showToast('يرجى اختيار قناة الترحيب', 'warning');
                    return;
                }

                const settings = {
                    enabled: document.getElementById('welcomeEnabled').checked,
                    channel: selectedChannel,
                    message: document.getElementById('welcomeMessage').value,
                    embed: document.querySelector('input[name="messageType"]:checked').value === 'embed',
                    color: document.getElementById('embedColor').value
                };

                // هنا يمكن إرسال الإعدادات للخادم
                // const response = await fetch('/api/settings/welcome', { ... });
                
                showToast('تم حفظ إعدادات الترحيب بنجاح!', 'success');
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showToast('حدث خطأ في حفظ الإعدادات', 'error');
            }
        }

        // اختبار رسالة الترحيب
        function testWelcome() {
            if (!selectedChannel) {
                showToast('يرجى اختيار قناة الترحيب أولاً', 'warning');
                return;
            }
            showToast('تم إرسال رسالة اختبار للقناة المحددة', 'success');
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات الترحيب؟')) {
                document.getElementById('welcomeEnabled').checked = false;
                document.getElementById('welcomeMessage').value = 'مرحباً {user} في {server}!';
                document.getElementById('welcomeChannelInput').value = '';
                document.getElementById('textMessage').checked = true;
                document.getElementById('embedColor').value = '#667eea';
                selectedChannel = '';
                toggleWelcome(false);
                updatePreview();
                showToast('تم إعادة تعيين الإعدادات', 'success');
            }
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'warning' ? 'exclamation-triangle' : 'exclamation'}-circle me-2"></i>
                ${message}
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('channelDropdown');
            const selector = document.querySelector('.channel-selector');
            
            if (!selector.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
