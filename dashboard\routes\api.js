/**
 * CS Bot - مسارات API
 *
 * هذا الملف يحتوي على مسارات API للتفاعل مع البوت
 */

const express = require('express');
const router = express.Router();
const { getGuildSettings, saveGuildSettings } = require('../../models/guild');

// التحقق من المصادقة للـ API
const checkAuth = (req, res, next) => {
    if (req.isAuthenticated()) return next();
    return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
};

// الحصول على إعدادات السيرفر
router.get('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لإعدادات هذا السيرفر' });
    }

    try {
        const settings = await getGuildSettings(req.params.guildId);
        res.json(settings);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإعدادات' });
    }
});

// حفظ إعدادات السيرفر
router.post('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }

    try {
        await saveGuildSettings(req.params.guildId, req.body);
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

// اختبار الترحيب
router.post('/guilds/:guildId/test-welcome', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    try {
        // هنا يتم استدعاء وظيفة اختبار الترحيب من البوت
        // سيتم تنفيذها لاحقاً
        res.json({ success: true, message: 'تم إرسال رسالة الترحيب التجريبية' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء اختبار الترحيب' });
    }
});

// الحصول على إحصائيات البوت الأسطورية
router.get('/stats', async (req, res) => {
    try {
        const client = req.app.locals.client;

        // حساب وقت التشغيل
        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) return `${days}d ${hours % 24}h`;
            if (hours > 0) return `${hours}h ${minutes % 60}m`;
            if (minutes > 0) return `${minutes}m`;
            return `${seconds}s`;
        }

        const stats = {
            servers: client ? client.guilds.cache.size : 0,
            users: client ? client.users.cache.size : 0,
            commands: client ? client.stats.commandsExecuted : 0,
            uptime: client ? formatUptime(Date.now() - client.stats.startTime) : '0s',
            messagesProcessed: client ? client.stats.messagesProcessed : 0,
            premiumUsers: client ? client.stats.premiumUsers : 0
        };

        res.json(stats);
    } catch (err) {
        console.error('خطأ في جلب الإحصائيات:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// تحديث إعدادات الملف الشخصي
router.post('/profile/update', checkAuth, async (req, res) => {
    try {
        const { getUserData } = require('../../models/user');
        const { language, notifications } = req.body;

        // تحديث بيانات المستخدم
        const userData = await getUserData(req.user.id, req.user.username, req.user.discriminator, req.user.avatar);

        if (language) {
            userData.language = language;
        }

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء تحديث الملف الشخصي' });
    }
});

// الحصول على قنوات السيرفر
router.get('/guilds/:guildId/channels', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لقنوات هذا السيرفر' });
    }

    try {
        // هنا سيتم جلب القنوات من البوت
        // مؤقتاً سنرجع قائمة فارغة
        const channels = [];
        res.json(channels);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب القنوات' });
    }
});

// التحقق من صلاحيات الأدمن
const checkAdmin = (req, res, next) => {
    if (!req.isAuthenticated()) {
        return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
    }

    const config = req.app.locals.config;
    if (config.owner.id !== req.user.id) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لهذه الصفحة' });
    }

    return next();
};

// إحصائيات الأدمن
router.get('/admin/stats', checkAdmin, async (req, res) => {
    try {
        const stats = {
            servers: 0,
            users: 0,
            commands: 0,
            uptime: '0m'
        };

        res.json(stats);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// إضافة بريميوم
router.post('/admin/premium/add', checkAdmin, async (req, res) => {
    try {
        const { userId, duration } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        // حساب تاريخ الانتهاء
        const now = new Date();
        let expiresAt = new Date(now);

        if (duration.endsWith('d')) {
            const days = parseInt(duration.replace('d', ''));
            expiresAt.setDate(expiresAt.getDate() + days);
        }

        userData.premium = {
            enabled: true,
            expiresAt: expiresAt,
            tier: 'basic'
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إضافة البريميوم' });
    }
});

// إزالة بريميوم
router.post('/admin/premium/remove', checkAdmin, async (req, res) => {
    try {
        const { userId } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        userData.premium = {
            enabled: false,
            expiresAt: null,
            tier: null
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إزالة البريميوم' });
    }
});

// إرسال إعلان
router.post('/admin/announcement', checkAdmin, async (req, res) => {
    try {
        const { title, message, sendToAll } = req.body;

        // هنا سيتم إرسال الإعلان عبر البوت
        // مؤقتاً سنرجع نجاح العملية

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إرسال الإعلان' });
    }
});

// الحصول على قائمة السيرفرات
router.get('/servers', async (req, res) => {
    try {
        const client = req.app.locals.client;

        if (!client || !client.guilds) {
            return res.json({ servers: [] });
        }

        const servers = client.guilds.cache.map(guild => {
            // التحقق من صلاحيات المستخدم (مؤقت - سيتم تحسينه لاحقاً)
            const canManage = true; // مؤقت

            return {
                id: guild.id,
                name: guild.name,
                icon: guild.iconURL(),
                memberCount: guild.memberCount,
                channelCount: guild.channels.cache.size,
                roleCount: guild.roles.cache.size,
                owner: guild.ownerId === req.user?.id,
                canManage: canManage
            };
        });

        res.json({ servers: servers });
    } catch (err) {
        console.error('خطأ في جلب السيرفرات:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب السيرفرات' });
    }
});

// الحصول على معلومات سيرفر محدد
router.get('/server/:guildId', async (req, res) => {
    try {
        const client = req.app.locals.client;
        const guildId = req.params.guildId;

        if (!client || !client.guilds) {
            return res.status(404).json({ error: 'البوت غير متصل' });
        }

        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            return res.status(404).json({ error: 'السيرفر غير موجود' });
        }

        const serverInfo = {
            id: guild.id,
            name: guild.name,
            icon: guild.iconURL(),
            memberCount: guild.memberCount,
            channelCount: guild.channels.cache.size,
            roleCount: guild.roles.cache.size,
            owner: guild.ownerId === req.user?.id,
            channels: guild.channels.cache
                .filter(channel => channel.type === 0) // Text channels only
                .map(channel => ({
                    id: channel.id,
                    name: channel.name,
                    type: channel.type
                })),
            roles: guild.roles.cache
                .filter(role => role.id !== guild.id) // Exclude @everyone
                .map(role => ({
                    id: role.id,
                    name: role.name,
                    color: role.hexColor
                }))
        };

        res.json(serverInfo);
    } catch (err) {
        console.error('خطأ في جلب معلومات السيرفر:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب معلومات السيرفر' });
    }
});

module.exports = router;
