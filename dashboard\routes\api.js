/**
 * CS Bot - مسارات API
 *
 * هذا الملف يحتوي على مسارات API للتفاعل مع البوت
 */

const express = require('express');
const router = express.Router();
// const { getGuildSettings, saveGuildSettings } = require('../../models/guild'); // تم تعطيل هذا لأننا نستخدم GuildSettings مباشرة

// التحقق من المصادقة للـ API
const checkAuth = (req, res, next) => {
    if (req.isAuthenticated()) return next();
    return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
};

// الحصول على إعدادات السيرفر
router.get('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لإعدادات هذا السيرفر' });
    }

    try {
        const GuildSettings = require('../../models/GuildSettings');
        const settings = await GuildSettings.getOrCreate(req.params.guildId);
        res.json(settings);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإعدادات' });
    }
});

// حفظ إعدادات السيرفر
router.post('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }

    try {
        const GuildSettings = require('../../models/GuildSettings');
        const settings = await GuildSettings.getOrCreate(req.params.guildId);

        // تحديث الإعدادات
        Object.assign(settings, req.body);
        settings.lastUpdated = new Date();
        settings.updatedBy = req.user.id;

        await settings.save();
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

// اختبار الترحيب
router.post('/guilds/:guildId/test-welcome', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    try {
        // هنا يتم استدعاء وظيفة اختبار الترحيب من البوت
        // سيتم تنفيذها لاحقاً
        res.json({ success: true, message: 'تم إرسال رسالة الترحيب التجريبية' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء اختبار الترحيب' });
    }
});

// الحصول على إحصائيات البوت الأسطورية
router.get('/stats', async (req, res) => {
    try {
        const client = req.app.locals.client;

        // حساب وقت التشغيل
        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) return `${days}d ${hours % 24}h`;
            if (hours > 0) return `${hours}h ${minutes % 60}m`;
            if (minutes > 0) return `${minutes}m`;
            return `${seconds}s`;
        }

        const stats = {
            servers: client ? client.guilds.cache.size : 0,
            users: client ? client.users.cache.size : 0,
            commands: client ? client.stats.commandsExecuted : 0,
            uptime: client ? formatUptime(Date.now() - client.stats.startTime) : '0s',
            messagesProcessed: client ? client.stats.messagesProcessed : 0,
            premiumUsers: client ? client.stats.premiumUsers : 0
        };

        res.json(stats);
    } catch (err) {
        console.error('خطأ في جلب الإحصائيات:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// تحديث إعدادات الملف الشخصي
router.post('/profile/update', checkAuth, async (req, res) => {
    try {
        const { getUserData } = require('../../models/user');
        const { language, notifications } = req.body;

        // تحديث بيانات المستخدم
        const userData = await getUserData(req.user.id, req.user.username, req.user.discriminator, req.user.avatar);

        if (language) {
            userData.language = language;
        }

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء تحديث الملف الشخصي' });
    }
});

// الحصول على قنوات السيرفر
router.get('/guilds/:guildId/channels', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لقنوات هذا السيرفر' });
    }

    try {
        // هنا سيتم جلب القنوات من البوت
        // مؤقتاً سنرجع قائمة فارغة
        const channels = [];
        res.json(channels);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب القنوات' });
    }
});

// التحقق من صلاحيات الأدمن
const checkAdmin = (req, res, next) => {
    if (!req.isAuthenticated()) {
        return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
    }

    const config = req.app.locals.config;
    if (config.owner.id !== req.user.id) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لهذه الصفحة' });
    }

    return next();
};

// إحصائيات الأدمن
router.get('/admin/stats', checkAdmin, async (req, res) => {
    try {
        const stats = {
            servers: 0,
            users: 0,
            commands: 0,
            uptime: '0m'
        };

        res.json(stats);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// إضافة بريميوم
router.post('/admin/premium/add', checkAdmin, async (req, res) => {
    try {
        const { userId, duration } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        // حساب تاريخ الانتهاء
        const now = new Date();
        let expiresAt = new Date(now);

        if (duration.endsWith('d')) {
            const days = parseInt(duration.replace('d', ''));
            expiresAt.setDate(expiresAt.getDate() + days);
        }

        userData.premium = {
            enabled: true,
            expiresAt: expiresAt,
            tier: 'basic'
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إضافة البريميوم' });
    }
});

// إزالة بريميوم
router.post('/admin/premium/remove', checkAdmin, async (req, res) => {
    try {
        const { userId } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        userData.premium = {
            enabled: false,
            expiresAt: null,
            tier: null
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إزالة البريميوم' });
    }
});

// إرسال إعلان
router.post('/admin/announcement', checkAdmin, async (req, res) => {
    try {
        const { title, message, sendToAll } = req.body;

        // هنا سيتم إرسال الإعلان عبر البوت
        // مؤقتاً سنرجع نجاح العملية

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إرسال الإعلان' });
    }
});

// الحصول على قائمة السيرفرات
router.get('/servers', async (req, res) => {
    try {
        const client = req.app.locals.client;

        if (!client || !client.guilds) {
            return res.json({ servers: [] });
        }

        const servers = client.guilds.cache.map(guild => {
            // التحقق من صلاحيات المستخدم (مؤقت - سيتم تحسينه لاحقاً)
            const canManage = true; // مؤقت

            return {
                id: guild.id,
                name: guild.name,
                icon: guild.iconURL(),
                memberCount: guild.memberCount,
                channelCount: guild.channels.cache.size,
                roleCount: guild.roles.cache.size,
                owner: guild.ownerId === req.user?.id,
                canManage: canManage
            };
        });

        res.json({ servers: servers });
    } catch (err) {
        console.error('خطأ في جلب السيرفرات:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب السيرفرات' });
    }
});

// الحصول على معلومات سيرفر محدد
router.get('/server/:guildId', async (req, res) => {
    try {
        const client = req.app.locals.client;
        const guildId = req.params.guildId;

        if (!client || !client.guilds) {
            return res.status(404).json({ error: 'البوت غير متصل' });
        }

        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            return res.status(404).json({ error: 'السيرفر غير موجود' });
        }

        const serverInfo = {
            id: guild.id,
            name: guild.name,
            icon: guild.iconURL(),
            memberCount: guild.memberCount,
            channelCount: guild.channels.cache.size,
            roleCount: guild.roles.cache.size,
            owner: guild.ownerId === req.user?.id,
            channels: guild.channels.cache
                .filter(channel => channel.type === 0) // Text channels only
                .map(channel => ({
                    id: channel.id,
                    name: channel.name,
                    type: channel.type
                })),
            roles: guild.roles.cache
                .filter(role => role.id !== guild.id) // Exclude @everyone
                .map(role => ({
                    id: role.id,
                    name: role.name,
                    color: role.hexColor
                }))
        };

        res.json(serverInfo);
    } catch (err) {
        console.error('خطأ في جلب معلومات السيرفر:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب معلومات السيرفر' });
    }
});

// APIs خاصة بالداشبورد (للمطور فقط)

// Middleware للتحقق من صلاحيات المطور
function checkOwnerAPI(req, res, next) {
    const config = req.app.locals.config;
    const user = req.session?.user || req.user;

    if (user && user.id === config.owner.id) {
        req.user = user;
        return next();
    }
    return res.status(403).json({ error: 'ليس لديك صلاحية للوصول إلى هذا API' });
}

// الحصول على قائمة المستخدمين
router.get('/dashboard/users', checkOwnerAPI, async (req, res) => {
    try {
        const memoryDB = require('../../utils/memoryDB');
        const users = Array.from(memoryDB.users.values());

        res.json({ users: users });
    } catch (err) {
        console.error('خطأ في جلب المستخدمين:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب المستخدمين' });
    }
});

// إعطاء بريميوم
router.post('/dashboard/premium/add', checkOwnerAPI, async (req, res) => {
    try {
        const { userId, duration } = req.body;

        if (!userId || !duration) {
            return res.status(400).json({ error: 'معاملات مفقودة' });
        }

        const memoryDB = require('../../utils/memoryDB');
        const user = await memoryDB.getUser(userId);

        // حساب تاريخ الانتهاء
        const now = new Date();
        const expiresAt = new Date(now);

        const durationMap = {
            '1m': () => expiresAt.setMinutes(expiresAt.getMinutes() + 1),
            '1h': () => expiresAt.setHours(expiresAt.getHours() + 1),
            '1d': () => expiresAt.setDate(expiresAt.getDate() + 1),
            '7d': () => expiresAt.setDate(expiresAt.getDate() + 7),
            '30d': () => expiresAt.setDate(expiresAt.getDate() + 30),
            '365d': () => expiresAt.setDate(expiresAt.getDate() + 365)
        };

        if (durationMap[duration]) {
            durationMap[duration]();
        } else {
            return res.status(400).json({ error: 'مدة غير صحيحة' });
        }

        user.premium = {
            enabled: true,
            expiresAt: expiresAt,
            tier: 'premium',
            addedBy: req.user.id,
            addedAt: new Date()
        };

        await memoryDB.updateUser(userId, user);

        res.json({ success: true, message: 'تم إعطاء البريميوم بنجاح' });
    } catch (err) {
        console.error('خطأ في إعطاء البريميوم:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء إعطاء البريميوم' });
    }
});

// إزالة بريميوم
router.post('/dashboard/premium/remove', checkOwnerAPI, async (req, res) => {
    try {
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'معرف المستخدم مفقود' });
        }

        const memoryDB = require('../../utils/memoryDB');
        const user = await memoryDB.getUser(userId);

        user.premium = {
            enabled: false,
            expiresAt: null,
            tier: null,
            removedBy: req.user.id,
            removedAt: new Date()
        };

        await memoryDB.updateUser(userId, user);

        res.json({ success: true, message: 'تم إزالة البريميوم بنجاح' });
    } catch (err) {
        console.error('خطأ في إزالة البريميوم:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء إزالة البريميوم' });
    }
});

// حظر مستخدم
router.post('/dashboard/ban', checkOwnerAPI, async (req, res) => {
    try {
        const { userId, reason } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'معرف المستخدم مفقود' });
        }

        const memoryDB = require('../../utils/memoryDB');
        const user = await memoryDB.getUser(userId);

        user.banned = true;
        user.banReason = reason || 'لم يتم تحديد سبب';
        user.bannedBy = req.user.id;
        user.bannedAt = new Date();

        await memoryDB.updateUser(userId, user);

        res.json({ success: true, message: 'تم حظر المستخدم بنجاح' });
    } catch (err) {
        console.error('خطأ في حظر المستخدم:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء حظر المستخدم' });
    }
});

// إلغاء حظر مستخدم
router.post('/dashboard/unban', checkOwnerAPI, async (req, res) => {
    try {
        const { userId } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'معرف المستخدم مفقود' });
        }

        const memoryDB = require('../../utils/memoryDB');
        const user = await memoryDB.getUser(userId);

        user.banned = false;
        user.banReason = null;
        user.unbannedBy = req.user.id;
        user.unbannedAt = new Date();

        await memoryDB.updateUser(userId, user);

        res.json({ success: true, message: 'تم إلغاء حظر المستخدم بنجاح' });
    } catch (err) {
        console.error('خطأ في إلغاء حظر المستخدم:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء إلغاء حظر المستخدم' });
    }
});

// إعادة تشغيل البوت
router.post('/restart', checkOwnerAPI, async (req, res) => {
    try {
        res.json({ success: true, message: 'سيتم إعادة تشغيل البوت خلال 5 ثوان' });

        setTimeout(() => {
            console.log('🔄 إعادة تشغيل البوت بواسطة المطور...');
            process.exit(0);
        }, 5000);
    } catch (err) {
        console.error('خطأ في إعادة التشغيل:', err);
        res.status(500).json({ error: 'حدث خطأ أثناء إعادة التشغيل' });
    }
});

// API التحليلات المتقدمة (للمطور فقط)
router.get('/analytics', checkOwnerAPI, async (req, res) => {
    try {
        const bot = req.app.locals.bot;
        const memoryDB = require('../../utils/memoryDB');

        // إحصائيات أساسية
        const totalUsers = bot?.users?.cache?.size || 0;
        const totalServers = bot?.guilds?.cache?.size || 0;
        const totalCommands = memoryDB.stats?.commandsExecuted || 0;
        const premiumUsers = Object.values(memoryDB.users || {}).filter(user =>
            user.premium && user.premium.active
        ).length;

        // بيانات وهمية للرسوم البيانية (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
        const commandsData = [120, 150, 180, 200, 250, 300];
        const usersData = [45, 67, 89, 123];
        const topCommands = [
            { name: 'help', count: 45 },
            { name: 'premium', count: 30 },
            { name: 'stats', count: 15 },
            { name: 'ping', count: 8 },
            { name: 'avatar', count: 2 }
        ];
        const serverActivity = [65, 85, 90, 95, 70, 45];

        // أكثر المستخدمين نشاطاً
        const topUsers = Object.entries(memoryDB.users || {})
            .map(([id, user]) => ({
                rank: 1,
                username: user.username || 'مستخدم',
                commands: user.commandsUsed || 0,
                activity: user.commandsUsed > 100 ? 'عالي' : user.commandsUsed > 50 ? 'متوسط' : 'منخفض'
            }))
            .sort((a, b) => b.commands - a.commands)
            .slice(0, 5)
            .map((user, index) => ({ ...user, rank: index + 1 }));

        // النشاط الأخير
        const recentActivity = [
            { time: 'منذ دقيقتين', action: 'تم تنفيذ أمر !help', user: 'مستخدم1' },
            { time: 'منذ 5 دقائق', action: 'انضمام مستخدم جديد', user: 'مستخدم2' },
            { time: 'منذ 10 دقائق', action: 'تم تنفيذ أمر !premium', user: 'مستخدم3' },
            { time: 'منذ 15 دقيقة', action: 'تم تنفيذ أمر !stats', user: 'مستخدم4' }
        ];

        res.json({
            totalUsers,
            totalServers,
            totalCommands,
            premiumUsers,
            activeServers: totalServers,
            commandsData,
            usersData,
            topCommands,
            serverActivity,
            topUsers,
            recentActivity
        });

    } catch (error) {
        console.error('خطأ في API التحليلات:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل التحليلات' });
    }
});

// API بيانات المستخدم العادي
router.get('/user/data', async (req, res) => {
    try {
        // التحقق من تسجيل الدخول
        if (!req.session || !req.session.user) {
            return res.status(401).json({ error: 'غير مسجل الدخول' });
        }

        const user = req.session.user;
        const bot = req.app.locals.bot;
        const memoryDB = require('../../utils/memoryDB');

        // الحصول على بيانات المستخدم من قاعدة البيانات
        const userData = await memoryDB.getUser(user.id);

        // الحصول على جميع سيرفرات البوت مع حالة المستخدم فيها
        const userServers = [];

        if (bot && bot.guilds) {
            for (const [guildId, guild] of bot.guilds.cache) {
                try {
                    const member = await guild.members.fetch(user.id).catch(() => null);
                    const isInServer = !!member;

                    // التحقق من صلاحيات الإدارة المختلفة
                    const canManage = member && (
                        member.permissions.has('ManageGuild') ||
                        member.permissions.has('Administrator') ||
                        member.permissions.has('ManageChannels') ||
                        member.permissions.has('ManageRoles') ||
                        guild.ownerId === user.id
                    );

                    userServers.push({
                        id: guild.id,
                        name: guild.name,
                        icon: guild.iconURL({ dynamic: true }),
                        memberCount: guild.memberCount,
                        owner: guild.ownerId === user.id,
                        isInServer: isInServer,
                        canManage: canManage,
                        permissions: member ? {
                            administrator: member.permissions.has('Administrator'),
                            manageGuild: member.permissions.has('ManageGuild'),
                            manageChannels: member.permissions.has('ManageChannels'),
                            manageRoles: member.permissions.has('ManageRoles'),
                            manageMessages: member.permissions.has('ManageMessages'),
                            kickMembers: member.permissions.has('KickMembers'),
                            banMembers: member.permissions.has('BanMembers')
                        } : null,
                        inviteUrl: `https://discord.com/api/oauth2/authorize?client_id=${req.app.locals.config.clientId}&permissions=8&scope=bot&guild_id=${guild.id}`
                    });
                } catch (error) {
                    // في حالة عدم القدرة على الوصول للسيرفر
                    userServers.push({
                        id: guild.id,
                        name: guild.name,
                        icon: guild.iconURL({ dynamic: true }),
                        memberCount: guild.memberCount,
                        owner: false,
                        isInServer: false,
                        canManage: false,
                        permissions: null,
                        inviteUrl: `https://discord.com/api/oauth2/authorize?client_id=${req.app.locals.config.clientId}&permissions=8&scope=bot&guild_id=${guild.id}`
                    });
                }
            }
        }

        // إعداد البيانات المرسلة
        const responseData = {
            id: user.id,
            username: user.username,
            avatar: user.avatar,
            servers: userServers,
            premium: userData.premium || { active: false },
            commandsUsed: userData.commandsUsed || 0,
            joinedAt: userData.joinedAt || new Date().toISOString()
        };

        res.json(responseData);

    } catch (error) {
        console.error('خطأ في API بيانات المستخدم:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل البيانات' });
    }
});

// API إعدادات السيرفر للمستخدم العادي
router.get('/user/server/:serverId', async (req, res) => {
    try {
        // التحقق من تسجيل الدخول
        if (!req.session || !req.session.user) {
            return res.status(401).json({ error: 'غير مسجل الدخول' });
        }

        const user = req.session.user;
        const serverId = req.params.serverId;
        const bot = req.app.locals.bot;

        // التحقق من وجود السيرفر
        const guild = bot.guilds.cache.get(serverId);
        if (!guild) {
            return res.status(404).json({ error: 'السيرفر غير موجود' });
        }

        // التحقق من صلاحيات المستخدم
        const member = await guild.members.fetch(user.id).catch(() => null);
        if (!member || (!member.permissions.has('ManageGuild') && guild.ownerId !== user.id)) {
            return res.status(403).json({ error: 'ليس لديك صلاحية لإدارة هذا السيرفر' });
        }

        // إرجاع إعدادات السيرفر (يمكن توسيعها لاحقاً)
        res.json({
            id: guild.id,
            name: guild.name,
            settings: {
                welcomeEnabled: false,
                leaveEnabled: false,
                autoRoleEnabled: false,
                antiSpamEnabled: false,
                autoModEnabled: false,
                logsEnabled: false
            }
        });

    } catch (error) {
        console.error('خطأ في API إعدادات السيرفر:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل إعدادات السيرفر' });
    }
});

// API سيرفرات المطور الشخصية (للمطور فقط)
router.get('/my-servers', checkOwnerAPI, async (req, res) => {
    try {
        const user = req.session?.user || req.user;
        const bot = req.app.locals.bot;

        // الحصول على السيرفرات التي يملكها أو يديرها المطور
        const myServers = [];
        let ownedCount = 0;
        let managedCount = 0;
        let totalMembers = 0;
        let activeConfigs = 0;

        if (bot && bot.guilds) {
            for (const [guildId, guild] of bot.guilds.cache) {
                try {
                    const member = await guild.members.fetch(user.id).catch(() => null);
                    if (member) {
                        const isOwner = guild.ownerId === user.id;
                        const canManage = member.permissions.has('ManageGuild') || isOwner;

                        if (canManage) {
                            myServers.push({
                                id: guild.id,
                                name: guild.name,
                                icon: guild.iconURL({ dynamic: true }),
                                memberCount: guild.memberCount,
                                channelCount: guild.channels.cache.size,
                                roleCount: guild.roles.cache.size,
                                owner: isOwner,
                                configCount: Math.floor(Math.random() * 5) + 1 // مؤقت
                            });

                            if (isOwner) ownedCount++;
                            else managedCount++;

                            totalMembers += guild.memberCount;
                            activeConfigs += Math.floor(Math.random() * 5) + 1;
                        }
                    }
                } catch (error) {
                    // تجاهل أخطاء السيرفرات التي لا يمكن الوصول إليها
                }
            }
        }

        res.json({
            servers: myServers,
            stats: {
                ownedServers: ownedCount,
                managedServers: managedCount,
                totalMembers: totalMembers,
                activeConfigs: activeConfigs
            }
        });

    } catch (error) {
        console.error('خطأ في API سيرفرات المطور:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل السيرفرات' });
    }
});

// API إعدادات سيرفر محدد (للمطور فقط)
router.get('/server/:serverId/settings', checkOwnerAPI, async (req, res) => {
    try {
        const serverId = req.params.serverId;
        const bot = req.app.locals.bot;

        // التحقق من وجود السيرفر
        const guild = bot.guilds.cache.get(serverId);
        if (!guild) {
            return res.status(404).json({ error: 'السيرفر غير موجود' });
        }

        // إرجاع إعدادات السيرفر (يمكن توسيعها لاحقاً)
        res.json({
            welcomeEnabled: false,
            leaveEnabled: false,
            autoRoleEnabled: false,
            antiSpamEnabled: false,
            autoModEnabled: false,
            logsEnabled: false
        });

    } catch (error) {
        console.error('خطأ في API إعدادات السيرفر:', error);
        res.status(500).json({ error: 'حدث خطأ في تحميل الإعدادات' });
    }
});

// API حفظ إعدادات سيرفر (للمطور فقط)
router.post('/server/:serverId/settings', checkOwnerAPI, async (req, res) => {
    try {
        const serverId = req.params.serverId;
        const settings = req.body;
        const bot = req.app.locals.bot;

        // التحقق من وجود السيرفر
        const guild = bot.guilds.cache.get(serverId);
        if (!guild) {
            return res.status(404).json({ error: 'السيرفر غير موجود' });
        }

        // حفظ الإعدادات (في التطبيق الحقيقي سيتم حفظها في قاعدة البيانات)
        console.log(`حفظ إعدادات السيرفر ${guild.name}:`, settings);

        res.json({ success: true, message: 'تم حفظ الإعدادات بنجاح' });

    } catch (error) {
        console.error('خطأ في حفظ إعدادات السيرفر:', error);
        res.status(500).json({ error: 'حدث خطأ في حفظ الإعدادات' });
    }
});

// API لإضافة مالك جديد
router.post('/add-owner', checkOwnerAPI, async (req, res) => {
    try {
        const { userId, note } = req.body;

        if (!userId) {
            return res.status(400).json({
                success: false,
                message: 'معرف المستخدم مطلوب'
            });
        }

        // التحقق من صحة معرف المستخدم
        if (!/^\d{17,19}$/.test(userId)) {
            return res.status(400).json({
                success: false,
                message: 'معرف المستخدم غير صحيح'
            });
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const config = req.app.locals.config;
        const currentOwners = config.owners || [];

        if (currentOwners.includes(userId)) {
            return res.status(400).json({
                success: false,
                message: 'هذا المستخدم مالك بالفعل'
            });
        }

        // إضافة المالك الجديد للذاكرة
        config.owners = [...currentOwners, userId];

        // تسجيل العملية
        console.log(`✅ تم إضافة مالك جديد: ${userId} بواسطة ${req.user.username}`);
        if (note) {
            console.log(`📝 ملاحظة: ${note}`);
        }

        res.json({
            success: true,
            message: 'تم إضافة المالك بنجاح',
            newOwner: userId,
            totalOwners: config.owners.length
        });

    } catch (error) {
        console.error('خطأ في إضافة المالك:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في إضافة المالك'
        });
    }
});

// API لحذف مالك
router.delete('/remove-owner/:userId', checkOwnerAPI, async (req, res) => {
    try {
        const { userId } = req.params;
        const config = req.app.locals.config;
        const currentOwners = config.owners || [];

        if (!currentOwners.includes(userId)) {
            return res.status(404).json({
                success: false,
                message: 'المستخدم ليس مالكاً'
            });
        }

        // منع حذف المالك الأساسي
        if (userId === req.user.id) {
            return res.status(400).json({
                success: false,
                message: 'لا يمكنك حذف نفسك من قائمة المالكين'
            });
        }

        // حذف المالك من الذاكرة
        config.owners = currentOwners.filter(id => id !== userId);

        console.log(`❌ تم حذف المالك: ${userId} بواسطة ${req.user.username}`);

        res.json({
            success: true,
            message: 'تم حذف المالك بنجاح',
            removedOwner: userId,
            totalOwners: config.owners.length
        });

    } catch (error) {
        console.error('خطأ في حذف المالك:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في حذف المالك'
        });
    }
});

module.exports = router;
