/**
 * CS Bot - مسارات API
 * 
 * هذا الملف يحتوي على مسارات API للتفاعل مع البوت
 */

const express = require('express');
const router = express.Router();
const { getGuildSettings, saveGuildSettings } = require('../../models/guild');

// التحقق من المصادقة للـ API
const checkAuth = (req, res, next) => {
    if (req.isAuthenticated()) return next();
    return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
};

// الحصول على إعدادات السيرفر
router.get('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);
    
    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لإعدادات هذا السيرفر' });
    }
    
    try {
        const settings = await getGuildSettings(req.params.guildId);
        res.json(settings);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإعدادات' });
    }
});

// حفظ إعدادات السيرفر
router.post('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);
    
    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }
    
    try {
        await saveGuildSettings(req.params.guildId, req.body);
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

// اختبار الترحيب
router.post('/guilds/:guildId/test-welcome', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);
    
    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }
    
    try {
        // هنا يتم استدعاء وظيفة اختبار الترحيب من البوت
        // سيتم تنفيذها لاحقاً
        res.json({ success: true, message: 'تم إرسال رسالة الترحيب التجريبية' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء اختبار الترحيب' });
    }
});

// الحصول على إحصائيات البوت
router.get('/stats', async (req, res) => {
    try {
        // هنا يتم جلب إحصائيات البوت
        const stats = {
            servers: 0, // سيتم تحديثها لاحقاً
            users: 0,   // سيتم تحديثها لاحقاً
            commands: 0, // سيتم تحديثها لاحقاً
            uptime: 0    // سيتم تحديثها لاحقاً
        };
        
        res.json(stats);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

module.exports = router;
