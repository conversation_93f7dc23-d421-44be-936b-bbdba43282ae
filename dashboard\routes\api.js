/**
 * CS Bot - مسارات API
 *
 * هذا الملف يحتوي على مسارات API للتفاعل مع البوت
 */

const express = require('express');
const router = express.Router();
const { getGuildSettings, saveGuildSettings } = require('../../models/guild');

// التحقق من المصادقة للـ API
const checkAuth = (req, res, next) => {
    if (req.isAuthenticated()) return next();
    return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
};

// الحصول على إعدادات السيرفر
router.get('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لإعدادات هذا السيرفر' });
    }

    try {
        const settings = await getGuildSettings(req.params.guildId);
        res.json(settings);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإعدادات' });
    }
});

// حفظ إعدادات السيرفر
router.post('/guilds/:guildId/settings', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتعديل إعدادات هذا السيرفر' });
    }

    try {
        await saveGuildSettings(req.params.guildId, req.body);
        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء حفظ الإعدادات' });
    }
});

// اختبار الترحيب
router.post('/guilds/:guildId/test-welcome', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    try {
        // هنا يتم استدعاء وظيفة اختبار الترحيب من البوت
        // سيتم تنفيذها لاحقاً
        res.json({ success: true, message: 'تم إرسال رسالة الترحيب التجريبية' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء اختبار الترحيب' });
    }
});

// الحصول على إحصائيات البوت
router.get('/stats', async (req, res) => {
    try {
        // هنا يتم جلب إحصائيات البوت
        const stats = {
            servers: 0, // سيتم تحديثها لاحقاً
            users: 0,   // سيتم تحديثها لاحقاً
            commands: 0, // سيتم تحديثها لاحقاً
            uptime: 0    // سيتم تحديثها لاحقاً
        };

        res.json(stats);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// تحديث إعدادات الملف الشخصي
router.post('/profile/update', checkAuth, async (req, res) => {
    try {
        const { getUserData } = require('../../models/user');
        const { language, notifications } = req.body;

        // تحديث بيانات المستخدم
        const userData = await getUserData(req.user.id, req.user.username, req.user.discriminator, req.user.avatar);

        if (language) {
            userData.language = language;
        }

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء تحديث الملف الشخصي' });
    }
});

// الحصول على قنوات السيرفر
router.get('/guilds/:guildId/channels', checkAuth, async (req, res) => {
    const guild = req.user.guilds.find(g => g.id === req.params.guildId);

    if (!guild || (guild.permissions & 0x20) !== 0x20) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لقنوات هذا السيرفر' });
    }

    try {
        // هنا سيتم جلب القنوات من البوت
        // مؤقتاً سنرجع قائمة فارغة
        const channels = [];
        res.json(channels);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب القنوات' });
    }
});

// التحقق من صلاحيات الأدمن
const checkAdmin = (req, res, next) => {
    if (!req.isAuthenticated()) {
        return res.status(401).json({ error: 'غير مصرح لك بالوصول' });
    }

    const config = req.app.locals.config;
    if (config.owner.id !== req.user.id) {
        return res.status(403).json({ error: 'غير مصرح لك بالوصول لهذه الصفحة' });
    }

    return next();
};

// إحصائيات الأدمن
router.get('/admin/stats', checkAdmin, async (req, res) => {
    try {
        const stats = {
            servers: 0,
            users: 0,
            commands: 0,
            uptime: '0m'
        };

        res.json(stats);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء جلب الإحصائيات' });
    }
});

// إضافة بريميوم
router.post('/admin/premium/add', checkAdmin, async (req, res) => {
    try {
        const { userId, duration } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        // حساب تاريخ الانتهاء
        const now = new Date();
        let expiresAt = new Date(now);

        if (duration.endsWith('d')) {
            const days = parseInt(duration.replace('d', ''));
            expiresAt.setDate(expiresAt.getDate() + days);
        }

        userData.premium = {
            enabled: true,
            expiresAt: expiresAt,
            tier: 'basic'
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إضافة البريميوم' });
    }
});

// إزالة بريميوم
router.post('/admin/premium/remove', checkAdmin, async (req, res) => {
    try {
        const { userId } = req.body;
        const { getUserData } = require('../../models/user');

        const userData = await getUserData(userId);
        if (!userData) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        userData.premium = {
            enabled: false,
            expiresAt: null,
            tier: null
        };

        await userData.save();

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إزالة البريميوم' });
    }
});

// إرسال إعلان
router.post('/admin/announcement', checkAdmin, async (req, res) => {
    try {
        const { title, message, sendToAll } = req.body;

        // هنا سيتم إرسال الإعلان عبر البوت
        // مؤقتاً سنرجع نجاح العملية

        res.json({ success: true });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'حدث خطأ أثناء إرسال الإعلان' });
    }
});

module.exports = router;
