/**
 * CS Bot - أوامر الترفيه الأسطورية
 * 
 * ألعاب وأنشطة ترفيهية مذهلة
 */

const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'fun',
    aliases: ['ترفيه', 'لعب', 'games'],
    description: 'ألعاب وأنشطة ترفيهية مذهلة!',
    usage: '!fun [8ball|dice|coin|rps|quiz|joke|roast]',
    category: 'ترفيه',
    cooldown: 3,
    
    async execute(message, args) {
        try {
            const subCommand = args[0]?.toLowerCase() || 'help';
            
            switch (subCommand) {
                case '8ball':
                case 'كرة':
                    await magic8Ball(message, args.slice(1));
                    break;
                    
                case 'dice':
                case 'نرد':
                    await rollDice(message, args.slice(1));
                    break;
                    
                case 'coin':
                case 'عملة':
                    await flipCoin(message);
                    break;
                    
                case 'rps':
                case 'حجر_ورقة_مقص':
                    await rockPaperScissors(message, args.slice(1));
                    break;
                    
                case 'quiz':
                case 'سؤال':
                    await startQuiz(message);
                    break;
                    
                case 'joke':
                case 'نكتة':
                    await tellJoke(message);
                    break;
                    
                case 'roast':
                case 'تنمر':
                    await roastUser(message);
                    break;
                    
                case 'love':
                case 'حب':
                    await loveCalculator(message, args.slice(1));
                    break;
                    
                case 'fortune':
                case 'حظ':
                    await fortuneTeller(message);
                    break;
                    
                default:
                    await showFunHelp(message);
            }
            
        } catch (error) {
            console.error('خطأ في أوامر الترفيه:', error);
            message.reply('❌ حدث خطأ في نظام الترفيه!');
        }
    }
};

// الكرة السحرية
async function magic8Ball(message, args) {
    if (!args.length) {
        return message.reply('❌ يرجى طرح سؤال للكرة السحرية!');
    }
    
    const responses = [
        'نعم بالتأكيد! ✅',
        'لا، بالطبع لا! ❌',
        'ربما... 🤔',
        'المستقبل غامض 🔮',
        'اسأل مرة أخرى لاحقاً ⏰',
        'لا أستطيع التنبؤ الآن 🌫️',
        'احتمال كبير! 📈',
        'لا تعتمد عليه 📉',
        'نعم! 🎉',
        'علاماتي تشير إلى نعم ✨',
        'الرد غامض، حاول مرة أخرى 🌀',
        'بدون شك! 💯',
        'مصادري تقول لا 📚',
        'النظرة جيدة 👀',
        'نعم بالتأكيد! 🌟'
    ];
    
    const response = responses[Math.floor(Math.random() * responses.length)];
    const question = args.join(' ');
    
    const embed = new EmbedBuilder()
        .setTitle('🔮 الكرة السحرية')
        .setColor('#9932cc')
        .addFields(
            {
                name: '❓ سؤالك',
                value: question,
                inline: false
            },
            {
                name: '🎱 الإجابة',
                value: response,
                inline: false
            }
        )
        .setTimestamp()
        .setFooter({ text: `سأل بواسطة ${message.author.username}` });
    
    await message.reply({ embeds: [embed] });
}

// رمي النرد
async function rollDice(message, args) {
    const sides = parseInt(args[0]) || 6;
    const count = parseInt(args[1]) || 1;
    
    if (sides < 2 || sides > 100) {
        return message.reply('❌ عدد الأوجه يجب أن يكون بين 2 و 100!');
    }
    
    if (count < 1 || count > 10) {
        return message.reply('❌ عدد النرد يجب أن يكون بين 1 و 10!');
    }
    
    const results = [];
    let total = 0;
    
    for (let i = 0; i < count; i++) {
        const roll = Math.floor(Math.random() * sides) + 1;
        results.push(roll);
        total += roll;
    }
    
    const embed = new EmbedBuilder()
        .setTitle('🎲 رمي النرد')
        .setColor('#ff6b6b')
        .addFields(
            {
                name: '🎯 النتائج',
                value: results.map(r => `**${r}**`).join(' + '),
                inline: true
            },
            {
                name: '📊 المجموع',
                value: `**${total}**`,
                inline: true
            },
            {
                name: 'ℹ️ التفاصيل',
                value: `${count} نرد بـ ${sides} أوجه`,
                inline: true
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// قلب العملة
async function flipCoin(message) {
    const result = Math.random() < 0.5 ? 'صورة' : 'كتابة';
    const emoji = result === 'صورة' ? '🪙' : '📝';
    
    const embed = new EmbedBuilder()
        .setTitle('🪙 قلب العملة')
        .setColor('#ffd700')
        .setDescription(`${emoji} **${result}**`)
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// حجر ورقة مقص
async function rockPaperScissors(message, args) {
    const choices = ['حجر', 'ورقة', 'مقص'];
    const emojis = { 'حجر': '🪨', 'ورقة': '📄', 'مقص': '✂️' };
    
    const userChoice = args[0]?.toLowerCase();
    
    if (!userChoice || !choices.includes(userChoice)) {
        const buttons = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('rps_rock')
                    .setLabel('حجر')
                    .setEmoji('🪨')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('rps_paper')
                    .setLabel('ورقة')
                    .setEmoji('📄')
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('rps_scissors')
                    .setLabel('مقص')
                    .setEmoji('✂️')
                    .setStyle(ButtonStyle.Primary)
            );
        
        const embed = new EmbedBuilder()
            .setTitle('🎮 حجر ورقة مقص')
            .setDescription('اختر خيارك!')
            .setColor('#4CAF50');
        
        return message.reply({ embeds: [embed], components: [buttons] });
    }
    
    const botChoice = choices[Math.floor(Math.random() * choices.length)];
    
    let result;
    if (userChoice === botChoice) {
        result = 'تعادل! 🤝';
    } else if (
        (userChoice === 'حجر' && botChoice === 'مقص') ||
        (userChoice === 'ورقة' && botChoice === 'حجر') ||
        (userChoice === 'مقص' && botChoice === 'ورقة')
    ) {
        result = 'فزت! 🎉';
    } else {
        result = 'خسرت! 😢';
    }
    
    const embed = new EmbedBuilder()
        .setTitle('🎮 حجر ورقة مقص')
        .setColor('#4CAF50')
        .addFields(
            {
                name: '👤 اختيارك',
                value: `${emojis[userChoice]} ${userChoice}`,
                inline: true
            },
            {
                name: '🤖 اختياري',
                value: `${emojis[botChoice]} ${botChoice}`,
                inline: true
            },
            {
                name: '🏆 النتيجة',
                value: result,
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// مسابقة
async function startQuiz(message) {
    const questions = [
        {
            question: 'ما هي عاصمة مصر؟',
            options: ['القاهرة', 'الإسكندرية', 'الجيزة', 'أسوان'],
            correct: 0
        },
        {
            question: 'كم عدد قارات العالم؟',
            options: ['5', '6', '7', '8'],
            correct: 2
        },
        {
            question: 'ما هو أكبر كوكب في النظام الشمسي؟',
            options: ['الأرض', 'المشتري', 'زحل', 'نبتون'],
            correct: 1
        },
        {
            question: 'في أي عام تم اختراع الإنترنت؟',
            options: ['1969', '1975', '1983', '1991'],
            correct: 0
        }
    ];
    
    const quiz = questions[Math.floor(Math.random() * questions.length)];
    
    const embed = new EmbedBuilder()
        .setTitle('🧠 مسابقة المعرفة')
        .setDescription(quiz.question)
        .setColor('#2196F3')
        .setTimestamp();
    
    quiz.options.forEach((option, index) => {
        embed.addFields({
            name: `${index + 1}️⃣`,
            value: option,
            inline: true
        });
    });
    
    const buttons = new ActionRowBuilder()
        .addComponents(
            quiz.options.map((_, index) => 
                new ButtonBuilder()
                    .setCustomId(`quiz_${index}`)
                    .setLabel(`${index + 1}`)
                    .setStyle(ButtonStyle.Secondary)
            )
        );
    
    const quizMessage = await message.reply({ embeds: [embed], components: [buttons] });
    
    // معالج الإجابات
    const collector = quizMessage.createMessageComponentCollector({
        time: 30000 // 30 ثانية
    });
    
    collector.on('collect', async (interaction) => {
        if (interaction.user.id !== message.author.id) {
            return interaction.reply({ content: '❌ هذه مسابقتك أنت!', ephemeral: true });
        }
        
        const answer = parseInt(interaction.customId.split('_')[1]);
        const isCorrect = answer === quiz.correct;
        
        const resultEmbed = new EmbedBuilder()
            .setTitle(isCorrect ? '🎉 إجابة صحيحة!' : '❌ إجابة خاطئة!')
            .setDescription(`الإجابة الصحيحة: **${quiz.options[quiz.correct]}**`)
            .setColor(isCorrect ? '#4CAF50' : '#f44336')
            .setTimestamp();
        
        await interaction.update({ embeds: [resultEmbed], components: [] });
        collector.stop();
    });
    
    collector.on('end', (collected) => {
        if (collected.size === 0) {
            const timeoutEmbed = new EmbedBuilder()
                .setTitle('⏰ انتهى الوقت!')
                .setDescription(`الإجابة الصحيحة كانت: **${quiz.options[quiz.correct]}**`)
                .setColor('#ff9800');
            
            quizMessage.edit({ embeds: [timeoutEmbed], components: [] });
        }
    });
}

// نكتة
async function tellJoke(message) {
    const jokes = [
        'لماذا لا يستطيع المبرمج النوم؟ لأن لديه bugs في كوده! 😂',
        'ما الفرق بين المبرمج والطبيب؟ الطبيب يقتل شخص واحد في المرة، المبرمج يقتل الآلاف! 💻',
        'لماذا يحب المبرمجون القهوة؟ لأنها تحول الكود إلى برامج! ☕',
        'ما هو الشيء الوحيد الذي يعمل في أول مرة؟ Demo! 🎭',
        'لماذا يكره المبرمجون الطبيعة؟ لديها bugs كثيرة! 🐛'
    ];
    
    const joke = jokes[Math.floor(Math.random() * jokes.length)];
    
    const embed = new EmbedBuilder()
        .setTitle('😂 نكتة اليوم')
        .setDescription(joke)
        .setColor('#ffeb3b')
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}

// مساعدة الترفيه
async function showFunHelp(message) {
    const embed = new EmbedBuilder()
        .setTitle('🎮 أوامر الترفيه الأسطورية')
        .setColor('#e91e63')
        .setDescription('استمتع بالألعاب والأنشطة المذهلة!')
        .addFields(
            {
                name: '🎲 الألعاب',
                value: [
                    '`!fun 8ball <سؤال>` - الكرة السحرية',
                    '`!fun dice [أوجه] [عدد]` - رمي النرد',
                    '`!fun coin` - قلب العملة',
                    '`!fun rps` - حجر ورقة مقص'
                ].join('\n'),
                inline: false
            },
            {
                name: '🧠 المعرفة والترفيه',
                value: [
                    '`!fun quiz` - مسابقة معرفة',
                    '`!fun joke` - نكتة مضحكة',
                    '`!fun love @user` - حاسبة الحب',
                    '`!fun fortune` - قراءة الطالع'
                ].join('\n'),
                inline: false
            }
        )
        .setTimestamp();
    
    await message.reply({ embeds: [embed] });
}
