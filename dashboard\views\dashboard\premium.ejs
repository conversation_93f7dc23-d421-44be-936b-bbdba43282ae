<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البريميوم - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="https://cdn.discordapp.com/avatars/<%= config.clientId %>/a_01234567890abcdef.gif?size=64" 
                 alt="CS Bot" 
                 class="rounded-circle me-2" 
                 width="40" height="40"
                 onerror="this.src='https://cdn.discordapp.com/embed/avatars/0.png'">
            <span><i class="fas fa-crown"></i> CS Bot - إدارة البريميوم</span>
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-crown"></i> إدارة البريميوم
                    </h2>
                    <p class="card-text">إدارة اشتراكات البريميوم ومنح المميزات الخاصة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="totalPremium">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">مستخدمين بريميوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="monthlyRevenue">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">الإيرادات الشهرية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="expiringToday">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">ينتهي اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-plus fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="newThisMonth">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">جديد هذا الشهر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Premium Management -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-crown"></i> إدارة البريميوم
                        <button class="btn btn-sm btn-primary float-end" onclick="addPremium()">
                            <i class="fas fa-plus"></i> إضافة بريميوم
                        </button>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="premiumTable">
                                <tr>
                                    <td colspan="5" class="text-center py-3">
                                        <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Premium Modal -->
<div class="modal fade" id="addPremiumModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-crown"></i> إضافة بريميوم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPremiumForm">
                    <div class="mb-3">
                        <label for="userId" class="form-label">معرف المستخدم</label>
                        <input type="text" class="form-control" id="userId" required>
                    </div>
                    <div class="mb-3">
                        <label for="duration" class="form-label">المدة</label>
                        <select class="form-control" id="duration" required>
                            <option value="30">شهر واحد</option>
                            <option value="90">3 أشهر</option>
                            <option value="180">6 أشهر</option>
                            <option value="365">سنة كاملة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="savePremium()">
                    <i class="fas fa-crown"></i> إضافة البريميوم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تحميل بيانات البريميوم
async function loadPremiumData() {
    try {
        const response = await fetch('/api/premium');
        const data = await response.json();
        
        // تحديث الإحصائيات
        document.getElementById('totalPremium').textContent = data.totalPremium || 0;
        document.getElementById('monthlyRevenue').textContent = '$' + (data.monthlyRevenue || 0);
        document.getElementById('expiringToday').textContent = data.expiringToday || 0;
        document.getElementById('newThisMonth').textContent = data.newThisMonth || 0;
        
        // تحديث الجدول
        updatePremiumTable(data.premiumUsers || []);
        
    } catch (error) {
        console.error('خطأ في تحميل بيانات البريميوم:', error);
    }
}

// تحديث جدول البريميوم
function updatePremiumTable(users) {
    const tbody = document.getElementById('premiumTable');
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-3">
                    <i class="fas fa-crown text-muted"></i>
                    <p class="mb-0 mt-2">لا يوجد مستخدمين بريميوم</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = users.map(user => `
        <tr>
            <td>
                <div class="d-flex align-items-center">
                    <img src="${user.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                         class="rounded-circle me-2" width="32" height="32">
                    <div>
                        <strong>${user.username}</strong>
                        <br><small class="text-muted">${user.id}</small>
                    </div>
                </div>
            </td>
            <td>${new Date(user.startDate).toLocaleDateString('ar-SA')}</td>
            <td>${new Date(user.endDate).toLocaleDateString('ar-SA')}</td>
            <td>
                <span class="badge ${user.active ? 'bg-success' : 'bg-danger'}">
                    ${user.active ? 'نشط' : 'منتهي'}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="extendPremium('${user.id}')">
                        <i class="fas fa-plus"></i> تمديد
                    </button>
                    <button class="btn btn-outline-danger" onclick="removePremium('${user.id}')">
                        <i class="fas fa-times"></i> إزالة
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// إضافة بريميوم
function addPremium() {
    new bootstrap.Modal(document.getElementById('addPremiumModal')).show();
}

// حفظ البريميوم
async function savePremium() {
    const userId = document.getElementById('userId').value;
    const duration = document.getElementById('duration').value;
    
    if (!userId) {
        alert('يرجى إدخال معرف المستخدم');
        return;
    }
    
    try {
        const response = await fetch('/api/premium/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId, duration: parseInt(duration) })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم إضافة البريميوم بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('addPremiumModal')).hide();
            loadPremiumData();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إضافة البريميوم');
    }
}

// تمديد البريميوم
async function extendPremium(userId) {
    const days = prompt('كم يوم تريد التمديد؟', '30');
    if (!days) return;
    
    try {
        const response = await fetch('/api/premium/extend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId, days: parseInt(days) })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم تمديد البريميوم بنجاح!');
            loadPremiumData();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في تمديد البريميوم');
    }
}

// إزالة البريميوم
async function removePremium(userId) {
    if (!confirm('هل أنت متأكد من إزالة البريميوم؟')) return;
    
    try {
        const response = await fetch('/api/premium/remove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم إزالة البريميوم بنجاح!');
            loadPremiumData();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في إزالة البريميوم');
    }
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadPremiumData);

// تحديث البيانات كل دقيقة
setInterval(loadPremiumData, 60000);
</script>

</body>
</html>
