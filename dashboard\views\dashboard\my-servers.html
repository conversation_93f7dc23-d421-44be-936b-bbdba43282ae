<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحكم في سيرفراتك - CS Bot</title>

    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
        <a class="navbar-brand" href="/dashboard">
            <i class="fas fa-user-cog"></i> CS Bot - تحكم في سيرفراتك
        </a>
        <div class="d-flex">
            <a href="/dashboard" class="btn btn-outline-light me-2">
                <i class="fas fa-arrow-left"></i> العودة للوحة التحكم
            </a>
            <a href="/auth/logout" class="btn btn-outline-light">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </a>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient text-white">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="fas fa-user-cog"></i> تحكم في سيرفراتك الشخصية
                    </h2>
                    <p class="card-text">إدارة السيرفرات التي تملكها أو لك صلاحيات إدارة فيها</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                    <h4 class="card-title" id="ownedServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفرات مملوكة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
                    <h4 class="card-title" id="managedServers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">سيرفرات تديرها</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                    <h4 class="card-title" id="totalMembers">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إجمالي الأعضاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-cog fa-2x text-primary mb-2"></i>
                    <h4 class="card-title" id="activeConfigs">
                        <i class="fas fa-spinner fa-spin"></i>
                    </h4>
                    <p class="card-text text-muted">إعدادات نشطة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- My Servers List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> سيرفراتك الشخصية
                        <span class="badge bg-light text-dark ms-2" id="myServerCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="myServersContainer">
                        <div class="col-12 text-center py-4">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">جاري تحميل سيرفراتك...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Server Settings Modal -->
<div class="modal fade" id="serverSettingsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog"></i> إعدادات السيرفر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="serverSettingsBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="saveServerSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
let myServers = [];
let currentServerId = null;

// تحميل سيرفرات المطور
async function loadMyServers() {
    try {
        const response = await fetch('/api/my-servers');
        const data = await response.json();
        
        if (response.ok) {
            myServers = data.servers || [];
            displayMyServers(myServers);
            updateMyStats(data.stats || {});
            
            document.getElementById('myServerCount').textContent = myServers.length;
        } else {
            console.error('خطأ في تحميل السيرفرات:', data.error);
        }
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        document.getElementById('myServersContainer').innerHTML = `
            <div class="col-12 text-center py-4 text-danger">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
                <p class="mt-2">حدث خطأ في تحميل السيرفرات</p>
            </div>
        `;
    }
}

// عرض سيرفرات المطور
function displayMyServers(servers) {
    const container = document.getElementById('myServersContainer');
    
    if (servers.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="fas fa-server fa-3x text-muted mb-3"></i>
                <h5>لا توجد سيرفرات</h5>
                <p class="text-muted">لا تملك أو تدير أي سيرفر يحتوي على البوت</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = servers.map(server => `
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${server.icon || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                             alt="${server.name}" 
                             class="rounded-circle me-3" 
                             width="50" height="50">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">${server.name}</h6>
                            <small class="text-muted">
                                <i class="fas fa-users"></i> ${server.memberCount} عضو
                                ${server.owner ? '<i class="fas fa-crown text-warning ms-2"></i>' : ''}
                            </small>
                        </div>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted">القنوات</small>
                            <div class="fw-bold">${server.channelCount || 0}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">الرتب</small>
                            <div class="fw-bold">${server.roleCount || 0}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">الإعدادات</small>
                            <div class="fw-bold text-success">${server.configCount || 0}</div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary btn-sm" onclick="openServerSettings('${server.id}')">
                            <i class="fas fa-cog"></i> إعدادات البوت
                        </button>
                        <div class="btn-group">
                            <button class="btn btn-outline-info btn-sm" onclick="viewServerAnalytics('${server.id}')">
                                <i class="fas fa-chart-bar"></i> إحصائيات
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="testBotFeatures('${server.id}')">
                                <i class="fas fa-play"></i> اختبار
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// تحديث إحصائيات المطور
function updateMyStats(stats) {
    document.getElementById('ownedServers').textContent = stats.ownedServers || 0;
    document.getElementById('managedServers').textContent = stats.managedServers || 0;
    document.getElementById('totalMembers').textContent = (stats.totalMembers || 0).toLocaleString();
    document.getElementById('activeConfigs').textContent = stats.activeConfigs || 0;
}

// فتح إعدادات السيرفر
async function openServerSettings(serverId) {
    currentServerId = serverId;
    const server = myServers.find(s => s.id === serverId);
    
    if (!server) return;
    
    try {
        const response = await fetch(`/api/server/${serverId}/settings`);
        const settings = await response.json();
        
        document.getElementById('serverSettingsBody').innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center mb-4">
                        <img src="${server.icon || 'https://cdn.discordapp.com/embed/avatars/0.png'}" 
                             alt="${server.name}" 
                             class="rounded-circle mb-2" 
                             width="80" height="80">
                        <h5>${server.name}</h5>
                        <small class="text-muted">${server.memberCount} عضو</small>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الإعدادات الأساسية</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="welcomeEnabled" ${settings.welcomeEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="welcomeEnabled">
                                    رسائل الترحيب
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="leaveEnabled" ${settings.leaveEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="leaveEnabled">
                                    رسائل المغادرة
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="autoRoleEnabled" ${settings.autoRoleEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="autoRoleEnabled">
                                    الرتب التلقائية
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>إعدادات الإدارة</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="antiSpamEnabled" ${settings.antiSpamEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="antiSpamEnabled">
                                    مانع السبام
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="autoModEnabled" ${settings.autoModEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="autoModEnabled">
                                    الإدارة التلقائية
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="logsEnabled" ${settings.logsEnabled ? 'checked' : ''}>
                                <label class="form-check-label" for="logsEnabled">
                                    تسجيل الأحداث
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        new bootstrap.Modal(document.getElementById('serverSettingsModal')).show();
        
    } catch (error) {
        alert('حدث خطأ في تحميل إعدادات السيرفر');
    }
}

// حفظ إعدادات السيرفر
async function saveServerSettings() {
    if (!currentServerId) return;
    
    const settings = {
        welcomeEnabled: document.getElementById('welcomeEnabled').checked,
        leaveEnabled: document.getElementById('leaveEnabled').checked,
        autoRoleEnabled: document.getElementById('autoRoleEnabled').checked,
        antiSpamEnabled: document.getElementById('antiSpamEnabled').checked,
        autoModEnabled: document.getElementById('autoModEnabled').checked,
        logsEnabled: document.getElementById('logsEnabled').checked
    };
    
    try {
        const response = await fetch(`/api/server/${currentServerId}/settings`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert('تم حفظ الإعدادات بنجاح!');
            bootstrap.Modal.getInstance(document.getElementById('serverSettingsModal')).hide();
        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        alert('حدث خطأ في حفظ الإعدادات');
    }
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadMyServers);

// تحديث البيانات كل دقيقة
setInterval(loadMyServers, 60000);
</script>

<style>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}
</style>

</body>
</html>
