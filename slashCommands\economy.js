/**
 * Slash Command للاقتصاد - CS Bot
 */

const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const User = require('../models/User');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('economy')
        .setNameLocalizations({
            'ar': 'اقتصاد'
        })
        .setDescription('نظام الاقتصاد والعملات')
        .setDescriptionLocalizations({
            'ar': 'نظام الاقتصاد والعملات'
        })
        .addSubcommand(subcommand =>
            subcommand
                .setName('balance')
                .setNameLocalizations({ 'ar': 'رصيد' })
                .setDescription('عرض رصيدك أو رصيد مستخدم آخر')
                .setDescriptionLocalizations({ 'ar': 'عرض رصيدك أو رصيد مستخدم آخر' })
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setNameLocalizations({ 'ar': 'مستخدم' })
                        .setDescription('المستخدم المراد عرض رصيده')
                        .setDescriptionLocalizations({ 'ar': 'المستخدم المراد عرض رصيده' })
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('daily')
                .setNameLocalizations({ 'ar': 'يومي' })
                .setDescription('احصل على مكافأتك اليومية')
                .setDescriptionLocalizations({ 'ar': 'احصل على مكافأتك اليومية' })
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('work')
                .setNameLocalizations({ 'ar': 'عمل' })
                .setDescription('اعمل لكسب المال')
                .setDescriptionLocalizations({ 'ar': 'اعمل لكسب المال' })
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('transfer')
                .setNameLocalizations({ 'ar': 'تحويل' })
                .setDescription('حول المال لمستخدم آخر')
                .setDescriptionLocalizations({ 'ar': 'حول المال لمستخدم آخر' })
                .addUserOption(option =>
                    option
                        .setName('user')
                        .setNameLocalizations({ 'ar': 'مستخدم' })
                        .setDescription('المستخدم المراد التحويل إليه')
                        .setDescriptionLocalizations({ 'ar': 'المستخدم المراد التحويل إليه' })
                        .setRequired(true)
                )
                .addIntegerOption(option =>
                    option
                        .setName('amount')
                        .setNameLocalizations({ 'ar': 'المبلغ' })
                        .setDescription('المبلغ المراد تحويله')
                        .setDescriptionLocalizations({ 'ar': 'المبلغ المراد تحويله' })
                        .setRequired(true)
                        .setMinValue(1)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('gamble')
                .setNameLocalizations({ 'ar': 'مقامرة' })
                .setDescription('قامر بأموالك')
                .setDescriptionLocalizations({ 'ar': 'قامر بأموالك' })
                .addIntegerOption(option =>
                    option
                        .setName('amount')
                        .setNameLocalizations({ 'ar': 'المبلغ' })
                        .setDescription('المبلغ المراد المقامرة به')
                        .setDescriptionLocalizations({ 'ar': 'المبلغ المراد المقامرة به' })
                        .setRequired(true)
                        .setMinValue(1)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('leaderboard')
                .setNameLocalizations({ 'ar': 'ترتيب' })
                .setDescription('عرض ترتيب أغنى المستخدمين')
                .setDescriptionLocalizations({ 'ar': 'عرض ترتيب أغنى المستخدمين' })
        ),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'balance':
                return this.handleBalance(interaction);
            case 'daily':
                return this.handleDaily(interaction);
            case 'work':
                return this.handleWork(interaction);
            case 'transfer':
                return this.handleTransfer(interaction);
            case 'gamble':
                return this.handleGamble(interaction);
            case 'leaderboard':
                return this.handleLeaderboard(interaction);
        }
    },

    async handleBalance(interaction) {
        const target = interaction.options.getUser('user') || interaction.user;
        const userData = await User.findOne({ userId: target.id });

        if (!userData) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('المستخدم غير مسجل في النظام')
                .setColor('#ff0000');
            return interaction.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle(`💰 رصيد ${target.username}`)
            .addFields([
                {
                    name: '💵 الرصيد الحالي',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '📊 المستوى',
                    value: `**${userData.economy.level}**`,
                    inline: true
                },
                {
                    name: '⭐ النقاط',
                    value: `**${userData.economy.xp.toLocaleString()}** XP`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(target.displayAvatarURL());

        return interaction.reply({ embeds: [embed] });
    },

    async handleDaily(interaction) {
        const userData = await User.findOneAndUpdate(
            { userId: interaction.user.id },
            {},
            { upsert: true, new: true }
        );

        const now = new Date();
        const lastDaily = userData.economy.lastDaily;
        const cooldown = 24 * 60 * 60 * 1000; // 24 ساعة

        if (lastDaily && (now - lastDaily) < cooldown) {
            const timeLeft = cooldown - (now - lastDaily);
            const hours = Math.floor(timeLeft / (60 * 60 * 1000));
            const minutes = Math.floor((timeLeft % (60 * 60 * 1000)) / (60 * 1000));

            const embed = new EmbedBuilder()
                .setTitle('⏰ انتظر قليلاً')
                .setDescription(`يمكنك الحصول على المكافأة اليومية بعد **${hours}س ${minutes}د**`)
                .setColor('#ff9900');
            return interaction.reply({ embeds: [embed] });
        }

        // حساب المكافأة
        const baseReward = 1000;
        const levelBonus = userData.economy.level * 100;
        const streakBonus = (userData.economy.dailyStreak || 0) * 50;
        const totalReward = baseReward + levelBonus + streakBonus;

        // تحديث البيانات
        userData.economy.balance += totalReward;
        userData.economy.lastDaily = now;
        userData.economy.dailyStreak = (userData.economy.dailyStreak || 0) + 1;
        userData.economy.totalEarned = (userData.economy.totalEarned || 0) + totalReward;
        
        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle('🎁 مكافأة يومية!')
            .setDescription(`تهانينا! حصلت على مكافأتك اليومية`)
            .addFields([
                {
                    name: '💵 إجمالي المكافأة',
                    value: `**${totalReward.toLocaleString()}** عملة`,
                    inline: false
                },
                {
                    name: '💰 رصيدك الجديد',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '🔥 سلسلة التتالي',
                    value: `**${userData.economy.dailyStreak}** يوم`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(interaction.user.displayAvatarURL());

        return interaction.reply({ embeds: [embed] });
    },

    async handleWork(interaction) {
        const userData = await User.findOneAndUpdate(
            { userId: interaction.user.id },
            {},
            { upsert: true, new: true }
        );

        const now = new Date();
        const lastWork = userData.economy.lastWork;
        const cooldown = 60 * 60 * 1000; // ساعة واحدة

        if (lastWork && (now - lastWork) < cooldown) {
            const timeLeft = cooldown - (now - lastWork);
            const minutes = Math.floor(timeLeft / (60 * 1000));

            const embed = new EmbedBuilder()
                .setTitle('😴 أنت متعب')
                .setDescription(`يمكنك العمل مرة أخرى بعد **${minutes}** دقيقة`)
                .setColor('#ff9900');
            return interaction.reply({ embeds: [embed] });
        }

        // أنواع الأعمال
        const jobs = [
            { name: 'مطور برمجيات', min: 500, max: 1500, emoji: '💻' },
            { name: 'طبيب', min: 800, max: 2000, emoji: '👨‍⚕️' },
            { name: 'مهندس', min: 600, max: 1800, emoji: '👷' },
            { name: 'معلم', min: 400, max: 1200, emoji: '👨‍🏫' }
        ];

        const randomJob = jobs[Math.floor(Math.random() * jobs.length)];
        const earnings = Math.floor(Math.random() * (randomJob.max - randomJob.min + 1)) + randomJob.min;
        const xpGain = Math.floor(earnings / 10);

        // تحديث البيانات
        userData.economy.balance += earnings;
        userData.economy.xp += xpGain;
        userData.economy.lastWork = now;
        userData.economy.totalEarned = (userData.economy.totalEarned || 0) + earnings;

        // فحص ترقية المستوى
        const newLevel = Math.floor(userData.economy.xp / 1000) + 1;
        let levelUp = false;
        if (newLevel > userData.economy.level) {
            userData.economy.level = newLevel;
            levelUp = true;
        }

        await userData.save();

        const embed = new EmbedBuilder()
            .setTitle(`${randomJob.emoji} عمل رائع!`)
            .setDescription(`عملت كـ **${randomJob.name}** وكسبت:`)
            .addFields([
                {
                    name: '💰 الأرباح',
                    value: `**${earnings.toLocaleString()}** عملة`,
                    inline: true
                },
                {
                    name: '⭐ النقاط',
                    value: `**${xpGain}** XP`,
                    inline: true
                },
                {
                    name: '💵 رصيدك الجديد',
                    value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(interaction.user.displayAvatarURL());

        if (levelUp) {
            embed.addFields([
                {
                    name: '🎉 ترقية مستوى!',
                    value: `تهانينا! وصلت للمستوى **${userData.economy.level}**`,
                    inline: false
                }
            ]);
        }

        return interaction.reply({ embeds: [embed] });
    },

    async handleTransfer(interaction) {
        const target = interaction.options.getUser('user');
        const amount = interaction.options.getInteger('amount');

        if (target.id === interaction.user.id) {
            const embed = new EmbedBuilder()
                .setTitle('❌ خطأ')
                .setDescription('لا يمكنك تحويل المال لنفسك!')
                .setColor('#ff0000');
            return interaction.reply({ embeds: [embed] });
        }

        const senderData = await User.findOne({ userId: interaction.user.id });
        if (!senderData || senderData.economy.balance < amount) {
            const embed = new EmbedBuilder()
                .setTitle('❌ رصيد غير كافي')
                .setDescription('ليس لديك رصيد كافي لهذا التحويل')
                .setColor('#ff0000');
            return interaction.reply({ embeds: [embed] });
        }

        // تحديث رصيد المرسل
        senderData.economy.balance -= amount;
        await senderData.save();

        // تحديث رصيد المستقبل
        await User.findOneAndUpdate(
            { userId: target.id },
            { 
                $inc: { 'economy.balance': amount },
                $setOnInsert: { 
                    userId: target.id,
                    username: target.username,
                    'economy.level': 1,
                    'economy.xp': 0
                }
            },
            { upsert: true }
        );

        const embed = new EmbedBuilder()
            .setTitle('💸 تحويل ناجح')
            .setDescription(`تم تحويل **${amount.toLocaleString()}** عملة إلى ${target}`)
            .addFields([
                {
                    name: '💰 رصيدك الجديد',
                    value: `**${senderData.economy.balance.toLocaleString()}** عملة`,
                    inline: true
                }
            ])
            .setColor('#00ff00')
            .setThumbnail(target.displayAvatarURL());

        return interaction.reply({ embeds: [embed] });
    },

    async handleGamble(interaction) {
        const amount = interaction.options.getInteger('amount');
        
        const userData = await User.findOne({ userId: interaction.user.id });
        if (!userData || userData.economy.balance < amount) {
            const embed = new EmbedBuilder()
                .setTitle('❌ رصيد غير كافي')
                .setDescription('ليس لديك رصيد كافي للمقامرة')
                .setColor('#ff0000');
            return interaction.reply({ embeds: [embed] });
        }

        // نسبة الفوز 45%
        const win = Math.random() < 0.45;
        const multiplier = win ? (Math.random() * 1.5 + 1.5) : 0; // 1.5x - 3x
        const winnings = Math.floor(amount * multiplier);

        if (win) {
            userData.economy.balance += winnings - amount;
            await userData.save();

            const embed = new EmbedBuilder()
                .setTitle('🎉 فزت!')
                .setDescription(`تهانينا! فزت في المقامرة`)
                .addFields([
                    {
                        name: '💰 المبلغ المراهن',
                        value: `${amount.toLocaleString()} عملة`,
                        inline: true
                    },
                    {
                        name: '🎯 المضاعف',
                        value: `${multiplier.toFixed(2)}x`,
                        inline: true
                    },
                    {
                        name: '💵 الأرباح',
                        value: `**${winnings.toLocaleString()}** عملة`,
                        inline: true
                    },
                    {
                        name: '💰 رصيدك الجديد',
                        value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                        inline: false
                    }
                ])
                .setColor('#00ff00');

            return interaction.reply({ embeds: [embed] });
        } else {
            userData.economy.balance -= amount;
            await userData.save();

            const embed = new EmbedBuilder()
                .setTitle('😢 خسرت!')
                .setDescription(`للأسف، خسرت في المقامرة`)
                .addFields([
                    {
                        name: '💸 المبلغ المفقود',
                        value: `${amount.toLocaleString()} عملة`,
                        inline: true
                    },
                    {
                        name: '💰 رصيدك الجديد',
                        value: `**${userData.economy.balance.toLocaleString()}** عملة`,
                        inline: true
                    }
                ])
                .setColor('#ff0000');

            return interaction.reply({ embeds: [embed] });
        }
    },

    async handleLeaderboard(interaction) {
        const topUsers = await User.find({})
            .sort({ 'economy.balance': -1 })
            .limit(10);

        if (topUsers.length === 0) {
            const embed = new EmbedBuilder()
                .setTitle('📊 ترتيب الأثرياء')
                .setDescription('لا يوجد مستخدمين في النظام بعد')
                .setColor('#ff9900');
            return interaction.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setTitle('🏆 ترتيب أغنى المستخدمين')
            .setDescription('أغنى 10 مستخدمين في البوت')
            .setColor('#ffd700');

        let description = '';
        for (let i = 0; i < topUsers.length; i++) {
            const user = topUsers[i];
            const medal = i === 0 ? '🥇' : i === 1 ? '🥈' : i === 2 ? '🥉' : `${i + 1}.`;
            description += `${medal} **${user.username}** - ${user.economy.balance.toLocaleString()} عملة\n`;
        }

        embed.setDescription(description);

        return interaction.reply({ embeds: [embed] });
    }
};
