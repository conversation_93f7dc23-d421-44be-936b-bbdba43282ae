<!-- Navbar أسطوري ومتقدم -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow-sm">
    <div class="container-fluid">
        <!-- زر القائمة للموبايل -->
        <button class="navbar-toggler d-lg-none me-3" type="button" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- العنوان والمسار -->
        <div class="navbar-brand-container">
            <h4 class="navbar-title mb-0">
                <i class="fas fa-tachometer-alt text-primary me-2"></i>
                <span id="pageTitle" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</span>
            </h4>
            <nav aria-label="breadcrumb" class="d-none d-md-block">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="text-decoration-none">
                            <i class="fas fa-home"></i>
                            <span data-en="Home" data-ar="الرئيسية">الرئيسية</span>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" id="currentPage" data-en="Dashboard" data-ar="لوحة التحكم">
                        لوحة التحكم
                    </li>
                </ol>
            </nav>
        </div>

        <!-- أدوات الـ Navbar -->
        <div class="navbar-tools">
            <!-- تبديل اللغة -->
            <div class="language-switcher me-3">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <span id="currentLang">العربية</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="#" onclick="switchLanguage('ar')">
                                <img src="https://flagcdn.com/w20/sa.png" alt="Arabic" class="me-2">
                                العربية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="switchLanguage('en')">
                                <img src="https://flagcdn.com/w20/us.png" alt="English" class="me-2">
                                English
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- الإشعارات المحسنة -->
            <div class="notifications me-3">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm position-relative notification-btn" type="button" id="notificationsDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge pulse" id="notificationCount">
                            5
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-bell me-2"></i>
                                <span data-en="Notifications" data-ar="الإشعارات">الإشعارات</span>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                                <i class="fas fa-check-double"></i>
                            </button>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="notification-list" id="notificationList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-footer">
                            <a href="/dashboard/notifications" class="dropdown-item text-center">
                                <span data-en="View All Notifications" data-ar="عرض جميع الإشعارات">عرض جميع الإشعارات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- البحث السريع -->
            <div class="quick-search me-3 d-none d-md-block">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="بحث سريع..." id="quickSearch">
                    <button class="btn btn-outline-light" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- معلومات المستخدم -->
            <div class="user-menu">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="userMenuDropdown" data-bs-toggle="dropdown">
                        <% if (user.avatar) { %>
                            <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>" class="user-avatar me-2">
                        <% } else { %>
                            <i class="fas fa-user-circle me-2"></i>
                        <% } %>
                        <%= user.username %>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="dropdown-header">
                            <div class="user-info-dropdown">
                                <% if (user.avatar) { %>
                                    <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>" class="user-avatar-large">
                                <% } else { %>
                                    <div class="user-avatar-large bg-primary">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <% } %>
                                <div class="user-details">
                                    <div class="username"><%= user.username %></div>
                                    <div class="user-id">#<%= user.discriminator || '0000' %></div>
                                </div>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="/dashboard/profile">
                                <i class="fas fa-user-circle me-2"></i>
                                <span data-en="Profile" data-ar="الملف الشخصي">الملف الشخصي</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="/dashboard/settings">
                                <i class="fas fa-cog me-2"></i>
                                <span data-en="Settings" data-ar="الإعدادات">الإعدادات</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="/support">
                                <i class="fas fa-life-ring me-2"></i>
                                <span data-en="Support" data-ar="الدعم">الدعم</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="/auth/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <span data-en="Logout" data-ar="تسجيل الخروج">تسجيل الخروج</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.navbar {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.navbar-brand-container {
    flex-grow: 1;
}

.navbar-title {
    color: white;
    font-weight: 600;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.7);
}

.breadcrumb-item.active {
    color: rgba(255,255,255,0.9);
}

.navbar-tools {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.user-avatar-large {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info-dropdown {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.user-details .username {
    font-weight: 600;
    color: #333;
}

.user-details .user-id {
    font-size: 0.875rem;
    color: #666;
}

.notification-dropdown {
    position: fixed !important;
    top: 80px !important;
    right: 20px !important;
    left: auto !important;
    transform: none !important;
    width: 380px;
    max-height: 500px;
    overflow-y: auto;
    z-index: 9999 !important;
    backdrop-filter: blur(15px);
    animation: slideDownNotification 0.3s ease-out;
}

@keyframes slideDownNotification {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.notification-text {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #999;
}

.quick-search .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.quick-search .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.quick-search .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.3);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.1);
}

/* تحسينات للموبايل */
@media (max-width: 767.98px) {
    .navbar-tools {
        gap: 0.5rem;
    }

    .quick-search {
        display: none !important;
    }

    .notification-dropdown {
        position: fixed !important;
        top: 70px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
        max-width: calc(100vw - 20px);
    }
}

/* دعم RTL */
[dir="rtl"] .dropdown-menu-end {
    right: auto;
    left: 0;
}

[dir="rtl"] .me-2 {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .me-3 {
    margin-right: 0;
    margin-left: 1rem;
}

/* دعم RTL للإشعارات */
[dir="rtl"] .notification-dropdown {
    right: auto !important;
    left: 20px !important;
}

@media (max-width: 767.98px) {
    [dir="rtl"] .notification-dropdown {
        left: 10px !important;
        right: 10px !important;
    }
}

/* تحسينات الإشعارات */
.notification-dropdown {
    width: 380px;
    max-height: 500px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    border-radius: 15px;
}

.notification-btn {
    position: relative;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    transform: scale(1.1);
}

.notification-badge {
    animation: pulse 2s infinite;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(-5px);
}

.notification-item.unread {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-left: 4px solid #667eea;
}

.notification-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.notification-text {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.notification-time {
    font-size: 0.7rem;
    color: #999;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.dropdown-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 15px 15px 0 0;
    margin: -0.5rem -0.5rem 0;
}

.dropdown-footer {
    background: #f8f9fa;
    margin: 0 -0.5rem -0.5rem;
    border-radius: 0 0 15px 15px;
}
</style>

<script>
// تبديل اللغة
function switchLanguage(lang) {
    localStorage.setItem('language', lang);

    // تحديث النصوص
    document.querySelectorAll('[data-en][data-ar]').forEach(element => {
        if (lang === 'en') {
            element.textContent = element.getAttribute('data-en');
        } else {
            element.textContent = element.getAttribute('data-ar');
        }
    });

    // تحديث اتجاه الصفحة
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;

    // تحديث عرض اللغة الحالية
    document.getElementById('currentLang').textContent = lang === 'ar' ? 'العربية' : 'English';

    // تحديث Bootstrap RTL
    if (lang === 'ar') {
        document.querySelector('link[href*="bootstrap"]').href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
    } else {
        document.querySelector('link[href*="bootstrap"]').href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
    }
}

// تحميل اللغة المحفوظة
document.addEventListener('DOMContentLoaded', function() {
    const savedLang = localStorage.getItem('language') || 'ar';
    switchLanguage(savedLang);

    // تحميل الإشعارات
    loadNotifications();
});

// تحميل الإشعارات
async function loadNotifications() {
    try {
        const response = await fetch('/api/notifications');
        const data = await response.json();

        if (data.success) {
            displayNotifications(data.notifications);
            updateNotificationCount(data.unreadCount);
        }
    } catch (error) {
        console.error('خطأ في تحميل الإشعارات:', error);
        // عرض إشعارات تجريبية
        displayDemoNotifications();
    }
}

// عرض الإشعارات
function displayNotifications(notifications) {
    const notificationList = document.getElementById('notificationList');

    if (!notifications || notifications.length === 0) {
        notificationList.innerHTML = `
            <div class="text-center py-4 text-muted">
                <i class="fas fa-bell-slash fa-2x mb-2"></i>
                <p>لا توجد إشعارات جديدة</p>
            </div>
        `;
        return;
    }

    notificationList.innerHTML = notifications.map(notification => `
        <a href="#" class="dropdown-item notification-item ${notification.read ? '' : 'unread'}"
           onclick="markAsRead('${notification.id}')">
            <div class="notification-icon ${getNotificationIconClass(notification.type)}">
                <i class="fas ${getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-text">${notification.message}</div>
                <div class="notification-time">
                    <i class="fas fa-clock"></i>
                    ${formatTime(notification.createdAt)}
                </div>
            </div>
        </a>
    `).join('');
}

// عرض إشعارات تجريبية
function displayDemoNotifications() {
    const demoNotifications = [
        {
            id: '1',
            type: 'server_join',
            title: 'تم إضافة البوت',
            message: 'تم إضافة البوت لسيرفر جديد: "مجتمع المطورين"',
            createdAt: new Date(Date.now() - 5 * 60 * 1000),
            read: false
        },
        {
            id: '2',
            type: 'user_register',
            title: 'مستخدم جديد',
            message: 'مستخدم جديد سجل في لوحة التحكم',
            createdAt: new Date(Date.now() - 15 * 60 * 1000),
            read: false
        },
        {
            id: '3',
            type: 'system_alert',
            title: 'تنبيه النظام',
            message: 'استخدام عالي للذاكرة - 85%',
            createdAt: new Date(Date.now() - 60 * 60 * 1000),
            read: true
        },
        {
            id: '4',
            type: 'premium_upgrade',
            title: 'ترقية بريميوم',
            message: 'تم ترقية سيرفر "الألعاب العربية" للبريميوم',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
            read: true
        },
        {
            id: '5',
            type: 'command_usage',
            title: 'استخدام مكثف',
            message: 'تم تنفيذ 1000+ أمر في آخر ساعة',
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
            read: false
        }
    ];

    displayNotifications(demoNotifications);
    updateNotificationCount(demoNotifications.filter(n => !n.read).length);
}

// تحديث عداد الإشعارات
function updateNotificationCount(count) {
    const badge = document.getElementById('notificationCount');
    if (count > 0) {
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'block';
    } else {
        badge.style.display = 'none';
    }
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        'server_join': 'fa-server',
        'user_register': 'fa-user-plus',
        'system_alert': 'fa-exclamation-triangle',
        'premium_upgrade': 'fa-crown',
        'command_usage': 'fa-terminal',
        'security': 'fa-shield-alt',
        'update': 'fa-download',
        'default': 'fa-bell'
    };
    return icons[type] || icons.default;
}

// الحصول على كلاس لون الأيقونة
function getNotificationIconClass(type) {
    const classes = {
        'server_join': 'bg-success',
        'user_register': 'bg-info',
        'system_alert': 'bg-warning',
        'premium_upgrade': 'bg-warning',
        'command_usage': 'bg-primary',
        'security': 'bg-danger',
        'update': 'bg-info',
        'default': 'bg-secondary'
    };
    return classes[type] || classes.default;
}

// تنسيق الوقت
function formatTime(date) {
    const now = new Date();
    const diff = now - new Date(date);
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `منذ ${days} ${days === 1 ? 'يوم' : 'أيام'}`;
    if (hours > 0) return `منذ ${hours} ${hours === 1 ? 'ساعة' : 'ساعات'}`;
    if (minutes > 0) return `منذ ${minutes} ${minutes === 1 ? 'دقيقة' : 'دقائق'}`;
    return 'الآن';
}

// تمييز الإشعار كمقروء
async function markAsRead(notificationId) {
    try {
        await fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST'
        });

        // تحديث العرض
        const notificationElement = document.querySelector(`[onclick="markAsRead('${notificationId}')"]`);
        if (notificationElement) {
            notificationElement.classList.remove('unread');
        }

        // تحديث العداد
        const currentCount = parseInt(document.getElementById('notificationCount').textContent) || 0;
        updateNotificationCount(Math.max(0, currentCount - 1));

    } catch (error) {
        console.error('خطأ في تمييز الإشعار كمقروء:', error);
    }
}

// تمييز جميع الإشعارات كمقروءة
async function markAllAsRead() {
    try {
        await fetch('/api/notifications/mark-all-read', {
            method: 'POST'
        });

        // تحديث العرض
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.classList.remove('unread');
        });

        // إخفاء العداد
        updateNotificationCount(0);

        showToast('تم تمييز جميع الإشعارات كمقروءة', 'success');

    } catch (error) {
        console.error('خطأ في تمييز جميع الإشعارات كمقروءة:', error);
        showToast('حدث خطأ في العملية', 'error');
    }
}

// عرض رسالة toast
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; animation: slideInRight 0.3s ease;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 5000);
}

// البحث السريع
document.getElementById('quickSearch')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const query = this.value.trim();
        if (query) {
            // تنفيذ البحث
            console.log('البحث عن:', query);
            // يمكن إضافة منطق البحث هنا
        }
    }
});

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');

    if (sidebar && overlay) {
        sidebar.classList.toggle('show');
        overlay.style.display = sidebar.classList.contains('show') ? 'block' : 'none';
    }
}
</script>
