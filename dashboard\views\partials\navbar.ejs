<!-- Navbar أسطوري ومتقدم -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow-sm">
    <div class="container-fluid">
        <!-- زر القائمة للموبايل -->
        <button class="navbar-toggler d-lg-none me-3" type="button" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- العنوان والمسار -->
        <div class="navbar-brand-container">
            <h4 class="navbar-title mb-0">
                <i class="fas fa-tachometer-alt text-primary me-2"></i>
                <span id="pageTitle" data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</span>
            </h4>
            <nav aria-label="breadcrumb" class="d-none d-md-block">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="text-decoration-none">
                            <i class="fas fa-home"></i>
                            <span data-en="Home" data-ar="الرئيسية">الرئيسية</span>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" id="currentPage" data-en="Dashboard" data-ar="لوحة التحكم">
                        لوحة التحكم
                    </li>
                </ol>
            </nav>
        </div>

        <!-- أدوات الـ Navbar -->
        <div class="navbar-tools">
            <!-- تبديل اللغة -->
            <div class="language-switcher me-3">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <span id="currentLang">العربية</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="#" onclick="switchLanguage('ar')">
                                <img src="https://flagcdn.com/w20/sa.png" alt="Arabic" class="me-2">
                                العربية
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="switchLanguage('en')">
                                <img src="https://flagcdn.com/w20/us.png" alt="English" class="me-2">
                                English
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- الإشعارات -->
            <div class="notifications me-3">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notificationCount">
                            3
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                        <div class="dropdown-header">
                            <i class="fas fa-bell me-2"></i>
                            <span data-en="Notifications" data-ar="الإشعارات">الإشعارات</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="notification-list">
                            <a href="#" class="dropdown-item notification-item">
                                <div class="notification-icon bg-success">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title" data-en="Bot Added" data-ar="تم إضافة البوت">تم إضافة البوت</div>
                                    <div class="notification-text" data-en="Bot was added to new server" data-ar="تم إضافة البوت لسيرفر جديد">تم إضافة البوت لسيرفر جديد</div>
                                    <div class="notification-time">منذ 5 دقائق</div>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item notification-item">
                                <div class="notification-icon bg-info">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title" data-en="New User" data-ar="مستخدم جديد">مستخدم جديد</div>
                                    <div class="notification-text" data-en="New user registered" data-ar="مستخدم جديد سجل">مستخدم جديد سجل</div>
                                    <div class="notification-time">منذ 15 دقيقة</div>
                                </div>
                            </a>
                            <a href="#" class="dropdown-item notification-item">
                                <div class="notification-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title" data-en="System Alert" data-ar="تنبيه النظام">تنبيه النظام</div>
                                    <div class="notification-text" data-en="High memory usage detected" data-ar="استخدام عالي للذاكرة">استخدام عالي للذاكرة</div>
                                    <div class="notification-time">منذ ساعة</div>
                                </div>
                            </a>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item text-center">
                            <span data-en="View All Notifications" data-ar="عرض جميع الإشعارات">عرض جميع الإشعارات</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- البحث السريع -->
            <div class="quick-search me-3 d-none d-md-block">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="بحث سريع..." id="quickSearch">
                    <button class="btn btn-outline-light" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- معلومات المستخدم -->
            <div class="user-menu">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="userMenuDropdown" data-bs-toggle="dropdown">
                        <% if (user.avatar) { %>
                            <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>" class="user-avatar me-2">
                        <% } else { %>
                            <i class="fas fa-user-circle me-2"></i>
                        <% } %>
                        <%= user.username %>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="dropdown-header">
                            <div class="user-info-dropdown">
                                <% if (user.avatar) { %>
                                    <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>" class="user-avatar-large">
                                <% } else { %>
                                    <div class="user-avatar-large bg-primary">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <% } %>
                                <div class="user-details">
                                    <div class="username"><%= user.username %></div>
                                    <div class="user-id">#<%= user.discriminator || '0000' %></div>
                                </div>
                            </div>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="/dashboard/profile">
                                <i class="fas fa-user-circle me-2"></i>
                                <span data-en="Profile" data-ar="الملف الشخصي">الملف الشخصي</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="/dashboard/settings">
                                <i class="fas fa-cog me-2"></i>
                                <span data-en="Settings" data-ar="الإعدادات">الإعدادات</span>
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="/support">
                                <i class="fas fa-life-ring me-2"></i>
                                <span data-en="Support" data-ar="الدعم">الدعم</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="/auth/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                <span data-en="Logout" data-ar="تسجيل الخروج">تسجيل الخروج</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.navbar {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.navbar-brand-container {
    flex-grow: 1;
}

.navbar-title {
    color: white;
    font-weight: 600;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.7);
}

.breadcrumb-item.active {
    color: rgba(255,255,255,0.9);
}

.navbar-tools {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.user-avatar-large {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info-dropdown {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.user-details .username {
    font-weight: 600;
    color: #333;
}

.user-details .user-id {
    font-size: 0.875rem;
    color: #666;
}

.notification-dropdown {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: rgba(0,0,0,0.02);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.notification-text {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #999;
}

.quick-search .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.quick-search .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.quick-search .form-control:focus {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.3);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.1);
}

/* تحسينات للموبايل */
@media (max-width: 767.98px) {
    .navbar-tools {
        gap: 0.5rem;
    }
    
    .quick-search {
        display: none !important;
    }
    
    .notification-dropdown {
        width: 300px;
    }
}

/* دعم RTL */
[dir="rtl"] .dropdown-menu-end {
    right: auto;
    left: 0;
}

[dir="rtl"] .me-2 {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .me-3 {
    margin-right: 0;
    margin-left: 1rem;
}
</style>

<script>
// تبديل اللغة
function switchLanguage(lang) {
    localStorage.setItem('language', lang);
    
    // تحديث النصوص
    document.querySelectorAll('[data-en][data-ar]').forEach(element => {
        if (lang === 'en') {
            element.textContent = element.getAttribute('data-en');
        } else {
            element.textContent = element.getAttribute('data-ar');
        }
    });
    
    // تحديث اتجاه الصفحة
    document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lang;
    
    // تحديث عرض اللغة الحالية
    document.getElementById('currentLang').textContent = lang === 'ar' ? 'العربية' : 'English';
    
    // تحديث Bootstrap RTL
    if (lang === 'ar') {
        document.querySelector('link[href*="bootstrap"]').href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
    } else {
        document.querySelector('link[href*="bootstrap"]').href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
    }
}

// تحميل اللغة المحفوظة
document.addEventListener('DOMContentLoaded', function() {
    const savedLang = localStorage.getItem('language') || 'ar';
    switchLanguage(savedLang);
});

// البحث السريع
document.getElementById('quickSearch')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const query = this.value.trim();
        if (query) {
            // تنفيذ البحث
            console.log('البحث عن:', query);
            // يمكن إضافة منطق البحث هنا
        }
    }
});

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    if (sidebar && overlay) {
        sidebar.classList.toggle('show');
        overlay.style.display = sidebar.classList.contains('show') ? 'block' : 'none';
    }
}
</script>
