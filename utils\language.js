/**
 * نظام اللغات المتقدم - CS Bot
 * يدعم العربية والإنجليزية مع إمكانية إضافة لغات جديدة
 */

const fs = require('fs');
const path = require('path');

class LanguageManager {
    constructor() {
        this.languages = {};
        this.defaultLanguage = 'ar';
        this.loadLanguages();
    }

    loadLanguages() {
        const langDir = path.join(__dirname, '../languages');

        if (!fs.existsSync(langDir)) {
            fs.mkdirSync(langDir, { recursive: true });
            this.createDefaultLanguageFiles();
        }

        const files = fs.readdirSync(langDir).filter(file => file.endsWith('.json'));

        for (const file of files) {
            const langCode = file.replace('.json', '');
            const langPath = path.join(langDir, file);

            try {
                this.languages[langCode] = JSON.parse(fs.readFileSync(langPath, 'utf8'));
                console.log(`✅ تم تحميل لغة: ${langCode}`);
            } catch (error) {
                console.error(`❌ خطأ في تحميل لغة ${langCode}:`, error);
            }
        }
    }

    createDefaultLanguageFiles() {
        // إنشاء ملف اللغة العربية
        const arabicLang = {
            // رسائل عامة
            general: {
                error: "❌ حدث خطأ غير متوقع!",
                success: "✅ تم بنجاح!",
                loading: "⏳ جاري التحميل...",
                notFound: "❌ غير موجود!",
                noPermission: "❌ ليس لديك صلاحية لاستخدام هذا الأمر!",
                cooldown: "⏰ يجب الانتظار {time} ثانية قبل استخدام هذا الأمر مرة أخرى!",
                invalidUser: "❌ المستخدم غير صحيح!",
                invalidChannel: "❌ القناة غير صحيحة!",
                botMissingPermissions: "❌ البوت لا يملك الصلاحيات المطلوبة!",
                userMissingPermissions: "❌ ليس لديك الصلاحيات المطلوبة!"
            },

            // أوامر المساعدة
            help: {
                title: "📚 قائمة الأوامر - CS Bot",
                description: "إليك جميع الأوامر المتاحة:",
                usage: "الاستخدام",
                example: "مثال",
                aliases: "الأسماء البديلة",
                category: "الفئة",
                categories: {
                    general: "🔧 عام",
                    moderation: "🛡️ الإدارة",
                    fun: "🎮 الترفيه",
                    economy: "💰 الاقتصاد",
                    music: "🎵 الموسيقى",
                    utility: "🔨 الأدوات",
                    admin: "👑 المطور"
                }
            },

            // أوامر الاقتصاد
            economy: {
                balance: "💰 رصيدك الحالي: **{balance}** عملة",
                daily: "🎁 تم استلام المكافأة اليومية: **{amount}** عملة!",
                dailyAlready: "⏰ لقد استلمت المكافأة اليومية بالفعل! عد بعد {time}",
                work: "💼 عملت بجد وحصلت على **{amount}** عملة!",
                workCooldown: "😴 أنت متعب! استرح لمدة {time} قبل العمل مرة أخرى",
                pay: "💸 تم تحويل **{amount}** عملة إلى {user}",
                payError: "❌ لا يمكنك تحويل هذا المبلغ!",
                rob: "🔫 سرقت **{amount}** عملة من {user}!",
                robFailed: "🛡️ فشلت في سرقة {user}!",
                shop: "🛒 متجر السيرفر",
                buy: "✅ تم شراء **{item}** بـ **{price}** عملة!",
                sell: "💰 تم بيع **{item}** بـ **{price}** عملة!"
            },

            // أوامر الإدارة
            moderation: {
                ban: "🔨 تم حظر {user} من السيرفر!\nالسبب: {reason}",
                unban: "✅ تم إلغاء حظر {user}",
                kick: "👢 تم طرد {user} من السيرفر!\nالسبب: {reason}",
                mute: "🔇 تم كتم {user} لمدة {time}!\nالسبب: {reason}",
                unmute: "🔊 تم إلغاء كتم {user}",
                warn: "⚠️ تم تحذير {user}!\nالسبب: {reason}\nعدد التحذيرات: {count}",
                clear: "🧹 تم حذف {count} رسالة!",
                lock: "🔒 تم قفل القناة!",
                unlock: "🔓 تم فتح القناة!",
                slowmode: "⏰ تم تفعيل الوضع البطيء: {time} ثانية"
            },

            // أوامر الترفيه
            fun: {
                meme: "😂 إليك ميم عشوائي!",
                joke: "😄 نكتة اليوم",
                fact: "🧠 هل تعلم؟",
                quote: "💭 اقتباس ملهم",
                "8ball": "🎱 الكرة السحرية تقول: {answer}",
                coinflip: "🪙 النتيجة: {result}",
                dice: "🎲 النرد: {result}",
                rps: "✂️ حجر ورقة مقص"
            },

            // أوامر الموسيقى
            music: {
                playing: "🎵 يتم تشغيل: **{song}**",
                added: "➕ تم إضافة **{song}** إلى قائمة التشغيل",
                stopped: "⏹️ تم إيقاف الموسيقى",
                paused: "⏸️ تم إيقاف الموسيقى مؤقتاً",
                resumed: "▶️ تم استئناف الموسيقى",
                skipped: "⏭️ تم تخطي الأغنية",
                queue: "📋 قائمة التشغيل",
                volume: "🔊 مستوى الصوت: {volume}%",
                noQueue: "❌ قائمة التشغيل فارغة!",
                notInVoice: "❌ يجب أن تكون في قناة صوتية!",
                botNotInVoice: "❌ البوت ليس في قناة صوتية!"
            },

            // رسائل النظام
            system: {
                welcome: "🎉 مرحباً {user} في **{server}**!\nأنت العضو رقم **{count}**",
                goodbye: "👋 وداعاً **{user}**!\nنتمنى أن نراك قريباً",
                levelUp: "🎊 تهانينا {user}! وصلت للمستوى **{level}**!",
                newMember: "📈 عضو جديد انضم للسيرفر!",
                memberLeft: "📉 عضو غادر السيرفر",
                botJoined: "🤖 شكراً لإضافة CS Bot إلى سيرفرك!",
                botLeft: "👋 تم إزالة CS Bot من السيرفر"
            }
        };

        // إنشاء ملف اللغة الإنجليزية
        const englishLang = {
            // General messages
            general: {
                error: "❌ An unexpected error occurred!",
                success: "✅ Success!",
                loading: "⏳ Loading...",
                notFound: "❌ Not found!",
                noPermission: "❌ You don't have permission to use this command!",
                cooldown: "⏰ Please wait {time} seconds before using this command again!",
                invalidUser: "❌ Invalid user!",
                invalidChannel: "❌ Invalid channel!",
                botMissingPermissions: "❌ Bot is missing required permissions!",
                userMissingPermissions: "❌ You are missing required permissions!"
            },

            // Help commands
            help: {
                title: "📚 Commands List - CS Bot",
                description: "Here are all available commands:",
                usage: "Usage",
                example: "Example",
                aliases: "Aliases",
                category: "Category",
                categories: {
                    general: "🔧 General",
                    moderation: "🛡️ Moderation",
                    fun: "🎮 Fun",
                    economy: "💰 Economy",
                    music: "🎵 Music",
                    utility: "🔨 Utility",
                    admin: "👑 Admin"
                }
            },

            // Economy commands
            economy: {
                balance: "💰 Your current balance: **{balance}** coins",
                daily: "🎁 Daily reward claimed: **{amount}** coins!",
                dailyAlready: "⏰ You already claimed your daily reward! Come back in {time}",
                work: "💼 You worked hard and earned **{amount}** coins!",
                workCooldown: "😴 You're tired! Rest for {time} before working again",
                pay: "💸 Transferred **{amount}** coins to {user}",
                payError: "❌ You can't transfer this amount!",
                rob: "🔫 You robbed **{amount}** coins from {user}!",
                robFailed: "🛡️ Failed to rob {user}!",
                shop: "🛒 Server Shop",
                buy: "✅ Bought **{item}** for **{price}** coins!",
                sell: "💰 Sold **{item}** for **{price}** coins!"
            },

            // Moderation commands
            moderation: {
                ban: "🔨 Banned {user} from the server!\nReason: {reason}",
                unban: "✅ Unbanned {user}",
                kick: "👢 Kicked {user} from the server!\nReason: {reason}",
                mute: "🔇 Muted {user} for {time}!\nReason: {reason}",
                unmute: "🔊 Unmuted {user}",
                warn: "⚠️ Warned {user}!\nReason: {reason}\nWarning count: {count}",
                clear: "🧹 Deleted {count} messages!",
                lock: "🔒 Channel locked!",
                unlock: "🔓 Channel unlocked!",
                slowmode: "⏰ Slowmode enabled: {time} seconds"
            },

            // Fun commands
            fun: {
                meme: "😂 Here's a random meme!",
                joke: "😄 Joke of the day",
                fact: "🧠 Did you know?",
                quote: "💭 Inspirational quote",
                "8ball": "🎱 Magic 8-ball says: {answer}",
                coinflip: "🪙 Result: {result}",
                dice: "🎲 Dice: {result}",
                rps: "✂️ Rock Paper Scissors"
            },

            // Music commands
            music: {
                playing: "🎵 Now playing: **{song}**",
                added: "➕ Added **{song}** to the queue",
                stopped: "⏹️ Music stopped",
                paused: "⏸️ Music paused",
                resumed: "▶️ Music resumed",
                skipped: "⏭️ Song skipped",
                queue: "📋 Music Queue",
                volume: "🔊 Volume: {volume}%",
                noQueue: "❌ Queue is empty!",
                notInVoice: "❌ You must be in a voice channel!",
                botNotInVoice: "❌ Bot is not in a voice channel!"
            },

            // System messages
            system: {
                welcome: "🎉 Welcome {user} to **{server}**!\nYou are member number **{count}**",
                goodbye: "👋 Goodbye **{user}**!\nHope to see you soon",
                levelUp: "🎊 Congratulations {user}! You reached level **{level}**!",
                newMember: "📈 New member joined the server!",
                memberLeft: "📉 Member left the server",
                botJoined: "🤖 Thanks for adding CS Bot to your server!",
                botLeft: "👋 CS Bot was removed from the server"
            }
        };

        // حفظ الملفات
        const langDir = path.join(__dirname, '../languages');
        fs.writeFileSync(path.join(langDir, 'ar.json'), JSON.stringify(arabicLang, null, 2));
        fs.writeFileSync(path.join(langDir, 'en.json'), JSON.stringify(englishLang, null, 2));

        console.log('✅ تم إنشاء ملفات اللغات الافتراضية');
    }

    get(key, lang = this.defaultLanguage, replacements = {}) {
        const language = this.languages[lang] || this.languages[this.defaultLanguage];

        if (!language) {
            console.error(`❌ اللغة ${lang} غير موجودة`);
            return key;
        }

        const keys = key.split('.');
        let value = language;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                console.warn(`⚠️ المفتاح ${key} غير موجود في اللغة ${lang}`);
                return key;
            }
        }

        if (typeof value !== 'string') {
            return key;
        }

        // استبدال المتغيرات
        let result = value;
        for (const [placeholder, replacement] of Object.entries(replacements)) {
            result = result.replace(new RegExp(`{${placeholder}}`, 'g'), replacement);
        }

        return result;
    }

    getSupportedLanguages() {
        return Object.keys(this.languages);
    }

    isSupported(lang) {
        return lang in this.languages;
    }

    setDefaultLanguage(lang) {
        if (this.isSupported(lang)) {
            this.defaultLanguage = lang;
            return true;
        }
        return false;
    }
}

module.exports = new LanguageManager();
