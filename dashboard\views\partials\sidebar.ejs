<!-- Sidebar أسطوري ومتقدم -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="logo-container">
            <% if (bot && bot.user && bot.user.avatar) { %>
                <img src="https://cdn.discordapp.com/avatars/<%= bot.user.id %>/<%= bot.user.avatar %>.png"
                     alt="CS Bot" class="logo">
            <% } else { %>
                <div class="logo-fallback">
                    <i class="fas fa-robot"></i>
                </div>
            <% } %>
            <div class="logo-text">
                <h3 class="mb-0">CS Bot</h3>
                <small class="text-muted">Dashboard</small>
                <% if (typeof isOwner !== 'undefined' && isOwner) { %>
                    <div class="owner-badge">
                        <i class="fas fa-crown text-warning"></i>
                        <span class="text-warning">مالك</span>
                    </div>
                <% } %>
            </div>
        </div>
        <button class="sidebar-toggle d-lg-none" onclick="toggleSidebar()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="sidebar-content">
        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div class="user-avatar">
                <% if (user.avatar) { %>
                    <img src="https://cdn.discordapp.com/avatars/<%= user.id %>/<%= user.avatar %>.png" alt="<%= user.username %>">
                <% } else { %>
                    <div class="default-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                <% } %>
                <div class="status-indicator online"></div>
            </div>
            <div class="user-details">
                <h6 class="username"><%= user.username %></h6>
                <small class="user-id">#<%= user.discriminator || '0000' %></small>
            </div>
        </div>

        <!-- القائمة الرئيسية -->
        <nav class="sidebar-nav">
            <div class="nav-section">
                <h6 class="nav-section-title">
                    <i class="fas fa-home"></i>
                    <span data-en="Main" data-ar="الرئيسية">الرئيسية</span>
                </h6>
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="/dashboard" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span data-en="Dashboard" data-ar="لوحة التحكم">لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/my-servers" class="nav-link">
                            <i class="fas fa-server"></i>
                            <span data-en="My Servers" data-ar="سيرفراتي">سيرفراتي</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/profile" class="nav-link">
                            <i class="fas fa-user-circle"></i>
                            <span data-en="Profile" data-ar="الملف الشخصي">الملف الشخصي</span>
                        </a>
                    </li>
                </ul>
            </div>

            <% if (typeof isOwner !== 'undefined' && isOwner) { %>
            <!-- قسم المطور -->
            <div class="nav-section">
                <h6 class="nav-section-title">
                    <i class="fas fa-crown text-warning"></i>
                    <span data-en="Admin Panel" data-ar="لوحة المطور">لوحة المطور</span>
                </h6>
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="/dashboard/users" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span data-en="Users" data-ar="المستخدمين">المستخدمين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/servers" class="nav-link">
                            <i class="fas fa-server"></i>
                            <span data-en="All Servers" data-ar="جميع السيرفرات">جميع السيرفرات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/analytics" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span data-en="Analytics" data-ar="التحليلات">التحليلات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/premium" class="nav-link">
                            <i class="fas fa-crown"></i>
                            <span data-en="Premium" data-ar="البريميوم">البريميوم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/notifications" class="nav-link">
                            <i class="fas fa-bell"></i>
                            <span data-en="Notifications" data-ar="الإشعارات">الإشعارات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/dashboard/settings" class="nav-link">
                            <i class="fas fa-cogs"></i>
                            <span data-en="Settings" data-ar="الإعدادات">الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </div>
            <% } %>

            <!-- أدوات -->
            <div class="nav-section">
                <h6 class="nav-section-title">
                    <i class="fas fa-tools"></i>
                    <span data-en="Tools" data-ar="الأدوات">الأدوات</span>
                </h6>
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="https://discord.com/api/oauth2/authorize?client_id=<%= config.clientId %>&permissions=8&scope=bot%20applications.commands" target="_blank" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            <span data-en="Add Bot" data-ar="إضافة البوت">إضافة البوت</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/support" class="nav-link">
                            <i class="fas fa-life-ring"></i>
                            <span data-en="Support" data-ar="الدعم">الدعم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/docs" class="nav-link">
                            <i class="fas fa-book"></i>
                            <span data-en="Documentation" data-ar="التوثيق">التوثيق</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- معلومات البوت -->
        <div class="bot-status">
            <div class="status-card">
                <div class="status-header">
                    <i class="fas fa-robot"></i>
                    <span data-en="Bot Status" data-ar="حالة البوت">حالة البوت</span>
                </div>
                <div class="status-body">
                    <div class="status-item">
                        <span class="status-dot online"></span>
                        <span data-en="Online" data-ar="متصل">متصل</span>
                    </div>
                    <div class="status-item">
                        <i class="fas fa-server"></i>
                        <span><%= typeof bot !== 'undefined' && bot.guilds ? bot.guilds.cache.size : 0 %></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تذييل الشريط الجانبي -->
    <div class="sidebar-footer">
        <div class="footer-links">
            <a href="/dashboard/terms" class="footer-link">
                <span data-en="Terms" data-ar="الشروط">الشروط</span>
            </a>
            <a href="/dashboard/privacy" class="footer-link">
                <span data-en="Privacy" data-ar="الخصوصية">الخصوصية</span>
            </a>
        </div>
        <div class="footer-copyright">
            <small>&copy; 2024 CS Bot</small>
        </div>
    </div>
</div>

<!-- Overlay للموبايل -->
<div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    z-index: 1000;
    transform: translateX(0);
    transition: transform 0.3s ease;
    overflow-y: auto;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
    object-fit: cover;
}

.logo-fallback {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.logo-text h3 {
    color: white;
    font-weight: 700;
}

.owner-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    padding: 0.125rem 0.5rem;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
}

.user-info {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.user-avatar {
    position: relative;
}

.user-avatar img, .default-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.2);
}

.default-avatar {
    background: rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    left: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-indicator.online {
    background: #43a047;
}

.username {
    color: white;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.user-id {
    color: rgba(255,255,255,0.7);
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    padding: 0 1.5rem 0.5rem;
    color: rgba(255,255,255,0.8);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-right-color: rgba(255,255,255,0.5);
}

.nav-link.active {
    background: rgba(255,255,255,0.15);
    color: white;
    border-right-color: white;
}

.bot-status {
    padding: 1rem 1.5rem;
    margin-top: auto;
}

.status-card {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 1rem;
}

.status-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    margin-top: auto;
}

.footer-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.footer-link {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
}

/* Responsive */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar.show + .sidebar-overlay {
        display: block;
    }
}

/* RTL Support */
[dir="rtl"] .sidebar {
    right: auto;
    left: 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

[dir="rtl"] .nav-link {
    border-right: none;
    border-left: 3px solid transparent;
}

[dir="rtl"] .nav-link:hover,
[dir="rtl"] .nav-link.active {
    border-left-color: rgba(255,255,255,0.5);
}
</style>
