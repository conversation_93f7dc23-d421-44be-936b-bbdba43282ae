/**
 * أمر المساعدة الأسطوري
 */

module.exports = {
    name: 'help',
    description: 'عرض قائمة الأوامر',
    usage: '!help [command]',
    category: 'general',
    
    async execute(message, args, client) {
        try {
            if (args[0]) {
                // عرض تفاصيل أمر محدد
                const command = client.commands.get(args[0]);
                if (!command) {
                    return message.reply('❌ لم أجد هذا الأمر!');
                }

                const embed = {
                    color: 0x00FF00,
                    title: `📖 تفاصيل الأمر: ${command.name}`,
                    fields: [
                        {
                            name: '📝 الوصف',
                            value: command.description || 'لا يوجد وصف',
                            inline: false
                        },
                        {
                            name: '💡 الاستخدام',
                            value: `\`${command.usage || `!${command.name}`}\``,
                            inline: false
                        },
                        {
                            name: '📂 الفئة',
                            value: command.category || 'عام',
                            inline: true
                        }
                    ],
                    footer: {
                        text: 'CS Bot - نظام المساعدة',
                        icon_url: client.user.displayAvatarURL()
                    },
                    timestamp: new Date()
                };

                return message.reply({ embeds: [embed] });
            }

            // عرض جميع الأوامر
            const categories = {};
            
            client.commands.forEach(command => {
                const category = command.category || 'عام';
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push(command);
            });

            const embed = {
                color: 0x0099FF,
                title: '🤖 قائمة أوامر CS Bot',
                description: `مرحباً ${message.author}! إليك جميع الأوامر المتاحة:\n\n**البادئة:** \`${client.config.prefix}\``,
                fields: [],
                footer: {
                    text: `إجمالي الأوامر: ${client.commands.size} | استخدم !help <اسم الأمر> لمزيد من التفاصيل`,
                    icon_url: client.user.displayAvatarURL()
                },
                timestamp: new Date()
            };

            // إضافة الفئات
            const categoryEmojis = {
                'admin': '👑',
                'general': '📋',
                'fun': '🎮',
                'moderation': '🛡️',
                'economy': '💰',
                'music': '🎵'
            };

            Object.keys(categories).forEach(category => {
                const emoji = categoryEmojis[category] || '📁';
                const commands = categories[category].map(cmd => `\`${cmd.name}\``).join(', ');
                
                embed.fields.push({
                    name: `${emoji} ${category.charAt(0).toUpperCase() + category.slice(1)}`,
                    value: commands,
                    inline: false
                });
            });

            // إضافة روابط مفيدة
            embed.fields.push({
                name: '🔗 روابط مفيدة',
                value: '[لوحة التحكم](http://localhost:3000) | [الدعم](http://localhost:3000/support) | [البريميوم](http://localhost:3000/premium)',
                inline: false
            });

            await message.reply({ embeds: [embed] });

        } catch (error) {
            console.error('خطأ في أمر help:', error);
            message.reply('❌ حدث خطأ أثناء عرض المساعدة!');
        }
    }
};
