const { EmbedBuilder, ActionRowBuilder, Button<PERSON>uilder, ButtonStyle } = require('discord.js');

module.exports = {
    name: 'ai',
    description: 'الذكاء الاصطناعي المتقدم - اسأل أي سؤال!',
    usage: '!ai <سؤالك>',
    category: 'ذكاء اصطناعي',
    cooldown: 5,
    
    async execute(message, args) {
        try {
            if (!args.length) {
                return this.showAIPanel(message);
            }
            
            const question = args.join(' ');
            
            // إرسال رسالة "جاري الكتابة..."
            await message.channel.sendTyping();
            
            const thinkingEmbed = new EmbedBuilder()
                .setColor('#00bfff')
                .setTitle('🤖 الذكاء الاصطناعي يفكر...')
                .setDescription(`**سؤالك:** ${question}`)
                .addFields([
                    { name: '⏳ الحالة', value: 'جاري معالجة السؤال...', inline: true },
                    { name: '🧠 النموذج', value: 'CS-AI v2.0', inline: true }
                ])
                .setFooter({ text: 'قد يستغرق هذا بضع ثوان...' })
                .setTimestamp();
                
            const thinkingMsg = await message.reply({ embeds: [thinkingEmbed] });
            
            // محاكاة وقت المعالجة
            setTimeout(async () => {
                const response = this.generateAIResponse(question);
                
                const responseEmbed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle('🤖 إجابة الذكاء الاصطناعي')
                    .addFields([
                        { name: '❓ سؤالك', value: question, inline: false },
                        { name: '💡 الإجابة', value: response, inline: false }
                    ])
                    .setFooter({ text: 'CS AI • قد تحتوي الإجابة على أخطاء' })
                    .setTimestamp();
                    
                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('ai_regenerate')
                            .setLabel('إعادة توليد')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('🔄'),
                        new ButtonBuilder()
                            .setCustomId('ai_translate')
                            .setLabel('ترجمة')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('🌐'),
                        new ButtonBuilder()
                            .setCustomId('ai_explain')
                            .setLabel('شرح مفصل')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('📚'),
                        new ButtonBuilder()
                            .setCustomId('ai_rate')
                            .setLabel('تقييم الإجابة')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('⭐')
                    );
                    
                await thinkingMsg.edit({ embeds: [responseEmbed], components: [row] });
                
            }, Math.random() * 3000 + 2000); // 2-5 ثوان
            
        } catch (error) {
            console.error('خطأ في أمر الذكاء الاصطناعي:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ خطأ في الذكاء الاصطناعي')
                .setDescription('حدث خطأ أثناء معالجة سؤالك. يرجى المحاولة مرة أخرى.')
                .setTimestamp();
                
            return message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async showAIPanel(message) {
        const embed = new EmbedBuilder()
            .setColor('#00bfff')
            .setTitle('🤖 الذكاء الاصطناعي المتقدم')
            .setDescription('مرحباً! أنا CS AI، مساعدك الذكي. يمكنني مساعدتك في:')
            .addFields([
                {
                    name: '📚 التعليم والدراسة',
                    value: '• شرح المفاهيم العلمية\n• حل المسائل الرياضية\n• مساعدة في البحوث',
                    inline: true
                },
                {
                    name: '💻 البرمجة والتقنية',
                    value: '• كتابة الكود\n• إصلاح الأخطاء\n• شرح الخوارزميات',
                    inline: true
                },
                {
                    name: '🎨 الإبداع والكتابة',
                    value: '• كتابة القصص\n• الشعر والأدب\n• الأفكار الإبداعية',
                    inline: true
                },
                {
                    name: '🌍 المعرفة العامة',
                    value: '• التاريخ والجغرافيا\n• العلوم والطبيعة\n• الثقافة والفنون',
                    inline: true
                },
                {
                    name: '🔧 المساعدة العملية',
                    value: '• النصائح والإرشادات\n• حل المشاكل\n• التخطيط والتنظيم',
                    inline: true
                },
                {
                    name: '🎯 كيفية الاستخدام',
                    value: '`!ai <سؤالك هنا>`\n\nمثال: `!ai ما هو الذكاء الاصطناعي؟`',
                    inline: true
                }
            ])
            .setFooter({ text: 'CS AI v2.0 • اسأل أي سؤال!' })
            .setTimestamp();
            
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('ai_example_science')
                    .setLabel('سؤال علمي')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔬'),
                new ButtonBuilder()
                    .setCustomId('ai_example_code')
                    .setLabel('سؤال برمجة')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💻'),
                new ButtonBuilder()
                    .setCustomId('ai_example_creative')
                    .setLabel('سؤال إبداعي')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎨'),
                new ButtonBuilder()
                    .setCustomId('ai_help')
                    .setLabel('مساعدة')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
            );
            
        return message.reply({ embeds: [embed], components: [row] });
    },
    
    generateAIResponse(question) {
        const responses = {
            // أسئلة البرمجة
            'برمجة': [
                'البرمجة هي فن كتابة التعليمات للحاسوب لحل المشاكل. تتطلب التفكير المنطقي والإبداع.',
                'لتعلم البرمجة، ابدأ بلغة سهلة مثل Python، ثم مارس حل المشاكل يومياً.',
                'أفضل طريقة لتحسين مهارات البرمجة هي الممارسة المستمرة والعمل على مشاريع حقيقية.'
            ],
            
            // أسئلة الذكاء الاصطناعي
            'ذكاء اصطناعي': [
                'الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم.',
                'يستخدم الذكاء الاصطناعي في العديد من المجالات مثل الطب، التعليم، والنقل.',
                'مستقبل الذكاء الاصطناعي مشرق ولكن يتطلب استخداماً أخلاقياً ومسؤولاً.'
            ],
            
            // أسئلة عامة
            'عام': [
                'هذا سؤال مثير للاهتمام! دعني أفكر فيه من زوايا مختلفة...',
                'بناءً على معرفتي، يمكنني القول أن هذا الموضوع معقد ويحتاج تحليل دقيق.',
                'هناك عدة وجهات نظر حول هذا الموضوع، وكل منها له مبرراته.',
                'من المهم النظر إلى هذا السؤال من منظور شامل يأخذ في الاعتبار جميع العوامل.',
                'هذا سؤال ممتاز! يظهر تفكيراً عميقاً ورغبة في الفهم.'
            ]
        };
        
        const lowerQuestion = question.toLowerCase();
        
        if (lowerQuestion.includes('برمج') || lowerQuestion.includes('كود') || lowerQuestion.includes('program')) {
            return responses['برمجة'][Math.floor(Math.random() * responses['برمجة'].length)];
        } else if (lowerQuestion.includes('ذكاء') || lowerQuestion.includes('ai') || lowerQuestion.includes('اصطناعي')) {
            return responses['ذكاء اصطناعي'][Math.floor(Math.random() * responses['ذكاء اصطناعي'].length)];
        } else {
            return responses['عام'][Math.floor(Math.random() * responses['عام'].length)];
        }
    }
};
