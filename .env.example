# CS Bot Configuration File
# انسخ هذا الملف إلى .env وأدخل القيم الصحيحة

# ===== إعدادات البوت الأساسية =====
# توكن البوت من Discord Developer Portal
DISCORD_TOKEN=YOUR_BOT_TOKEN_HERE

# معرف البوت (Client ID)
CLIENT_ID=YOUR_CLIENT_ID_HERE

# سر العميل (Client Secret) - مطلوب للـ OAuth
CLIENT_SECRET=YOUR_CLIENT_SECRET_HERE

# ===== إعدادات قاعدة البيانات =====
# رابط MongoDB (اتركه فارغاً لاستخدام قاعدة البيانات المؤقتة)
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# ===== إعدادات لوحة التحكم =====
# منفذ لوحة التحكم
PORT=3000

# رابط لوحة التحكم
DASHBOARD_DOMAIN=http://localhost:3000

# سر الجلسة (اختر نص عشوائي قوي)
SESSION_SECRET=your_super_secret_session_key_here_make_it_long_and_random

# ===== إعدادات الـ Webhook =====
# رابط الـ webhook للإشعارات المهمة
WEBHOOK_URL=https://discord.com/api/webhooks/1377228284856893450/PLuaHJQ9un0T3bXCQ0cqVDfL0e1nbzlzPpdgRXOkyzHh8vrhu6dt2lyJOgDqXUrliNfl

# تفعيل/إلغاء تفعيل الـ webhook (true/false)
WEBHOOK_ENABLED=true

# ===== إعدادات المطورين =====
# معرفات مالكي البوت (افصل بينها بفاصلة)
BOT_OWNERS=1289581696995561495,123456789012345678

# ===== إعدادات البريميوم =====
# تفعيل نظام البريميوم (true/false)
PREMIUM_ENABLED=true

# مدة البريميوم الافتراضية بالأيام
DEFAULT_PREMIUM_DURATION=30

# ===== إعدادات الأمان =====
# تفعيل وضع التطوير (true/false)
DEVELOPMENT_MODE=false

# تفعيل السجلات المفصلة (true/false)
VERBOSE_LOGGING=true

# الحد الأقصى لمعدل الطلبات (requests per minute)
RATE_LIMIT=60

# ===== إعدادات الذكاء الاصطناعي (اختياري) =====
# مفتاح OpenAI API (للميزات المتقدمة)
OPENAI_API_KEY=your_openai_api_key_here

# مفتاح Google API (للبحث والترجمة)
GOOGLE_API_KEY=your_google_api_key_here

# ===== إعدادات التخزين =====
# مجلد التخزين المؤقت
TEMP_DIR=./temp

# مجلد الملفات المرفوعة
UPLOADS_DIR=./uploads

# الحد الأقصى لحجم الملف (بالميجابايت)
MAX_FILE_SIZE=10

# ===== إعدادات الشبكة =====
# البروكسي (اختياري)
HTTP_PROXY=

# مهلة الاتصال (بالثواني)
CONNECTION_TIMEOUT=30

# ===== إعدادات التحليلات =====
# تفعيل تتبع الإحصائيات (true/false)
ANALYTICS_ENABLED=true

# تفعيل تتبع الأخطاء (true/false)
ERROR_TRACKING=true

# ===== إعدادات البريد الإلكتروني (اختياري) =====
# خادم SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# ===== إعدادات التخزين السحابي (اختياري) =====
# AWS S3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_BUCKET_NAME=your_bucket_name

# ===== إعدادات Redis (اختياري) =====
# رابط Redis للتخزين المؤقت المتقدم
REDIS_URL=redis://localhost:6379

# ===== إعدادات الأمان المتقدم =====
# تفعيل التشفير (true/false)
ENCRYPTION_ENABLED=false

# مفتاح التشفير (32 حرف)
ENCRYPTION_KEY=your_32_character_encryption_key

# ===== إعدادات اللغة =====
# اللغة الافتراضية (ar/en)
DEFAULT_LANGUAGE=ar

# تفعيل الترجمة التلقائية (true/false)
AUTO_TRANSLATE=false

# ===== إعدادات الصيانة =====
# وضع الصيانة (true/false)
MAINTENANCE_MODE=false

# رسالة الصيانة
MAINTENANCE_MESSAGE=البوت تحت الصيانة، سيعود قريباً

# ===== إعدادات النسخ الاحتياطي =====
# تفعيل النسخ الاحتياطي التلقائي (true/false)
AUTO_BACKUP=true

# فترة النسخ الاحتياطي (بالساعات)
BACKUP_INTERVAL=24

# مجلد النسخ الاحتياطية
BACKUP_DIR=./backups

# ===== إعدادات الأداء =====
# عدد العمليات المتوازية
MAX_CONCURRENT_OPERATIONS=10

# حجم ذاكرة التخزين المؤقت (بالميجابايت)
CACHE_SIZE=100

# مدة انتهاء صلاحية التخزين المؤقت (بالدقائق)
CACHE_TTL=60

# ===== إعدادات التطوير =====
# تفعيل إعادة التحميل التلقائي (true/false)
AUTO_RELOAD=false

# تفعيل وضع التصحيح (true/false)
DEBUG_MODE=false

# منفذ التصحيح
DEBUG_PORT=9229

# ===== ملاحظات =====
# 1. لا تشارك ملف .env مع أي شخص
# 2. أضف .env إلى ملف .gitignore
# 3. استخدم كلمات مرور قوية
# 4. غير المفاتيح بانتظام
# 5. احتفظ بنسخة احتياطية آمنة من الإعدادات
