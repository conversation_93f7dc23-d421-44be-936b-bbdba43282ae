/**
 * CS Bot - تشغيل لوحة التحكم فقط (للاختبار)
 * يشغل لوحة التحكم بدون البوت للاختبار والتطوير
 */

const express = require('express');
const mongoose = require('mongoose');
const config = require('./config');

console.log('🚀 بدء تشغيل لوحة التحكم (وضع الاختبار)...');

// محاكاة البوت للاختبار
const mockBot = {
    user: {
        id: '1362339926535831553',
        username: 'CS Bot',
        discriminator: '0000',
        avatar: null
    },
    guilds: {
        cache: new Map([
            ['123456789', {
                id: '123456789',
                name: 'سيرفر الاختبار الأول',
                icon: null,
                memberCount: 150,
                channels: { cache: { size: 25 } },
                roles: { cache: { size: 12 } },
                ownerId: '987654321',
                joinedAt: new Date(),
                features: [],
                premiumTier: 1,
                premiumSubscriptionCount: 5,
                members: {
                    fetch: async (userId) => {
                        if (userId === 'test_user_id') {
                            return {
                                permissions: {
                                    has: (permission) => true // للاختبار
                                }
                            };
                        }
                        throw new Error('Member not found');
                    }
                }
            }],
            ['987654321', {
                id: '987654321',
                name: 'مجتمع المطورين',
                icon: null,
                memberCount: 500,
                channels: { cache: { size: 40 } },
                roles: { cache: { size: 20 } },
                ownerId: 'test_user_id',
                joinedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                features: ['VERIFIED'],
                premiumTier: 2,
                premiumSubscriptionCount: 15,
                members: {
                    fetch: async (userId) => {
                        if (userId === 'test_user_id') {
                            return {
                                permissions: {
                                    has: (permission) => true
                                }
                            };
                        }
                        throw new Error('Member not found');
                    }
                }
            }]
        ]),
        size: 2
    },
    uptime: Date.now() - (24 * 60 * 60 * 1000), // 24 ساعة
    ws: { ping: 45 },
    stats: {
        commandsExecuted: 1234,
        messagesProcessed: 5678,
        commands: {
            'help': 456,
            'ping': 234,
            'music': 123,
            'fun': 89
        }
    }
};

// الاتصال بقاعدة البيانات
mongoose.connect(config.mongoURI)
    .then(() => {
        console.log('✅ تم الاتصال بقاعدة البيانات MongoDB');
    })
    .catch(err => {
        console.error('❌ فشل الاتصال بقاعدة البيانات:', err.message);
        console.log('⚠️ سيتم تشغيل لوحة التحكم بدون قاعدة البيانات');
    });

// تحميل لوحة التحكم
const dashboard = require('./dashboard/app');
dashboard.locals.config = config;
dashboard.locals.bot = mockBot;

// تشغيل لوحة التحكم
const PORT = process.env.PORT || 3000;
dashboard.listen(PORT, () => {
    console.log('✅ تم تشغيل لوحة التحكم على المنفذ', PORT);
    console.log('🌐 يمكنك الوصول إلى لوحة التحكم من خلال: http://localhost:' + PORT);
    console.log('');
    console.log('📝 ملاحظة: هذا وضع اختبار - البوت غير متصل');
    console.log('💡 لتشغيل البوت الكامل، استخدم: node index.js');
    console.log('');
    console.log('🔧 للاختبار:');
    console.log('   1. اذهب إلى http://localhost:3000');
    console.log('   2. سجل دخول بـ Discord');
    console.log('   3. اختبر لوحة التحكم');
});

// معالجة الأخطاء
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ خطأ غير معالج:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
    process.exit(1);
});

// إيقاف نظيف
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف لوحة التحكم...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف لوحة التحكم...');
    process.exit(0);
});
