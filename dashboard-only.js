/**
 * CS Bot - تشغيل لوحة التحكم فقط (للاختبار)
 */

console.log('🚀 بدء تشغيل لوحة التحكم...');

try {
    // تحميل المتغيرات البيئية
    require('dotenv').config();
    console.log('✅ تم تحميل المتغيرات البيئية');
    
    // تحميل الإعدادات
    const config = require('./config');
    console.log('✅ تم تحميل الإعدادات');
    
    // تحميل لوحة التحكم
    const dashboard = require('./dashboard/app');
    dashboard.locals.config = config;
    
    // تشغيل لوحة التحكم
    const server = dashboard.listen(config.dashboard.port, () => {
        console.log(`✅ تم تشغيل لوحة التحكم على المنفذ ${config.dashboard.port}`);
        console.log(`🌐 يمكنك الوصول إلى لوحة التحكم من خلال: ${config.dashboard.domain}`);
        console.log('📝 ملاحظة: البوت غير متصل، لوحة التحكم فقط للاختبار');
    });
    
    // معالجة أخطاء الخادم
    server.on('error', (error) => {
        console.error('❌ خطأ في خادم لوحة التحكم:', error);
    });
    
} catch (error) {
    console.error('❌ فشل في تشغيل لوحة التحكم:', error);
    console.error('📍 تفاصيل الخطأ:', error.stack);
}
